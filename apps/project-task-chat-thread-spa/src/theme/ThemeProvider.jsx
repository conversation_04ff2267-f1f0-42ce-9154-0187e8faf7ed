import React, { useState } from "react";
import { StylesProvider } from "@mui/styles";
import { ThemeProvider } from "@mui/material/styles";
import { themeCreator } from "./base";

// import { CacheProvider } from '@emotion/react';
// import createCache from '@emotion/cache';
// import stylisRTLPlugin from 'stylis-plugin-rtl';

// const cacheRtl = createCache({
//   key: 'bloom-ui',
// prepend: true,
//
//   stylisPlugins: [stylisRTLPlugin]
// });

export const ThemeContext = React.createContext();

const ThemeProviderWrapper = function ({ children }) {
  const curThemeName =
    localStorage.getItem("ept-chat-spa-theme") || "PureLightTheme";
  const [themeName, _setThemeName] = useState(curThemeName);
  const theme = themeCreator(themeName);

  const setThemeName = (themeName) => {
    localStorage.setItem("ept-chat-spa-theme", themeName);
    _setThemeName(themeName);
  };

  // useEffect(() => {
  //   if (getPreferredColorScheme() === "dark") {
  //     setThemeName("GreenFieldsTheme");
  //   } else {
  //     setThemeName("PureLightTheme");
  //   }
  // }, []);

  return (
    <StylesProvider injectFirst>
      {/* <CacheProvider value={cacheRtl}> */}
      <ThemeContext.Provider value={setThemeName}>
        <ThemeProvider theme={theme}>{children}</ThemeProvider>
      </ThemeContext.Provider>
      {/* </CacheProvider> */}
    </StylesProvider>
  );
};

// eslint-disable-next-line
function getPreferredColorScheme() {
  if (window.matchMedia) {
    if (window.matchMedia("(prefers-color-scheme: dark)").matches) {
      return "dark";
    } else {
      return "light";
    }
  }
  return "light";
}
export default ThemeProviderWrapper;
