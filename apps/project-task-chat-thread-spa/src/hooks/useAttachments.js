import { useEffect, useState } from "react";
import useS<PERSON> from "swr";
import axios from "axios";
import Cookies from "js-cookie";

const isDev = import.meta.env.MODE === "development";
const fetcher = (url) =>
  axios
    .get(url, {
      headers: {
        "Content-Type": "application/json",
      },
    })
    .then((res) => res.data);

const comsUrl =
  import.meta.env.MODE === "development"
    ? import.meta.env.VITE_EXTERNAL_SUITELET_URL
    : import.meta.env.VITE_SUITELET_URL;
let params = new URLSearchParams(document.location.search);

let taskIdFromUrl = params.get("taskId");
let taskIdFromCookie = Cookies.get("ept-taskChatId");
let taskId;

if (taskIdFromUrl) {
  taskId = taskIdFromUrl;
  Cookies.set("ept-taskChatId", taskIdFromUrl, { expires: 7 });
} else if (taskIdFromCookie) {
  taskId = taskIdFromCookie;
} else {
  taskId = import.meta.env.DEV ? 129 : null;
}

const commentsUrlAssembled = `${comsUrl}&action=getAttachments&taskId=${taskId}`;

export const useAttachments = () => {
  const { data, error, mutate } = useSWR(commentsUrlAssembled, fetcher);
  const [attachments, setAttachments] = useState([]);

  useEffect(() => {
    if (data) {
      isDev && console.log("Attachment data gathered: ", data);
      setAttachments(data);
    } else if (error) {
      console.error("Error fetching attachments: ", error);
    }
  }, [data, error]);

  return { attachments, setAttachments, error, attachmentsMutate: mutate };
};
