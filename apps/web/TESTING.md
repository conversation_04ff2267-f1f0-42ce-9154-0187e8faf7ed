# Testing Guide

This document provides comprehensive information about the testing setup and practices for the Exhibit House React application.

## Overview

Our testing strategy follows a three-tier approach:

1. **Unit Tests** - Test individual components, hooks, and utilities in isolation
2. **Integration Tests** - Test component interactions and API integrations
3. **End-to-End (E2E) Tests** - Test complete user workflows in the browser

## Testing Stack

- **Jest** - Test runner and assertion library with built-in mocking
- **React Testing Library** - Component testing utilities
- **Cypress** - E2E and component testing framework
- **Jest Mock Functions** - API mocking using Jest's `jest.mock()` functionality
- **@testing-library/jest-dom** - Custom Jest matchers for DOM testing

## Quick Start

### Running All Tests
```bash
npm run test:all
```

### Running Specific Test Types
```bash
# Unit tests only
npm run test:unit

# Integration tests only
npm run test:integration

# E2E tests (headless)
npm run test:e2e

# E2E tests (headed - see browser)
npm run test:e2e:headed

# Open Cypress Test Runner
npm run test:e2e:open

# Component tests with Cypress
npm run test:component

# Tests with coverage report
npm run test:coverage

# Watch mode for development
npm run test:watch
```

### Using the Test Runner
```bash
# Interactive test runner with options
npm run test:runner

# Run specific test type
npm run test:runner unit
npm run test:runner e2e
npm run test:runner coverage

# Get help
npm run test:runner --help
```

## Test Structure

```
src/
├── __tests__/
│   ├── setup/
│   │   ├── test-utils.js          # Custom render functions and utilities
│   │   └── axios-mock.js          # Jest-based axios mocking system
│   ├── unit/
│   │   ├── components/            # Component unit tests
│   │   ├── hooks/                 # Hook unit tests
│   │   └── utils/                 # Utility function tests
│   └── integration/
│       ├── authentication.test.js # Auth flow integration tests
│       └── project-management.test.js # Project CRUD integration tests
├── components/
│   └── [ComponentName]/
│       ├── index.js
│       └── [ComponentName].test.js # Co-located component tests
└── utils/
    └── __tests__/                 # Utility function tests

cypress/
├── e2e/
│   ├── authentication.cy.js      # E2E auth tests
│   └── project-management.cy.js  # E2E project tests
└── support/
    ├── e2e.js                     # E2E support and commands
    └── component.js               # Component test support
```

## Writing Tests

### Unit Tests

Unit tests should test components, hooks, and utilities in isolation:

```javascript
import { render, screen } from '../setup/test-utils';
import MyComponent from '../../components/MyComponent';

describe('MyComponent', () => {
  it('renders with correct text', () => {
    render(<MyComponent text="Hello World" />);
    expect(screen.getByText('Hello World')).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const handleClick = jest.fn();
    const user = userEvent.setup();
    
    render(<MyComponent onClick={handleClick} />);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Integration Tests

Integration tests should test component interactions and API calls:

```javascript
import { render, screen, waitFor } from '../setup/test-utils';
import { mockGetSuccess, mockGetError, mockedAxios } from '../setup/axios-mock';
import ProjectList from '../../components/ProjectList';

describe('ProjectList Integration', () => {
  it('loads and displays projects', async () => {
    render(<ProjectList />);

    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
    });
  });

  it('handles API errors', async () => {
    mockGetError(500, 'Server error');

    render(<ProjectList />);

    await waitFor(() => {
      expect(screen.getByText(/error loading projects/i)).toBeInTheDocument();
    });
  });

  it('handles successful API responses', async () => {
    mockGetSuccess([
      { id: 1, name: 'Test Project 1' },
      { id: 2, name: 'Test Project 2' },
    ]);

    render(<ProjectList />);

    await waitFor(() => {
      expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      expect(screen.getByText('Test Project 2')).toBeInTheDocument();
    });
  });

  it('handles dynamic responses', async () => {
    mockedAxios.get.mockImplementation((url) => {
      if (url.includes('projects')) {
        return Promise.resolve({
          data: [{ id: 1, name: 'Dynamic Project' }],
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        });
      }
      return Promise.reject(new Error('Not found'));
    });

    render(<ProjectList />);

    await waitFor(() => {
      expect(screen.getByText('Dynamic Project')).toBeInTheDocument();
    });
  });
});
```

### E2E Tests

E2E tests should test complete user workflows:

```javascript
describe('Project Management E2E', () => {
  beforeEach(() => {
    cy.login(); // Custom command for authentication
  });

  it('creates a new project', () => {
    cy.visit('/projects');
    cy.getByTestId('create-project-button').click();
    
    cy.fillForm({
      'project-name': 'E2E Test Project',
      'project-description': 'Created by E2E test',
    });
    
    cy.getByTestId('submit-button').click();
    cy.contains('Project created successfully').should('be.visible');
  });
});
```

## Testing Best Practices

### General Guidelines

1. **Test Behavior, Not Implementation** - Focus on what the user sees and does
2. **Use Descriptive Test Names** - Test names should clearly describe what is being tested
3. **Follow AAA Pattern** - Arrange, Act, Assert
4. **Keep Tests Independent** - Each test should be able to run in isolation
5. **Use Data Test IDs** - Add `data-testid` attributes for reliable element selection

### Component Testing

1. **Test User Interactions** - Click, type, hover, etc.
2. **Test Different States** - Loading, error, success states
3. **Test Props and Callbacks** - Ensure props are handled correctly
4. **Test Accessibility** - Use semantic queries when possible

### API Testing

1. **Mock External Dependencies** - Use Jest's `jest.mock('axios')` for API mocking
2. **Test Error Scenarios** - Network errors, 404s, 500s, etc.
3. **Test Loading States** - Ensure loading indicators work
4. **Test Data Transformation** - Verify data is processed correctly

#### Jest Axios Mock Examples

```javascript
import { mockGetSuccess, mockPostSuccess, mockGetError, mockedAxios } from '../setup/axios-mock';

// Mock successful GET request
mockGetSuccess([{ id: 1, name: 'John Doe' }]);

// Mock successful POST request
mockPostSuccess({ id: 2, name: 'Jane Doe' }, 201);

// Mock error responses
mockGetError(500, 'Server Error');
mockPostError(400, 'Validation Error');

// Mock with dynamic responses
mockedAxios.post.mockImplementation((url, data) => {
  return Promise.resolve({
    data: { id: 2, ...data },
    status: 201,
    statusText: 'Created',
    headers: {},
    config: {},
  });
});

// Mock conditional responses
mockedAxios.get.mockImplementation((url) => {
  if (url.includes('users')) {
    return Promise.resolve({
      data: [{ id: 1, name: 'User' }],
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  }
  return Promise.reject(new Error('Not found'));
});

// Mock specific method calls
mockedAxios.get.mockResolvedValue({
  data: { message: 'Success' },
  status: 200,
  statusText: 'OK',
  headers: {},
  config: {},
});

mockedAxios.post.mockRejectedValue(new Error('Network Error'));
```

#### NetSuite Integration Testing

The mock system includes comprehensive NetSuite deployment URL support:

```javascript
import { mockDeploymentUrls } from '../setup/axios-mock';

// Test NetSuite deployment URLs
const response = await axios.get('/app/site/hosting/scriptlet.nl');
expect(response.data.success).toBe(true);
expect(response.data.core_account_url).toContain('tstdrv2149044.app.netsuite.com');

// Test specific NetSuite endpoints
expect(response.data.get_project_form_data_internal).toContain('script=1188');
expect(response.data.get_item_order_history_external).toContain('extforms.netsuite.com');

// Test NetSuite scriptlet endpoints
const projectResponse = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1188');
expect(projectResponse.data.formFields).toBeDefined();

const orderResponse = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1120');
expect(orderResponse.data.rows).toBeDefined();
```

### E2E Testing

1. **Test Critical User Paths** - Focus on the most important workflows
2. **Use Page Object Pattern** - Organize selectors and actions
3. **Test Across Browsers** - Ensure cross-browser compatibility
4. **Test Responsive Design** - Test on different viewport sizes

## Configuration

### Jest Configuration

Jest is configured in `jest.config.js` with:
- Custom test environment setup
- Module name mapping for imports
- Coverage thresholds (70% minimum)
- Transform patterns for modern JavaScript

### Cypress Configuration

Cypress is configured in `cypress.config.js` with:
- E2E and component testing setup
- Custom commands and utilities
- Code coverage integration
- Retry configuration for flaky tests

### Jest Axios Mock Configuration

Jest's built-in mocking is used to mock axios calls during tests:
- Mock setup defined in `src/__tests__/setup/axios-mock.js`
- Uses `jest.mock('axios')` at the module level
- Automatic setup in `setupTests.js`
- Supports `mockResolvedValue()`, `mockRejectedValue()`, and `mockImplementation()`
- Provides helper functions for common mocking scenarios
- Includes comprehensive NetSuite deployment URL mocking with realistic endpoints

## Coverage Reports

Coverage reports are generated in the `coverage/` directory and include:
- Line coverage
- Function coverage
- Branch coverage
- Statement coverage

View coverage reports by opening `coverage/lcov-report/index.html` in your browser.

## Continuous Integration

Tests are designed to run in CI environments with:
- Headless browser support
- Parallel test execution
- Artifact collection (screenshots, videos)
- Coverage reporting

## Troubleshooting

### Common Issues

1. **Tests timing out** - Increase timeout values or check for infinite loops
2. **Flaky tests** - Add proper waits and assertions
3. **Memory leaks** - Ensure proper cleanup in test teardown
4. **Mock issues** - Verify Jest axios mocks are correctly configured using `jest.clearAllMocks()`

### Debug Tips

1. **Use `screen.debug()`** - Print current DOM state
2. **Add `cy.pause()`** - Pause Cypress tests for debugging
3. **Check browser console** - Look for JavaScript errors
4. **Use `--verbose` flag** - Get more detailed test output

## Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure all tests pass
3. Maintain or improve coverage
4. Update this documentation if needed

For questions or issues with testing, please refer to the team documentation or create an issue in the project repository.
