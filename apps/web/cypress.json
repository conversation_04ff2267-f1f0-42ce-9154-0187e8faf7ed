{"baseUrl": "http://localhost:3000", "viewportWidth": 1280, "viewportHeight": 720, "video": true, "screenshotOnRunFailure": true, "defaultCommandTimeout": 10000, "requestTimeout": 10000, "responseTimeout": 10000, "pageLoadTimeout": 30000, "coverage": true, "retries": {"runMode": 2, "openMode": 0}, "e2e": {"specPattern": "cypress/e2e/**/*.cy.{js,jsx,ts,tsx}", "supportFile": "cypress/support/e2e.js", "setupNodeEvents": "cypress/plugins/index.js"}, "component": {"testFiles": "__tests__/*.test.{js,ts,jsx,tsx}", "componentFolder": "src", "devServer": {"framework": "create-react-app", "bundler": "webpack"}}, "env": {"REACT_APP_API_BASE_URL": "http://localhost:3000", "coverage": true}}