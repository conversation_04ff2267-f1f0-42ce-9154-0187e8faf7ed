import React, { createContext, useEffect, useReducer, useState } from 'react';
import useSWR from 'swr';
import useSettings from '../hooks/useSettings';
import useHoistedUrls from '../hooks/useHoistedUrls';

// eslint-disable-next-line camelcase
const user_suitelet =
  process.env.NODE_ENV === 'development'
    ? process.env.REACT_APP_GET_USER_SUITELET
    : window.env.REACT_APP_GET_USER_SUITELET;

const userReducer = (state, action) => {
  switch (action.type) {
    case 'ADD_PROJECT':
      return { ...state, project: action };
    case 'ADD_INVENTORY':
      return { ...state, inventory: action };
    case 'UPDATE_INVENTORY':
      return { ...state, inventory: action };
    case 'ADD_INVAVAILDATES':
      return { ...state, invAvailDates: action };
    case 'ADD_USER':
      return { ...state, user: action };
    case 'ADD_CATEGORIES':
      return { ...state, categories: action };
    default:
      return state;
  }
};

export const UserContext = createContext();

const UserProvider = (props) => {
  const [user, setUser] = useState({});
  const [inventoryDataFiltered, setInventoryDataFiltered] = useState(null);
  const [tableLoading, setTableLoading] = useState(true);
  const [availInvLoading, setAvailInvLoading] = useState(false);
  const [currentProject, setCurrentProject] = useState('');
  const [currentStep, setCurrentStep] = useState(0); // TODO: remove due to stepper deletion
  const [settings] = useSettings();
  const [checkoutConfig, setCheckoutConfig] = useState(() => {
    let checkoutObj = {
      rentals: false,
      inventory: false
    };

    const localData = localStorage.getItem('checkoutConfig');
    return localData ? JSON.parse(localData) : checkoutObj;
  });

  const { getDeploymentUrl } = useHoistedUrls();

  // eslint-disable-next-line camelcase
  const {
    data: userData,
    error,
    mutate: userMutate,
    isValidating: nsDataValidating
    // eslint-disable-next-line camelcase
  } = useSWR(() => `${user_suitelet}`);

  // state for globally available address modal

  const [userState, dispatch] = useReducer(userReducer, [], () => {
    const localData = localStorage.getItem('user');
    return localData ? JSON.parse(localData) : [];
  });

  const getUser = () => {
    return user && Object.keys(user).length !== 0 ? user : undefined;
  };

  const username =
    process.env.NODE_ENV === 'development'
      ? 'Test User'
      : getUser()
      ? user?.name
      : 'Loading...';

  const logout = () => {
    window.open(
      process.env.NODE_ENV === 'development'
        ? 'https://tstdrv2436112.app.netsuite.com/pages/nllogoutnoback.jsp'
        : '/pages/nllogoutnoback.jsp'
    );
  };

  const setProject = (project) => {
    console.log('⚡ Setting project:', project);
    setCurrentProject(project);
    sessionStorage.setItem('project', JSON.stringify(project));
  };

  useEffect(() => {
    console.log('User:', userData);
  }, [userData]);

  useEffect(() => {
    console.log('Get User SWR:', userData);
    if (userData && userData.data?.profile) {
      let logoUrl = ``;

      if (settings) {
        if (
          userData.data.profile?.record?.fields
            ?.custentity_ng_eh_customer_logo_url
        ) {
          logoUrl = `${getDeploymentUrl.core_account_url}${userData.data.profile?.record?.fields?.custentity_ng_eh_customer_logo_url}`;
        } else if (settings?.custrecord_ng_eh_react_logo_image_url) {
          logoUrl = `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_react_logo_image_url}`;
        }
      }

      if (userData.data.profile?.record) {
        let checkoutObj = {
          rentals: false,
          inventory: false
        };

        let { record } = userData.data.profile;
        if (
          settings.custrecord_ng_eh_disable_rental_items === 'F' &&
          record.fields.custentity_ng_eh_enable_ren_checkout_web === 'T'
        ) {
          checkoutObj.rentals = true;
        }
        if (
          settings.custrecord_ng_eh_disable_inventory_items === 'F' &&
          record.fields.custentity_ng_eh_enable_inv_checkout_web === 'T'
        ) {
          checkoutObj.inventory = true;
        }

        console.log('Web checkout config:', checkoutObj);

        localStorage.setItem('checkoutConfig', JSON.stringify(checkoutObj));

        setCheckoutConfig(checkoutObj);
      }

      let updatedUserData = {
        ...userData?.data,
        avatar: logoUrl
      };

      setUser(updatedUserData);
      setTableLoading(false);
      localStorage.setItem('user', JSON.stringify(updatedUserData));
    } else if (typeof userData === 'string') {
      setTimeout(() => {
        // Fix page init after signout
        // https://tstdrv2149044.app.netsuite.com/app/login/secure/privatelogin.nl?c=TSTDRV2149044&whence=
        window.location.reload();
      }, 2000);
    }
    if (error) {
      console.error('User data error:', error);
    }
  }, [userData, settings]);

  // Project and task data models have been moved to /Data Model.md for better maintainability

  useEffect(() => {
    const localProjectData = sessionStorage.getItem('project');
    if (user && !currentProject) {
      if (localProjectData) {
        setCurrentProject(JSON.parse(localProjectData));
      } else {
        setCurrentProject('');
      }
    }
  }, [user]);

  const updateItemAvail = (merge) => {
    console.log(merge);
    dispatch({
      type: 'UPDATE_INVENTORY',
      inventory: merge
    });
  };

  return (
    <UserContext.Provider
      value={{
        user,
        username,
        dispatch,
        userMutate,
        checkoutConfig,
        currentStep,
        tableLoading,
        setCurrentStep,
        updateItemAvail,
        availInvLoading,
        nsDataValidating,
        setAvailInvLoading,
        inventoryDataFiltered,
        setInventoryDataFiltered,
        setProject,
        currentProject,
        logout
      }}
    >
      {props.children}
    </UserContext.Provider>
  );
};

export default UserProvider;
