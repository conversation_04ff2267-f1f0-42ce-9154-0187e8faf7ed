import React, { createContext } from 'react';
import { useSnackbar } from 'notistack';
import { useTranslation } from 'react-i18next';
import { useCartStore } from '../store/cartStore';
import { shallow } from 'zustand/shallow';

export const CartContext = createContext();

const CartProvider = (props) => {
  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const [cart, setCart, removeCartItem, resetCart] = useCartStore(
    (state) => [
      state.cart,
      state.setCart,
      state.removeCartItem,
      state.resetCart
    ],
    shallow
  );

  return (
    <CartContext.Provider
      value={{
        cart,
        setCart,
        removeCartItem,
        resetCart
      }}
    >
      {props.children}
    </CartContext.Provider>
  );
};

export default CartProvider;
