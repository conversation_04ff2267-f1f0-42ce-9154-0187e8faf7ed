import React, { useEffect, useState } from 'react';

export const DimensionsContext = React.createContext();

export const DimensionsProvider = (props) => {
  const [dimensions, setDimensions] = useState({});
  useEffect(() => {
    setDimensions({
      height: window.innerHeight,
      width: window.innerWidth
    });
  }, []);

  useEffect(() => {
    function handleResize() {
      // console.log('resized to: ', window.innerWidth, 'x', window.innerHeight)
      setDimensions({
        height: window.innerHeight,
        width: window.innerWidth
      });
    }

    // console.log('Dim:', dimensions)

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  });

  return (
    <DimensionsContext.Provider value={[dimensions, setDimensions]}>
      {props.children}
    </DimensionsContext.Provider>
  );
};
