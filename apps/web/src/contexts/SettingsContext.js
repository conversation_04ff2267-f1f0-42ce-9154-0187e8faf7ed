import React, { useEffect, useState } from 'react';
import { mutate } from 'swr';
import { nsRequest } from '../utils/customFunctions';
import Cookies from 'js-cookie';

export const SettingsContext = React.createContext();

const getSettingsUrl =
  process.env.NODE_ENV === 'development'
    ? process.env.REACT_APP_RESTLET_SETTINGS
    : window.env.REACT_APP_SUITELET_SETTINGS;

export const SettingsProvider = (props) => {
  const [settings, setSettings] = useState(() => {
    let settingsLocalItem = localStorage.getItem('settings');
    let localSettingsData = settingsLocalItem
      ? JSON.parse(settingsLocalItem)
      : {};
    if (settingsLocalItem) {
      return localSettingsData;
    }
    return localSettingsData;
  });
  const [retryCount, setRetryCount] = useState(0);

  // eslint-disable-next-line no-unused-vars
  const getSettings = () => {
    return Object.keys(settings).length !== 0 ? settings : undefined;
  };

  useEffect(() => {
    let active = true;

    if (active && settings) {
      localStorage.setItem('settings', JSON.stringify(settings));
    }

    return () => {
      active = false;
    };
  }, [settings]);

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    let active = true;

    if (active) {
      const runFetchRetry = (numOfTries, delay) => {
        if (active) {
          while (
            retryCount !== numOfTries &&
            Object.keys(settings).length !== 0
          ) {
            let tries = retryCount;
            setRetryCount((tries += 1));
            setTimeout(() => {
              mutate(
                `${getSettingsUrl}`,
                nsRequest('GET', `${getSettingsUrl}`, (err, res, body) => {
                  if (err) {
                    console.error(
                      'There was an error getting the settings.',
                      err
                    );
                    return err;
                  }
                  console.log('Setttings Request retry hit: ', body);
                  setSettings(JSON.parse(body));
                  return res;
                })
                  .catch((err) => console.error(err))
                  .then((r) => r)
              );
            }, delay);
          }
        }
      };

      const prefetch = () => {
        if (active) {
          mutate(
            `${getSettingsUrl}`,
            nsRequest('GET', `${getSettingsUrl}`, (err, res, body) => {
              if (err) {
                console.error('There was an error getting the settings.', err);
                return err;
              }
              let resJson = {};
              if (body) resJson = JSON.parse(body);
              console.log('Settings Request hit: ', body);
              Cookies.set(
                'secondaryColor',
                resJson.custrecord_ng_eh_secondary_web_color,
                { path: '/', expires: 7 }
              );
              Cookies.set(
                'primaryColor',
                resJson.custrecord_ng_eh_primary_web_color,
                { path: '/', expires: 7 }
              );
              Cookies.set(
                'themeMode',
                resJson.custrecord_ng_eh_web_dark_brand === 'T'
                  ? 'dark'
                  : 'light',
                { path: '/', expires: 7 }
              );

              if (
                JSON.stringify(resJson) !== localStorage.getItem('settings')
              ) {
                window.location.reload();
              }

              setSettings(resJson);
              return res;
            }).catch((err) => {
              console.error('Error getting settings: ', err);
              if (err) {
                runFetchRetry(5, 5000);
              }
            }),
            active
          );
          // the second parameter is a Promise
          // SWR will use the result when it resolves
        }
      };

      prefetch();

      return () => (active = false);
    }
  }, []);

  /*	useEffect(() => {
		if (settings !== prevSettings) {

		}
	}, [settings]) */

  return (
    <SettingsContext.Provider value={[settings, setSettings]}>
      {props.children}
    </SettingsContext.Provider>
  );
};
