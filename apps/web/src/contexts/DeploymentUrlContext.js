import React, { useEffect, useState } from 'react';
import { mutate } from 'swr';
import { nsRequest } from '../utils/customFunctions';

export const DeploymentUrlContext = React.createContext();

export const DeploymentUrlsProvider = (props) => {
  const [getDeploymentUrl, setDeploymentUrls] = useState({});
  const [retryCount, setRetryCount] = useState(0);

  const getHoistedUrls =
    process.env.NODE_ENV === 'development'
      ? process.env.REACT_APP_NS_HOISTED_URL_SUITELET
      : window.env.REACT_APP_NS_HOISTED_URL_SUITELET;

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    let active = true;

    if (active) {
      const runFetchRetry = (numOfTries, delay) => {
        if (active) {
          while (
            retryCount !== numOfTries &&
            Object.keys(getDeploymentUrl).length !== 0
          ) {
            let tries = retryCount;
            setRetryCount((tries += 1));
            setTimeout(() => {
              mutate(
                `${getHoistedUrls}`,
                nsRequest(
                  'GET',
                  `${getHoistedUrls}&env=${process.env.NODE_ENV}`,
                  (err, res, body) => {
                    if (err) {
                      console.error(
                        'There was an error getting the settings.',
                        err
                      );
                      return err;
                    }
                    console.log('Deployment Request retry hit: ', body);
                    setDeploymentUrls(JSON.parse(body));
                    return res;
                  },
                  process.env.NODE_ENV !== 'development'
                )
                  .catch((err) => console.error(err))
                  .then((r) => r)
              );
            }, delay);
          }
        }
      };

      const prefetch = () => {
        if (active) {
          mutate(
            `${getHoistedUrls}`,
            nsRequest(
              'GET',
              `${getHoistedUrls}&env=${process.env.NODE_ENV}`,
              (err, res, body) => {
                if (err) {
                  console.error(
                    'There was an error getting the settings.',
                    err
                  );
                  return err;
                }
                console.log('Deployment Request hit: ', JSON.parse(body));
                console.log('Deployment Response hit: ', res);
                setDeploymentUrls(JSON.parse(body));
                return res;
              },
              process.env.NODE_ENV !== 'development'
            ).catch((err) => {
              console.error('Error getting settings: ', err);
              if (err) {
                runFetchRetry(5, 5000);
              }
            }),
            active
          );
          // the second parameter is a Promise
          // SWR will use the result when it resolves
        }
      };

      prefetch();

      return () => (active = false);
    }
  }, []);

  /*	useEffect(() => {
		if (getDeploymentUrl !== prevDeploymentUrls) {

		}
	}, [getDeploymentUrl]) */

  return (
    <DeploymentUrlContext.Provider
      value={[getDeploymentUrl, setDeploymentUrls]}
    >
      {props.children}
    </DeploymentUrlContext.Provider>
  );
};
