import React, { useState } from 'react';

import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Button,
  ButtonGroup,
  Card,
  Checkbox,
  CircularProgress,
  Collapse,
  Divider,
  Grid,
  IconButton,
  List,
  ListItem,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  styled,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import { ExpandLess, ExpandMore } from '@mui/icons-material';

const AccordionSummaryWrapper = styled(AccordionSummary)(
  () => `
      &.Mui-expanded {
        min-height: 48px;
      }s

      .MuiAccordionSummary-content.Mui-expanded {
        margin: 12px 0;
      }
  `
);

const ListItemWrapper = styled(ListItemButton)(
  () => `
      &.MuiButtonBase-root {
        border-radius: 0;
      }
  `
);

function Sidebar({
  categories,
  handleCategoryToggle,
  handleParentCategoryToggle,
  setActiveCategoryFilters,
  activeCategoryFilters,
  eventFilters
}) {
  const { t } = useTranslation();
  const [expanded, setExpanded] = useState([]);
  const [expandedEvents, setExpandedEvents] = useState([]);
  // const categories = [
  //   {
  //     id: 1,
  //     name: 'Toys & Hobbies',
  //     value: 'toys_hobbies'
  //   },
  //   {
  //     id: 2,
  //     name: 'Office Supplies',
  //     value: 'office_supplies'
  //   },
  //   {
  //     id: 3,
  //     name: 'Electronics',
  //     value: 'electronics'
  //   },
  //   {
  //     id: 4,
  //     name: 'Video Games',
  //     value: 'video_games'
  //   }
  // ];

  const handleCategoryExpandToggle = (value) => () => {
    const currentIndex = expanded.indexOf(value);
    const newChecked = [...expanded];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setExpanded(newChecked);
  };

  const handleEventExpandToggle = (value) => () => {
    const currentIndex = expandedEvents.indexOf(value);
    const newChecked = [...expandedEvents];

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);
    }

    setExpandedEvents(newChecked);
  };

  const handleToggleAllCategories = (toggle) => () => {
    let categoryKeys = [];
    if (categories) {
      categoryKeys = categories.map((cat) => cat.key);
    }

    if (toggle) {
      setExpanded(categoryKeys);
    } else {
      setExpanded([]);
    }
  };

  const handleToggleAllEvents = (toggle) => () => {
    let categoryKeys = [];
    if (categories) {
      categoryKeys = eventFilters.map((cat) => cat.key);
    }

    if (toggle) {
      setExpandedEvents(categoryKeys);
    } else {
      setExpandedEvents([]);
    }
  };

  const projectIndeterminate = (value) => {
    // console.log('Values indeter:', value);
    let isIndeterminate;
    let foundFilter = activeCategoryFilters.find(
      (activeFilter) => value.key === activeFilter?.project?.id
    );

    let foundProjectIndex = activeCategoryFilters.findIndex(
      (activeFilter) => value.key === activeFilter.id
    );

    let foundProject = foundProjectIndex !== -1;

    if (foundFilter && foundProject) {
      console.log('Found filter:', foundFilter);
      // If project id is found match up all ids in active filters comparing the categories in the project are the same to the active filters in state
      let activeFoundFilterCategories = foundFilter.project.categories;
      let catIds = activeFoundFilterCategories.map((cat) => cat.key);

      let selectedFilters = activeCategoryFilters.filter((activeFilter) =>
        catIds.includes(activeFilter.id)
      );

      console.log('Filters selected:', selectedFilters);

      if (selectedFilters.length === 0) {
        console.log('Toggle project off');
        let updatedFilters = [...activeCategoryFilters];
        updatedFilters.splice(foundProjectIndex, 1);
        setActiveCategoryFilters(updatedFilters);
      }

      isIndeterminate = selectedFilters.length !== catIds.length;
    }

    return isIndeterminate;
  };

  const projectAllChecked = (value) => {
    // console.log('Values indeter:', value);
    let allExist = false;
    let foundFilter = activeCategoryFilters.find(
      (activeFilter) => value.key === activeFilter?.project?.id
    );

    let foundProject =
      activeCategoryFilters.findIndex(
        (activeFilter) => value.key === activeFilter.id
      ) !== -1;

    if (foundFilter && foundProject) {
      console.log('Found filter:', foundFilter);
      // If project id is found match up all ids in active filters comparing the categories in the project are the same to the active filters in state
      let activeFoundFilterCategories = foundFilter.project.categories;
      activeFoundFilterCategories.forEach((cat) => {
        allExist =
          activeCategoryFilters.findIndex(
            (activeFilter) => cat.key === activeFilter.id
          ) !== -1;
      });
    }

    return allExist;
  };

  return (
    <Card>
      <Divider />
      <Accordion defaultExpanded>
        <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h5">{t('Categories')}</Typography>
        </AccordionSummaryWrapper>
        <AccordionDetails
          sx={{
            p: 0
          }}
        >
          <Grid container sx={{ px: 2 }}>
            <Grid item xs={12} justifyContent="center" display="flex">
              <ButtonGroup
                spacing={1}
                direction="row"
                size="small"
                variant="text"
              >
                <Button size="small" onClick={handleToggleAllCategories(true)}>
                  Expand All
                </Button>
                <Button size="small" onClick={handleToggleAllCategories(false)}>
                  Collapse All
                </Button>
              </ButtonGroup>
            </Grid>
          </Grid>
          <List component="div">
            {categories && Array.isArray(categories) ? (
              categories.length !== 0 ? (
                categories.map((value) => {
                  if (value?.children && value.children.length !== 0) {
                    return (
                      <React.Fragment key={value.key}>
                        <ListItem>
                          <ListItemIcon
                            sx={{
                              minWidth: 32
                            }}
                          >
                            <Checkbox
                              edge="start"
                              checked={
                                activeCategoryFilters.findIndex(
                                  (activeFilter) =>
                                    value.key === activeFilter.id
                                ) !== -1
                              }
                              tabIndex={-1}
                              onClick={handleParentCategoryToggle({
                                id: value.key,
                                label: value.title
                              })}
                            />
                          </ListItemIcon>
                          <ListItemText primary={value.title} />
                          {expanded.indexOf(value.key) !== -1 ? (
                            <IconButton
                              onClick={handleCategoryExpandToggle(value.key)}
                            >
                              <ExpandLess />
                            </IconButton>
                          ) : (
                            <IconButton
                              onClick={handleCategoryExpandToggle(value.key)}
                            >
                              <ExpandMore />
                            </IconButton>
                          )}
                        </ListItem>
                        <Collapse
                          in={expanded.indexOf(value.key) !== -1}
                          timeout="auto"
                          unmountOnExit
                        >
                          <List component="div" disablePadding>
                            {value.children.map((subcategory) => (
                              <ListItemWrapper
                                disablePadding
                                sx={{
                                  pl: 4,
                                  py: 0
                                }}
                                key={value.key}
                                onClick={handleCategoryToggle({
                                  id: subcategory.key,
                                  label: subcategory.title
                                })}
                              >
                                <ListItemIcon
                                  sx={{
                                    minWidth: 32
                                  }}
                                >
                                  <Checkbox
                                    edge="start"
                                    color="secondary"
                                    checked={
                                      activeCategoryFilters.findIndex(
                                        (activeFilter) =>
                                          subcategory.key === activeFilter.id
                                      ) !== -1
                                    }
                                    tabIndex={-1}
                                    disableRipple
                                  />
                                </ListItemIcon>
                                <ListItemText
                                  primary={subcategory.title}
                                  primaryTypographyProps={{ variant: 'body1' }}
                                />
                              </ListItemWrapper>
                            ))}
                          </List>
                        </Collapse>
                      </React.Fragment>
                    );
                  }
                  return (
                    <ListItemWrapper
                      sx={{
                        py: 0,
                        px: 1.8
                      }}
                      key={value.key}
                      onClick={handleCategoryToggle({
                        id: value.nsInternalId,
                        label: value.title
                      })}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 32
                        }}
                      >
                        <Checkbox
                          edge="start"
                          checked={
                            activeCategoryFilters.findIndex(
                              (activeFilter) =>
                                value.nsInternalId === activeFilter.id
                            ) !== -1
                          }
                          tabIndex={-1}
                          disableRipple
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={value.title}
                        primaryTypographyProps={{ variant: 'body1' }}
                      />
                    </ListItemWrapper>
                  );
                })
              ) : (
                <Typography variant="subtitle1">
                  No categories available
                </Typography>
              )
            ) : (
              <Grid container sx={{ px: 2 }}>
                <Grid item xs={12} justifyContent="center" display="flex">
                  <CircularProgress />
                </Grid>
              </Grid>
            )}
          </List>
        </AccordionDetails>
      </Accordion>
      {/* <Divider />
      <Accordion defaultExpanded>
        <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h5">{t('By Event Date Range')}</Typography>
        </AccordionSummaryWrapper>
        <AccordionDetails
          sx={{
            pt: 5
          }}
        >
          <Box px={2}>
            <DateRangePicker
              defaultValue={[new Date(), new Date()]}
              desktopModeMediaQuery="sm"
              InputProps={{
                fullWidth: true
              }}
            />
          </Box>
        </AccordionDetails>
      </Accordion> */}
      <Divider />
      <Accordion defaultExpanded>
        <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
          <Typography variant="h5">{t('By Past Events')}</Typography>
        </AccordionSummaryWrapper>
        <AccordionDetails
          sx={{
            pt: 1,
            px: 0
          }}
        >
          <Grid container sx={{ px: 2 }}>
            <Grid item xs={12} justifyContent="center" display="flex">
              <ButtonGroup
                spacing={1}
                direction="row"
                size="small"
                variant="text"
              >
                <Button size="small" onClick={handleToggleAllEvents(true)}>
                  Expand All
                </Button>
                <Button size="small" onClick={handleToggleAllEvents(false)}>
                  Collapse All
                </Button>
              </ButtonGroup>
            </Grid>
          </Grid>
          <List component="div">
            {eventFilters && Array.isArray(eventFilters) ? (
              eventFilters.length !== 0 ? (
                eventFilters.map((value) => {
                  if (value?.categories && value.categories.length !== 0) {
                    return (
                      <React.Fragment key={value.key}>
                        <ListItem>
                          <ListItemIcon
                            sx={{
                              minWidth: 42
                            }}
                          >
                            <Checkbox
                              edge="start"
                              checked={projectAllChecked(value)}
                              tabIndex={-1}
                              onClick={handleCategoryToggle({
                                id: value.key,
                                label: value.title,
                                project: {
                                  id: value.key,
                                  categories: value.categories
                                }
                              })}
                              indeterminate={projectIndeterminate(value)}
                            />
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography fontSize="smaller">
                                {value.title}
                              </Typography>
                            }
                          />
                          {expandedEvents.indexOf(value.key) !== -1 ? (
                            <IconButton
                              onClick={handleEventExpandToggle(value.key)}
                            >
                              <ExpandLess />
                            </IconButton>
                          ) : (
                            <IconButton
                              onClick={handleEventExpandToggle(value.key)}
                            >
                              <ExpandMore />
                            </IconButton>
                          )}
                        </ListItem>
                        <Collapse
                          in={expandedEvents.indexOf(value.key) !== -1}
                          timeout="auto"
                          unmountOnExit
                        >
                          <List component="div" disablePadding>
                            {value.categories.map((category) => (
                              <ListItemWrapper
                                disablePadding
                                sx={{
                                  pl: 4,
                                  py: 0
                                }}
                                key={value.key}
                                onClick={handleCategoryToggle({
                                  id: category.key,
                                  label: category.title,
                                  project: {
                                    id: value.key
                                  }
                                })}
                              >
                                <ListItemIcon
                                  sx={{
                                    minWidth: 42
                                  }}
                                >
                                  <Checkbox
                                    edge="start"
                                    color="secondary"
                                    checked={
                                      activeCategoryFilters.findIndex(
                                        (activeFilter) =>
                                          category.key === activeFilter.id
                                      ) !== -1
                                    }
                                    tabIndex={-1}
                                    disableRipple
                                  />
                                </ListItemIcon>
                                <ListItemText
                                  primary={category.title}
                                  primaryTypographyProps={{ variant: 'body1' }}
                                />
                              </ListItemWrapper>
                            ))}
                          </List>
                        </Collapse>
                      </React.Fragment>
                    );
                  }
                  return (
                    <ListItemWrapper
                      sx={{
                        py: 0
                      }}
                      key={value.key}
                      onClick={handleCategoryToggle({
                        id: value.id,
                        label: value.title
                      })}
                    >
                      <ListItemIcon
                        sx={{
                          minWidth: 32
                        }}
                      >
                        <Checkbox
                          edge="start"
                          checked={
                            activeCategoryFilters.findIndex(
                              (activeFilter) => value.id === activeFilter.id
                            ) !== -1
                          }
                          tabIndex={-1}
                          disableRipple
                        />
                      </ListItemIcon>
                      <ListItemText
                        primary={value.title}
                        primaryTypographyProps={{ variant: 'body1' }}
                      />
                    </ListItemWrapper>
                  );
                })
              ) : (
                <Typography variant="subtitle1">No events available</Typography>
              )
            ) : (
              <Grid container sx={{ px: 2 }}>
                <Grid item xs={12} justifyContent="center" display="flex">
                  <CircularProgress />
                </Grid>
              </Grid>
            )}
          </List>
        </AccordionDetails>
      </Accordion>
    </Card>
  );
}

export default Sidebar;
