import { useEffect, useState } from 'react';
import { format } from 'date-fns';
import useSWR from 'swr';
import { Helmet } from 'react-helmet-async';
import Footer from 'src/components/Footer';
import Scrollbar from 'src/components/Scrollbar';

import {
  alpha,
  Box,
  Grid,
  IconButton,
  styled,
  SwipeableDrawer,
  Tooltip,
  Typography
} from '@mui/material';

// import useRefMounted from 'src/hooks/useRefMounted';
import { MenuOpenTwoTone } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import { Results } from './Results';
import PageHeader from './PageHeader';
import Sidebar from './Sidebar';
import { usePrevious } from '../../../../../utils/customHooks';
import useAuth from '../../../../../hooks/useAuth';
import useSettings from '../../../../../hooks/useSettings';
import useHoistedUrls from '../../../../../hooks/useHoistedUrls';

const DrawerWrapperMobile = styled(SwipeableDrawer)(
  () => `
    width: 340px;
    flex-shrink: 0;

  & > .MuiPaper-root {
        width: 340px;
        z-index: 3;
  }
`
);

const IconButtonToggle = styled(IconButton)(
  ({ theme }) => `
  width: ${theme.spacing(6)};
  height: ${theme.spacing(6)};
   background-color: ${
     theme.palette.mode === 'dark'
       ? alpha(theme.palette.background.default, 0.7)
       : theme.colors.alpha.white[70]
   };
      :hover {
            background-color: ${
              theme.palette.mode === 'dark'
                ? alpha(theme.palette.background.default, 0.5)
                : theme.colors.alpha.white[50]
            };
      }
`
);

const applyCategoryFilters = (products, query) => {
  return products.filter((product) => {
    let matches = true;

    if (query && Array.isArray(query) && query.length !== 0) {
      const properties = [
        'custitem_ng_eh_item_category',
        'custitem_ng_eh_item_subcategory'
      ];
      let containsQuery = false;

      properties.forEach((property) => {
        query.forEach((categoryObj) => {
          let category = categoryObj.label;
          if (
            product[property]?.title
              .toLowerCase()
              .includes(category.toLowerCase())
          ) {
            containsQuery = true;
          }
        });
      });

      if (!containsQuery) {
        matches = false;
      }
    } else {
      matches = true;
    }

    return matches;
  });
};

function ManagementProductsShop() {
  const theme = useTheme();
  const [settings] = useSettings();
  const { currentProject } = useAuth();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [rentalAvailabilityRange, setRentalAvailabilityRange] = useState([
    null,
    null
  ]);
  const [activeEventFilters, setActiveEventFilters] = useState([]);
  // const isMountedRef = useRefMounted();

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const { getDeploymentUrl } = useHoistedUrls();

  const getItemsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_customer_items_external
      : getDeploymentUrl.get_customer_items_internal;
  const { data, error, isLoading } = useSWR(
    `${getItemsUrl}&isRentals=T&location=${
      currentProject?.shipmentlocation ||
      settings?.custrecord_ng_eh_default_inv_ship_loc
    }&start=${
      rentalAvailabilityRange[0] &&
      format(rentalAvailabilityRange[0], 'MM/dd/yyyy')
    }&end=${
      rentalAvailabilityRange[1] &&
      format(rentalAvailabilityRange[1], 'MM/dd/yyyy')
    }`,
    {
      revalidateOnFocus: false,
      refreshWhenHidden: true
    }
  );
  const [activeCategoryFilters, setActiveWebCategoryFilters] = useState([]);
  const products = data
    ? applyCategoryFilters(data.items, activeCategoryFilters)
    : data?.items;
  const prevCategories = usePrevious(data?.categories);
  const prevEventFilters = usePrevious(data?.eventFilters);

  const handleParentCategoryToggle = (value) => () => {
    console.log('Parent Clicked', value);
    if (data?.categories) {
      let newChecked = [...activeCategoryFilters];

      console.log('newChecked Before', newChecked);

      const categoryFound = activeCategoryFilters.findIndex(
        // -1 if not there, otherwise 0
        (activeFilter) => activeFilter.id === value.id
      );

      console.log('data?.categories', data?.categories);
      console.log('categoryFound', categoryFound);

      const parentCategoryClicked = data?.categories.findIndex(
        // returns index of category in the category array
        (activeFilter) => activeFilter.key === value.id
      );
      let childrenArr = data?.categories[parentCategoryClicked].children;

      console.log('Parent parentCategoryClicked', parentCategoryClicked);

      if (categoryFound === -1) {
        newChecked.push(value);

        console.log('childrenArr', childrenArr);

        childrenArr.forEach((subCategory) => {
          let categoryObj = {
            id: subCategory.key,
            label: subCategory.title
          };

          newChecked.push(categoryObj);
        });

        setActiveWebCategoryFilters(newChecked);
      } else {
        newChecked.splice(categoryFound, 1);

        let childrenRemoved = newChecked.filter((child) => {
          return (
            childrenArr.findIndex((item) => item?.key === child?.id) === -1
          );
        });

        console.log('childrenRemoved', childrenRemoved);
        setActiveWebCategoryFilters(childrenRemoved);
      }

      console.log('newChecked After', newChecked);
    }
  };

  const handleCategoryToggle = (value) => () => {
    const currentIndex = activeCategoryFilters.findIndex(
      (activeFilter) => activeFilter.id === value.id
    );
    const newChecked = [...activeCategoryFilters];

    console.log('Toggle check:', value);

    if (
      value.project &&
      Array.isArray(value.project.categories) &&
      value.project.categories.length !== 0
    ) {
      console.log('Project selected - Selecting all categories');

      if (currentIndex === -1) {
        newChecked.push(value);
        value.project.categories.forEach((category) => {
          let categoryObj = {
            id: category.key,
            label: category.title
          };

          newChecked.push(categoryObj);
        });
      } else {
        newChecked.splice(currentIndex, 1);
        value.project.categories.forEach((category) => {
          const currentCategoryIndex = activeCategoryFilters.findIndex(
            (activeFilter) => activeFilter.id === category.id
          );

          newChecked.splice(currentCategoryIndex, 1);
        });
      }

      setActiveWebCategoryFilters(newChecked);
      return;
    }

    if (currentIndex === -1) {
      newChecked.push(value);
    } else {
      newChecked.splice(currentIndex, 1);

      let parentKey;

      for (const item of data?.categories) {
        if (item.children.length !== 0) {
          for (const child of item.children) {
            if (child.key == value.id) {
              parentKey = item.key;
            }
          }
        }
      }

      if (parentKey) {
        const parentIndex = newChecked.findIndex(
          (activeFilter) => activeFilter.id === parentKey
        );
        if (parentIndex !== -1) {
          newChecked.splice(parentIndex, 1);
        }
      }
    }
    setActiveWebCategoryFilters(newChecked);
  };

  const handleMultiCategoryToggle = (arrayOfValues) => () => {
    let boxesToCheck = arrayOfValues;
    const newChecked = [...activeCategoryFilters];
    if (Array.isArray(boxesToCheck)) {
      boxesToCheck.forEach((category) => {
        const currentIndex = activeCategoryFilters.findIndex(
          (activeFilter) => activeFilter.id === category.id
        );

        if (currentIndex === -1) {
          newChecked.push({
            id: category.id,
            label: category.title
          });
        } else {
          newChecked.splice(currentIndex, 1);
        }
      });
    }

    setActiveWebCategoryFilters(newChecked);
  };

  useEffect(() => {
    let active = true;
    if (active) {
      let today = new Date();
      let dateTwo = new Date();
      dateTwo.setDate(dateTwo.getDate() + 7);
      console.log('TODAY', today);
      console.log('dateTwo', dateTwo);
      if (currentProject) {
        setRentalAvailabilityRange([
          currentProject?.shipdate ? new Date(currentProject.shipdate) : today,
          currentProject?.projreturn
            ? new Date(currentProject.projreturn)
            : dateTwo
        ]);
      } else {
        setRentalAvailabilityRange([today, dateTwo]);
      }
    }

    return () => (active = false);
  }, [currentProject]);

  useEffect(() => {
    console.log('Category Filters active:', activeCategoryFilters);
    console.log('Event Filters active:', activeEventFilters);
  }, [activeCategoryFilters, activeEventFilters]);

  useEffect(() => {
    console.log('Items fetched:', data);
    if (data && !error) {
      if (JSON.stringify(data?.categories) !== JSON.stringify(prevCategories)) {
        setActiveWebCategoryFilters([]);
      }
      if (
        JSON.stringify(data?.eventFilters) !== JSON.stringify(prevEventFilters)
      ) {
        setActiveEventFilters([]);
      }
    }
  }, [data]);

  return (
    <>
      <Helmet>
        <title>Customer Owned Inventory</title>
      </Helmet>

      <Grid
        sx={{
          px: 4
        }}
        container
        direction="row"
        justifyContent="center"
        alignItems="stretch"
        spacing={4}
      >
        <Grid display="flex" alignItems="center" item xs={12}>
          <Box flex={1} mt={3}>
            <PageHeader />
          </Box>
        </Grid>
        <Grid
          item
          xs={12}
          md={3}
          sx={{
            display: { xs: 'none', md: 'none', lg: 'block' }
          }}
        >
          <Sidebar
            categories={data?.categories}
            handleCategoryToggle={handleCategoryToggle}
            handleParentCategoryToggle={handleParentCategoryToggle}
            handleMultiCategoryToggle={handleMultiCategoryToggle}
            activeCategoryFilters={activeCategoryFilters}
            eventFilters={data?.eventFilters}
            setActiveCategoryFilters={(e) => setActiveWebCategoryFilters(e)}
          />
        </Grid>
        <Grid item xs={12} md={9}>
          <Tooltip
            placement="right"
            arrow
            title={<Typography>Toggle category filters</Typography>}
          >
            <IconButtonToggle
              sx={{
                mr: { md: 1, xs: 0 },
                mb: 1,
                display: { lg: 'none', xs: 'flex' }
              }}
              color="primary"
              onClick={handleDrawerToggle}
              size="small"
            >
              <MenuOpenTwoTone />
            </IconButtonToggle>
          </Tooltip>
          <Results
            isLoading={isLoading}
            products={products ?? []}
            categories={data?.categories}
            rentalAvailabilityRange={rentalAvailabilityRange}
            setRentalAvailabilityRange={(e) => setRentalAvailabilityRange(e)}
          />
        </Grid>
      </Grid>
      <DrawerWrapperMobile
        sx={{
          display: { lg: 'none', md: 'flex', sm: 'flex' }
        }}
        variant="temporary"
        anchor={theme.direction === 'rtl' ? 'right' : 'left'}
        open={mobileOpen}
        onClose={handleDrawerToggle}
      >
        <Scrollbar>
          <Sidebar
            categories={data?.categories}
            handleCategoryToggle={handleCategoryToggle}
            handleParentCategoryToggle={handleParentCategoryToggle}
            activeCategoryFilters={activeCategoryFilters}
            eventFilters={data?.eventFilters}
            setActiveCategoryFilters={(e) => setActiveWebCategoryFilters(e)}
          />
        </Scrollbar>
      </DrawerWrapperMobile>
      <Footer />
    </>
  );
}

export default ManagementProductsShop;
