import { useState } from 'react';
import {
  Avatar,
  Box,
  Button,
  Card,
  Grid,
  List,
  ListItem,
  ListItemText,
  styled,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import UpdatePasswordModal from '../../../components/modals/UpdatePasswordModal';

// eslint-disable-next-line no-unused-vars
const ButtonError = styled(Button)(
  ({ theme }) => `
     background: ${theme.colors.error.main};
     color: ${theme.palette.error.contrastText};

     &:hover {
        background: ${theme.colors.error.dark};
     }
    `
);

// eslint-disable-next-line no-unused-vars
const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.success.light};
    width: ${theme.spacing(5)};
    height: ${theme.spacing(5)};
`
);

// eslint-disable-next-line no-unused-vars
const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    width: ${theme.spacing(5)};
    height: ${theme.spacing(5)};
`
);

function SecurityTab() {
  const { t } = useTranslation();
  const [passwordUpdateOpen, setPasswordUpdateWindowOpen] = useState(false);

  return (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Box pb={2}>
          <Typography variant="h3">{t('Security')}</Typography>
          <Typography variant="subtitle2">
            {t('Change your security preferences below')}
          </Typography>
        </Box>
        <Card>
          <List>
            <ListItem
              sx={{
                p: 3
              }}
            >
              <ListItemText
                primaryTypographyProps={{ variant: 'h5', gutterBottom: true }}
                secondaryTypographyProps={{
                  variant: 'subtitle2',
                  lineHeight: 1
                }}
                primary={t('Change Password')}
                secondary={t('You can change your password here')}
              />
              <Button
                size="large"
                variant="outlined"
                onClick={() => setPasswordUpdateWindowOpen(!passwordUpdateOpen)}
              >
                {t('Change password')}
              </Button>
            </ListItem>
          </List>
        </Card>
      </Grid>
      <UpdatePasswordModal
        open={passwordUpdateOpen}
        onClose={(prevState) => setPasswordUpdateWindowOpen(prevState)}
      />
    </Grid>
  );
}

export default SecurityTab;
