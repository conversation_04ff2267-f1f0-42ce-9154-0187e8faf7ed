import {
  <PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>H<PERSON>er,
  Circular<PERSON><PERSON><PERSON>,
  Di<PERSON>r,
  Grid,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { ArrowForwardTwoTone, PlusOneTwoTone } from '@mui/icons-material';

function Addresses({ user, addressBook, setCurrentTab }) {
  const { t } = useTranslation();

  const handleAddressManage = (tab) => {
    setCurrentTab(tab);
    window.scrollTo({
      top: document.body.scrollHeight,
      behavior: 'smooth'
    });
  };

  return (
    <Grid
      container
      direction="row"
      justifyContent="center"
      alignItems="stretch"
      spacing={3}
    >
      <Grid item xs={12} md={6}>
        {user?.profile.record?.sublists ? (
          addressBook.length !== 0 ? (
            addressBook.map((address, i) => {
              if (address.defaultbilling === 'T') {
                return (
                  <Card key={i}>
                    <CardHeader
                      title={t('Default Billing Address')}
                      subheader={addressBook.length + t(' saved addresses')}
                    />
                    <Divider />
                    <Box p={2}>
                      <Box
                        sx={{
                          minHeight: { xs: 0, md: 243 }
                        }}
                        p={2}
                      >
                        <Typography variant="h5">
                          {address.addressee_initialvalue}
                        </Typography>
                        <Typography
                          variant="h5"
                          sx={{
                            py: 1
                          }}
                          fontWeight="normal"
                        >
                          {address.phone_initialvalue}
                        </Typography>
                        <Typography variant="subtitle1">
                          {`${address.addr1_initialvalue}, ${
                            address.addr2_initialvalue
                              ? address.addr2_initialvalue
                              : ''
                          } ${address.city_initialvalue}, ${
                            address.state_initialvalue
                          } ${address.zip_initialvalue}`}
                        </Typography>
                      </Box>
                      <Button
                        fullWidth
                        variant="outlined"
                        endIcon={<ArrowForwardTwoTone />}
                        onClick={() => handleAddressManage('addresses')}
                      >
                        {t('Manage')}
                      </Button>
                    </Box>
                  </Card>
                );
              }
              return null;
            })
          ) : (
            <Card>
              <CardHeader
                title={t('Default Billing Address')}
                subheader={addressBook.length + t(' saved addresses')}
              />
              <Divider />
              <Box p={2}>
                <Typography variant="caption" fontWeight="bold">
                  {t('No Addresses Found')}
                </Typography>
                <Box
                  sx={{
                    minHeight: { xs: 0, md: 243 }
                  }}
                  p={2}
                >
                  <Button
                    fullWidth
                    variant="outlined"
                    endIcon={<PlusOneTwoTone />}
                    onClick={() => handleAddressManage('addresses')}
                  >
                    {t('Add An Address')}
                  </Button>
                </Box>
              </Box>
            </Card>
          )
        ) : (
          <Card>
            <CardHeader title={t('Default Billing Address')} />
            <Divider />
            <Box p={2}>
              <Box
                sx={{
                  minHeight: { xs: 0, md: 243 }
                }}
                p={2}
              >
                <CircularProgress />
              </Box>
            </Box>
          </Card>
        )}
      </Grid>
      <Grid item xs={12} md={6}>
        {user?.profile.record?.sublists ? (
          addressBook.length !== 0 ? (
            addressBook.map((address, i) => {
              if (address.defaultshipping === 'T') {
                return (
                  <Card key={i}>
                    <CardHeader
                      title={t('Default Shipping Address')}
                      subheader={addressBook.length + t(' saved addresses')}
                    />
                    <Divider />
                    <Box p={2}>
                      <Box
                        sx={{
                          minHeight: { xs: 0, md: 243 }
                        }}
                        p={2}
                      >
                        <Typography variant="h5">
                          {address.addressee_initialvalue}
                        </Typography>
                        <Typography
                          variant="h5"
                          sx={{
                            py: 1
                          }}
                          fontWeight="normal"
                        >
                          {address.phone_initialvalue}
                        </Typography>
                        <Typography variant="subtitle1">
                          {`${address.addr1_initialvalue}, ${
                            address.addr2_initialvalue
                              ? address.addr2_initialvalue
                              : ''
                          } ${address.city_initialvalue}, ${
                            address.state_initialvalue
                          } ${address.zip_initialvalue}`}
                        </Typography>
                      </Box>
                      <Button
                        fullWidth
                        variant="outlined"
                        endIcon={<ArrowForwardTwoTone />}
                        onClick={() => handleAddressManage('addresses')}
                      >
                        {t('Manage')}
                      </Button>
                    </Box>
                  </Card>
                );
              }
              return null;
            })
          ) : (
            <Card>
              <CardHeader
                title={t('Default Shipping Address')}
                subheader={addressBook.length + t(' saved addresses')}
              />
              <Divider />
              <Box p={2}>
                <Typography variant="caption" fontWeight="bold">
                  {t('No Addresses Found')}
                </Typography>
                <Box
                  sx={{
                    minHeight: { xs: 0, md: 243 }
                  }}
                  p={2}
                >
                  <Button
                    fullWidth
                    variant="outlined"
                    endIcon={<PlusOneTwoTone />}
                  >
                    {t('Add An Address')}
                  </Button>
                </Box>
              </Box>
            </Card>
          )
        ) : (
          <Card>
            <CardHeader title={t('Default Shipping Address')} />
            <Divider />
            <Box p={2}>
              <Box
                sx={{
                  minHeight: { xs: 0, md: 243 }
                }}
                p={2}
              >
                <CircularProgress />
              </Box>
            </Box>
          </Card>
        )}
      </Grid>
    </Grid>
  );
}

export default Addresses;
