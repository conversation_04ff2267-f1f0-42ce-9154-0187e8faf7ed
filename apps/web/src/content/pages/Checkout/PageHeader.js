import { useTranslation } from 'react-i18next';
import { Link as RouterLink } from 'react-router-dom';

import { Box, Chip, Link, Typography } from '@mui/material';
import { CancelTwoTone } from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import useAuth from '../../../hooks/useAuth';

function PageHeader() {
  const { currentProject, setProject } = useAuth();
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Box
      display="flex"
      alignItems={{ xs: 'stretch', md: 'center' }}
      flexDirection={{ xs: 'column', md: 'row' }}
      justifyContent="space-between"
      sx={{
        padding: 3,
        backgroundColor: `${
          theme.palette.mode === 'dark'
            ? theme.colors.alpha.trueWhite[10]
            : theme.colors.alpha.white[50]
        }`,
        borderRadius: 1
      }}
    >
      <Box display="flex" alignItems="center">
        <Box>
          <Typography
            sx={{
              fontSize: 'unset'
            }}
            variant="h3"
            component="h3"
            gutterBottom
            className="!text-base sm:!text-1xl md:!text-2xl"
          >
            {t('Checkout')}
          </Typography>
          {currentProject ? (
            <>
              <Typography variant="body1">
                Shopping cart set to{' '}
                <span>
                  <Chip
                    color="primary"
                    variant="outlined"
                    deleteIcon={
                      <CancelTwoTone
                        style={{ color: theme.colors.secondary.main }}
                      />
                    }
                    label={
                      <Link
                        color="text.primary"
                        component={RouterLink}
                        to={`/management/projects/single/${currentProject?.internalid}`}
                      >
                        {currentProject?.project}
                      </Link>
                    }
                    onDelete={() => setProject(null)}
                  />
                </span>
              </Typography>
            </>
          ) : (
            <Typography variant="subtitle2">
              No{' '}
              <span>
                <Link component={RouterLink} to="/management/projects/list">
                  project
                </Link>
              </span>{' '}
              has been set shopping cart
            </Typography>
          )}
        </Box>
      </Box>
      <Box mt={{ xs: 3, md: 0 }}>
        {/* <Button
                  sx={{
                    mt: { xs: 2, sm: 0 }
                  }}
                  component={RouterLink}
                  to={`/${
                    location.pathname.split('/')[1]
                  }/management/commerce/products/create`}
                  variant="contained"
                  startIcon={<AddTwoToneIcon fontSize="small" />}
                >
                  {t('Create product')}
                </Button> */}
      </Box>
    </Box>
  );
}

export default PageHeader;
