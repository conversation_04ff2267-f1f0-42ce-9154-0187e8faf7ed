import React from 'react';
import { Link as RouterLink } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import DOMPurify from 'dompurify';
import parse from 'html-react-parser';
import { Box, Button, Divider, Typography } from '@mui/material';
import { KnowledgePersonSvg } from '../../../utils/customSvgs';

const NewsTab = ({ newsItems }) => {
  const { t } = useTranslation();
  const htmlFrom = (htmlString) => {
    const options = {
      replace: (domNode) => {
        /* if (domNode.type === 'tag' && domNode.name === 'img') {
					return null
				} */
        return domNode;
      }
    };
    const cleanHtmlString = DOMPurify.sanitize(htmlString, {
      USE_PROFILES: { html: true }
    });
    const html = parse(cleanHtmlString, options);
    return html;
  };

  return (
    <Box>
      {newsItems.length !== 0 ? (
        <Box
          sx={{
            py: { xs: 3, md: 8, lg: 12 }
          }}
        >
          {newsItems.map((item) => (
            <Box px={3} py={2} key={`content-${item.name}`}>
              {htmlFrom(item.contents)}
              <Divider />
            </Box>
          ))}
        </Box>
      ) : (
        <Box
          sx={{
            py: { xs: 3, md: 8, lg: 12 },
            textAlign: 'center'
          }}
        >
          <KnowledgePersonSvg height={150} width={150} />
          <Typography variant="h2">{t('No news available')}</Typography>
          <Typography
            variant="h4"
            sx={{
              pt: 1,
              pb: 3
            }}
            fontWeight="normal"
            color="text.secondary"
          >
            {t('No news information has been set by your admin')}!
          </Typography>
          <Button
            color="primary"
            variant="outlined"
            component={RouterLink}
            to="/management/projects/list"
            sx={{
              borderWidth: '2px',
              '&:hover': {
                borderWidth: '2px'
              }
            }}
          >
            {t('Go to Projects')}
          </Button>
        </Box>
      )}
    </Box>
  );
};

NewsTab.propTypes = {};

export default NewsTab;
