import { useRef, useState } from 'react';
import {
  Box,
  Button,
  Card,
  CardActions,
  Divider,
  FormControl,
  Grid,
  InputAdornment,
  Link,
  Menu,
  MenuItem,
  OutlinedInput,
  styled,
  TablePagination,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import parse from 'html-react-parser';
import DOMPurify from 'dompurify';
import { useTranslation } from 'react-i18next';
import { useSnackbar } from 'notistack';
import { formatDistance, subDays } from 'date-fns';
import TodayTwoToneIcon from '@mui/icons-material/TodayTwoTone';
import { useNavigate } from 'react-router-dom';
import SearchTwoToneIcon from '@mui/icons-material/SearchTwoTone';
import Text from 'src/components/Text';
import ExpandMoreTwoToneIcon from '@mui/icons-material/ExpandMoreTwoTone';

const OutlinedInputWrapper = styled(OutlinedInput)(
  ({ theme }) => `
    background-color: ${theme.colors.alpha.white[100]};
    padding-right: ${theme.spacing(0.7)}
`
);

// TODO: Finish search query for projects
const applyFilters = (projects, query, filters) => {
  return projects.filter((project) => {
    let matches = true;

    if (query) {
      const properties = ['project'];
      let containsQuery = false;

      properties.forEach((property) => {
        if (project[property].toLowerCase().includes(query.toLowerCase())) {
          containsQuery = true;
        }
      });

      if (filters.status && project.status !== filters.status) {
        matches = false;
      }

      if (!containsQuery) {
        matches = false;
      }
    }

    Object.keys(filters).forEach((key) => {
      const value = filters[key];
      if (
        key === 'company' &&
        value &&
        Array.isArray(value) &&
        value.length !== 0 &&
        value.includes(project[key])
      ) {
        console.log('Filtering Company:', value);
        console.log('Project Company:', project[key]);
        matches = true;
      } else if (value && project[key] !== value) {
        matches = false;
      }
    });

    return matches;
  });
};

const applyPagination = (projects, page, limit) => {
  return projects.slice(page * limit, page * limit + limit);
};

function TaskSearch({ projects }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();

  // eslint-disable-next-line no-unused-vars
  const handleDelete = () => {
    enqueueSnackbar(t('You clicked on delete!'), {
      variant: 'error'
    });
  };

  // eslint-disable-next-line no-unused-vars
  const handleClick = () => {
    enqueueSnackbar(t('You clicked on the chip!'), {
      variant: 'success'
    });
  };

  const periods = [
    {
      value: 'popular',
      text: t('Most popular')
    },
    {
      value: 'recent',
      text: t('Recent tasks')
    },
    {
      value: 'updated',
      text: t('Latest updated tasks')
    },
    {
      value: 'oldest',
      text: t('Oldest tasks first')
    }
  ];

  const actionRef1 = useRef(null);
  const [openPeriod, setOpenMenuPeriod] = useState(false);
  const [period, setPeriod] = useState(periods[0].text);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(3);
  const [query, setQuery] = useState('');
  // eslint-disable-next-line no-unused-vars
  const [filters, setFilters] = useState({
    status: null,
    company: null
  });

  const handlePageChange = (_event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value));
  };

  const handleQueryChange = (event) => {
    event.persist();
    setQuery(event.target.value);
  };

  const htmlFrom = (htmlString) => {
    const options = {
      replace: (domNode) => {
        if (domNode.type === 'tag' && domNode.name === 'img') {
          return null;
        }
        return domNode;
      }
    };
    const cleanHtmlString = DOMPurify.sanitize(htmlString, {
      USE_PROFILES: { html: true }
    });
    const html = parse(cleanHtmlString, options);
    return html;
  };

  const filteredProjects = applyFilters(projects, query, filters);
  const paginatedProjects = applyPagination(filteredProjects, page, limit);

  const navigate = useNavigate();

  return (
    <>
      <FormControl variant="outlined" fullWidth>
        <OutlinedInputWrapper
          type="text"
          placeholder={t('Search terms here...')}
          onChange={handleQueryChange}
          startAdornment={
            <InputAdornment position="start">
              <SearchTwoToneIcon />
            </InputAdornment>
          }
        />
      </FormControl>
      <Box
        py={3}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box>
          <Typography variant="subtitle2">
            {t('Showing')}:{' '}
            <Text color="black">
              <b>{paginatedProjects.length} projects</b>
            </Text>
          </Typography>
        </Box>
        <Tooltip arrow title="Functionality coming soon!">
          <Box display="flex" alignItems="center">
            <Typography
              variant="subtitle2"
              sx={{
                pr: 1
              }}
            >
              {t('Sort by')}:
            </Typography>
            <Button
              disabled
              size="small"
              variant="outlined"
              ref={actionRef1}
              onClick={() => setOpenMenuPeriod(true)}
              endIcon={<ExpandMoreTwoToneIcon fontSize="small" />}
            >
              {period}
            </Button>
            <Menu
              disableScrollLock
              anchorEl={actionRef1.current}
              onClose={() => setOpenMenuPeriod(false)}
              open={openPeriod}
              anchorOrigin={{
                vertical: 'bottom',
                horizontal: 'right'
              }}
              transformOrigin={{
                vertical: 'top',
                horizontal: 'right'
              }}
            >
              {periods.map((_period) => (
                <MenuItem
                  key={_period.value}
                  onClick={() => {
                    setPeriod(_period.text);
                    setOpenMenuPeriod(false);
                  }}
                >
                  {_period.text}
                </MenuItem>
              ))}
            </Menu>
          </Box>
        </Tooltip>
      </Box>
      <Grid container spacing={3}>
        {paginatedProjects.length === 0 ? (
          <>
            <Typography
              sx={{
                py: 10
              }}
              variant="h3"
              fontWeight="normal"
              color="text.secondary"
              align="center"
            >
              {t("We couldn't find any projects matching your search criteria")}
            </Typography>
          </>
        ) : (
          paginatedProjects.map((project) => {
            return (
              <Grid key={project.id} item xs={12} md={4}>
                <Card
                  variant="outlined"
                  sx={{
                    p: 3,
                    background: `${theme.colors.alpha.black[5]}`
                  }}
                >
                  <Box>
                    <Typography
                      display="flex"
                      alignItems="center"
                      variant="subtitle2"
                      gutterBottom
                    >
                      {project.entityid}
                    </Typography>
                  </Box>
                  <Link
                    onClick={() =>
                      navigate(
                        `/management/projects/single/${project.internalid}`
                      )
                    }
                    variant="h3"
                    color="text.primary"
                  >
                    {project.project}
                  </Link>
                  <Typography
                    sx={{
                      pb: 2
                    }}
                    color="text.secondary"
                  >
                    {project.custentity_ng_eh_proj_portal_blurb &&
                      htmlFrom(
                        atob(project.custentity_ng_eh_proj_portal_blurb)
                      )}
                  </Typography>
                  <Button
                    onClick={() =>
                      navigate(
                        `/management/projects/single/${project.internalid}`
                      )
                    }
                    size="small"
                    variant="contained"
                  >
                    {t('View project')}
                  </Button>
                  <Divider
                    sx={{
                      my: 2
                    }}
                  />
                  <CardActions
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Typography
                      display="flex"
                      alignItems="center"
                      variant="subtitle2"
                    >
                      <Tooltip title={project.startdate}>
                        <TodayTwoToneIcon
                          sx={{
                            mr: 1
                          }}
                        />
                      </Tooltip>
                      {formatDistance(
                        subDays(
                          new Date(project.startdate),
                          new Date(project.startdate).getDate() + 1
                        ),
                        new Date(),
                        {
                          addSuffix: true
                        }
                      )}
                    </Typography>
                  </CardActions>
                </Card>
              </Grid>
            );
          })
        )}
      </Grid>
      <Box
        sx={{
          pt: 4
        }}
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <TablePagination
          component="div"
          count={filteredProjects.length}
          onPageChange={handlePageChange}
          onRowsPerPageChange={handleLimitChange}
          page={page}
          rowsPerPage={limit}
          rowsPerPageOptions={[3, 6, 10, 15]}
        />
      </Box>
    </>
  );
}

export default TaskSearch;
