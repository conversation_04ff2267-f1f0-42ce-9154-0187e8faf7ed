import { forwardRef, useLayoutEffect, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Autocomplete,
  Avatar,
  Box,
  Button,
  Card,
  CircularProgress,
  Dialog,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  Slide,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  Zoom
} from '@mui/material';

import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import LaunchTwoToneIcon from '@mui/icons-material/LaunchTwoTone';
import Label from 'src/components/Label';
import SearchTwoToneIcon from '@mui/icons-material/SearchTwoTone';
import { CheckTwoTone } from '@mui/icons-material';
import ShoppingCartCheckoutTwoToneIcon from '@mui/icons-material/ShoppingCartCheckoutTwoTone';
import ChatBubbleOutlineTwoToneIcon from '@mui/icons-material/ChatBubbleOutlineTwoTone';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import { useTheme } from '@mui/material/styles';
import { format, formatDistance } from 'date-fns';
import Text from 'src/components/Text';
import BulkActions from './BulkActions';
import { uuidv4 } from '../../../../utils/customFunctions';
import useAuth from '../../../../hooks/useAuth';
import { useDeploymentUrls } from 'src/store/deploymentUrlStore';
import Cookies from 'js-cookie';
import { motion } from 'framer-motion';

const DialogWrapper = styled(Dialog)(
  () => `
      .MuiDialog-paper {
        overflow: visible;
      }
`
);

const AvatarError = styled(Avatar)(
  ({ theme }) => `
      background-color: ${theme.colors.error.lighter};
      color: ${theme.colors.error.main};
      width: ${theme.spacing(12)};
      height: ${theme.spacing(12)};

      .MuiSvgIcon-root {
        font-size: ${theme.typography.pxToRem(45)};
      }
`
);

const CardWrapper = styled(Card)(
  ({ theme }) => `

  position: relative;
  overflow: visible;

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: inherit;
    z-index: 1;
    transition: ${theme.transitions.create(['box-shadow'])};
  }
      
    &.Mui-selected::after {
      box-shadow: 0 0 0 3px ${theme.colors.primary.main};
    }
  `
);

const ButtonError = styled(Button)(
  ({ theme }) => `
     background: ${theme.colors.error.main};
     color: ${theme.palette.error.contrastText};

     &:hover {
        background: ${theme.colors.error.dark};
     }
    `
);

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const getProjectStatusLabel = (projectStatus) => {
  const map = {
    'Not Started': {
      text: 'Not Started',
      color: 'error'
    },
    'In Progress': {
      text: 'In Progress',
      color: 'info'
    },
    Completed: {
      text: 'Completed',
      color: 'success'
    }
  };

  const { text, color } = map[projectStatus];

  return <Label color={color}>{text}</Label>;
};

const applyFilters = (projects, query, filters) => {
  return projects.filter((project) => {
    let matches = true;

    if (query) {
      const properties = ['name'];
      let containsQuery = false;

      properties.forEach((property) => {
        if (project[property].toLowerCase().includes(query.toLowerCase())) {
          containsQuery = true;
        }
      });

      if (filters.status && project.status !== filters.status) {
        matches = false;
      }

      if (!containsQuery) {
        matches = false;
      }
    }

    Object.keys(filters).forEach((key) => {
      const value = filters[key];
      if (
        key === 'company' &&
        value &&
        Array.isArray(value) &&
        value.length !== 0 &&
        value.includes(project[key])
      ) {
        console.log('Filtering Company:', value);
        console.log('Project Company:', project[key]);
        matches = true;
      } else if (value && project[key] !== value) {
        matches = false;
      }
    });

    return matches;
  });
};

const applyPagination = (projects, page, limit) => {
  return projects.slice(page * limit, page * limit + limit);
};

const Results = ({ projects }) => {
  // eslint-disable-next-line no-unused-vars
  const [selectedItems, setSelectedProjects] = useState([]);
  const { currentProject, setProject } = useAuth();
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const { getDeploymentUrl } = useDeploymentUrls();
  const theme = useTheme();
  const navigate = useNavigate();
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(15);
  const [query, setQuery] = useState('');
  const [deDupedProjectsFilter, setDeDupedProjectsFilter] = useState([]);
  const [filters, setFilters] = useState({
    status: null,
    company: null
  });

  const handleProjectSetForCart = (project) => {
    setProject(project);
    enqueueSnackbar(t(`Project ${project.project} has been set for checkout`), {
      variant: 'info',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'center'
      },
      TransitionComponent: Zoom
    });
  };

  const statusOptions = [
    {
      id: 'all',
      name: 'All'
    },
    {
      id: 'Not Started',
      name: t('Not Started')
    },
    {
      id: 'Completed',
      name: t('Completed')
    },
    {
      id: 'In Progress',
      name: t('In Progress')
    }
  ];

  const handleQueryChange = (event) => {
    event.persist();
    setQuery(event.target.value);
  };

  const handleStatusChange = (e) => {
    let value = null;

    if (e.target.value !== 'all') {
      value = e.target.value;
    }

    setFilters((prevFilters) => ({
      ...prevFilters,
      status: value
    }));
  };

  const handleProjectFilterChange = (e, v) => {
    let value = null;

    console.log('Event Auto:', e);
    console.log('Value Auto:', v);
    if (v && v.length !== 0) {
      value = v;
    }

    setFilters((prevFilters) => ({
      ...prevFilters,
      company: value
    }));
  };

  /*
  const handleSelectAllProjects = (event) => {
    setSelectedProjects(
      event.target.checked ? projects.map((project) => project.id) : []
    );
  };
*/

  /* const handleSelectOneProject = (_event, projectId) => {
    if (!selectedItems.includes(projectId)) {
      setSelectedProjects((prevSelected) => [...prevSelected, projectId]);
    } else {
      setSelectedProjects((prevSelected) =>
        prevSelected.filter((id) => id !== projectId)
      );
    }
  }; */

  const handlePageChange = (_event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value));
  };

  const filteredProjects = applyFilters(projects, query, filters);
  const paginatedProjects = applyPagination(filteredProjects, page, limit);
  const selectedBulkActions = selectedItems.length > 0;
  /*
  const selectedSomeProjects =
    selectedItems.length > 0 && selectedItems.length < projects.length;
  const selectedAllProjects = selectedItems.length === projects.length;
*/

  const [toggleView /* setToggleView */] = useState('table_view');

  /* const handleViewOrientation = (_event, newValue) => {
    setToggleView(newValue);
  }; */

  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);

  const closeConfirmDelete = () => {
    setOpenConfirmDelete(false);
  };

  const handleDeleteCompleted = () => {
    setOpenConfirmDelete(false);

    enqueueSnackbar(t('The projects has been deleted successfully'), {
      variant: 'success',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'right'
      },
      TransitionComponent: Zoom
    });
  };

  useLayoutEffect(() => {
    if (projects && projects.length !== 0) {
      let allProjects = projects.map((project) => project.company);
      setDeDupedProjectsFilter(
        allProjects.sort().filter(function (item, pos, ary) {
          return !pos || item !== ary[pos - 1];
        })
      );
    }
  }, [projects]);

  return (
    <>
      <Card
        sx={{
          p: 1,
          mb: 3
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box p={1}>
              <TextField
                sx={{
                  m: 0
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchTwoToneIcon />
                    </InputAdornment>
                  )
                }}
                onChange={handleQueryChange}
                placeholder={t('Search by task name...')}
                value={query}
                fullWidth
                variant="outlined"
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={6}>
            <Box p={1}>
              <Autocomplete
                multiple
                sx={{
                  m: 0
                }}
                limitTags={2}
                onChange={handleProjectFilterChange}
                options={deDupedProjectsFilter}
                getOptionLabel={(option) => option}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    fullWidth
                    variant="outlined"
                    label={t('Projects')}
                    placeholder={t('Select projects...')}
                  />
                )}
              />
            </Box>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Box p={1}>
              <FormControl fullWidth variant="outlined">
                <InputLabel>{t('Status')}</InputLabel>
                <Select
                  value={filters.status || 'all'}
                  onChange={handleStatusChange}
                  label={t('Status')}
                >
                  {statusOptions.map((statusOption) => (
                    <MenuItem key={statusOption.id} value={statusOption.id}>
                      {statusOption.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Box>
          </Grid>
          {/*   <Grid
            item
            xs={12}
            md={3}
            display="flex"
            justifyContent={{ xs: 'center', md: 'flex-end' }}
          >
            <Box p={1}>
              <ToggleButtonGroup
                value={toggleView}
                exclusive
                onChange={handleViewOrientation}
              >
                <ToggleButton disableRipple value="table_view">
                  <TableRowsTwoToneIcon />
                </ToggleButton>
                <ToggleButton disableRipple value="grid_view">
                  <GridViewTwoToneIcon />
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
          </Grid> */}
        </Grid>
      </Card>

      {toggleView === 'table_view' && (
        <Card>
          {selectedBulkActions && (
            <Box p={2}>
              <BulkActions />
            </Box>
          )}
          {!selectedBulkActions && (
            <Box
              p={2}
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <Box>
                {/* <Typography component="span" variant="subtitle1">
                  {t('Showing')}:
                </Typography>{' '}
                <b>{paginatedProjects.length}</b> <b>{t('tasks')}</b> */}
              </Box>
              <TablePagination
                component="div"
                count={filteredProjects.length}
                onPageChange={handlePageChange}
                onRowsPerPageChange={handleLimitChange}
                page={page}
                rowsPerPage={limit}
                rowsPerPageOptions={[5, 10, 15, 25, 50]}
              />
            </Box>
          )}
          <Divider />

          {paginatedProjects.length === 0 ? (
            <>
              <Typography
                sx={{
                  py: 10
                }}
                variant="h3"
                fontWeight="normal"
                color="text.secondary"
                align="center"
              >
                {t("We couldn't find any tasks matching your search criteria")}
              </Typography>
            </>
          ) : (
            <>
              <TableContainer>
                {projects && projects.length !== 0 ? (
                  <Table>
                    <TableHead>
                      <TableRow>
                        {/* <TableCell padding="checkbox">
                        <Checkbox
                          checked={selectedAllProjects}
                          indeterminate={selectedSomeProjects}
                          onChange={handleSelectAllProjects}
                        />
                      </TableCell> */}
                        <TableCell>{t('Name')}</TableCell>
                        <TableCell>{t('Status')}</TableCell>
                        {/* <TableCell>{t('Project')}</TableCell> */}
                        <TableCell>{t('Start Date')}</TableCell>
                        <TableCell>{t('End Date')}</TableCell>
                        <TableCell align="center">{t('Chat')}</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {paginatedProjects.map((project) => {
                        const isProjectSelected = selectedItems.includes(
                          project.id
                        );
                        return (
                          <TableRow
                            hover
                            key={`${project.internalid}-${project.id}`}
                            selected={isProjectSelected}
                          >
                            {/* <TableCell padding="checkbox">
                            <Checkbox
                              checked={isProjectSelected}
                              onChange={(event) =>
                                handleSelectOneProject(event, project.id)
                              }
                              value={isProjectSelected}
                            />
                          </TableCell> */}
                            <TableCell>
                              <Typography variant="h5">
                                {project.name}
                              </Typography>
                              {/* {process.env.NODE_ENV === 'development' && (
                                <>
                                  <Typography
                                    noWrap
                                    fontSize="small"
                                    variant="caption"
                                  >
                                    {project.company.split(':')[0].trim()}
                                  </Typography>
                                  <Divider />
                                </>
                              )} */}
                              <Typography
                                gutterBottom
                                fontSize="small"
                                variant="caption"
                              >
                                {project.company.split(':')[1].trim()}
                              </Typography>
                              <Tooltip title={t('View Project')} arrow>
                                <IconButton
                                  onClick={() =>
                                    navigate(
                                      `/management/projects/single/${project.projectid}`
                                    )
                                  }
                                  color="primary"
                                >
                                  <LaunchTwoToneIcon fontSize="small" />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                            <TableCell>
                              <Typography noWrap>
                                {getProjectStatusLabel(project.status)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {/* {project.finshbydate ?
                                <Typography
                                  noWrap
                                  variant="subtitle1"
                                  color="text.primary"
                                >
                                  {t('Finish By')}
                                  <b>
                                    {' '}
                                    {formatDistance(
                                      project.parsedstartdate,
                                      project.parsedfinishbydate || 0,
                                      {
                                        addSuffix: true
                                      }
                                    )}
                                  </b>
                                </Typography>
                                :
                               null
                              } */}
                              <Typography noWrap color="text.secondary">
                                {format(
                                  new Date(project.startdate),
                                  'MM/dd/yyyy'
                                )}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography noWrap color="text.secondary">
                                {format(
                                  new Date(
                                    project.finshbydate ?? project.enddate
                                  ),
                                  'MM/dd/yyyy'
                                )}
                              </Typography>
                            </TableCell>
                            <TableCell align="center">
                              <Tooltip
                                title={
                                  !project.externalChat
                                    ? t(
                                        'Chat is not enabled externally for this task'
                                      )
                                    : t('Open Task Chat')
                                }
                                arrow
                              >
                                <span>
                                  <IconButton
                                    disabled={!project.externalChat}
                                    onClick={() => {
                                      if (project.externalChat) {
                                        const isDevelopment =
                                          process.env.NODE_ENV ===
                                          'development';
                                        const baseUrl = isDevelopment
                                          ? 'http://localhost:5173/'
                                          : getDeploymentUrl?.get_cs_chat_url;

                                        if (baseUrl) {
                                          const chatUrl = `${baseUrl}?taskId=${project.internalid}&mode=view`;
                                          Cookies.set(
                                            'ept-taskChatId',
                                            project.internalid,
                                            { expires: 7 }
                                          );
                                          Cookies.set('ept-internalChat', 'F', {
                                            expires: 7
                                          });
                                          navigate('/view-chat', {
                                            state: {
                                              chatUrl,
                                              taskName: project.name
                                            }
                                          });
                                        } else {
                                          console.error(
                                            'Chat base URL is not configured.'
                                          );
                                          enqueueSnackbar(
                                            t(
                                              'Chat URL is not configured. Please contact support.'
                                            ),
                                            {
                                              variant: 'error'
                                            }
                                          );
                                        }
                                      }
                                    }}
                                    color="primary"
                                  >
                                    <ChatBubbleOutlineTwoToneIcon fontSize="small" />
                                    {project.externalChat && (
                                      <>
                                        {/* Animated ring pulse */}
                                        <motion.span
                                          initial={{ scale: 0.7, opacity: 0.5 }}
                                          animate={{
                                            scale: [0.7, 1.6],
                                            opacity: [0.5, 0]
                                          }}
                                          transition={{
                                            duration: 1.3,
                                            repeat: Infinity,
                                            repeatType: 'loop',
                                            ease: 'easeInOut'
                                          }}
                                          style={{
                                            position: 'absolute',
                                            top: 0,
                                            right: 0,
                                            width: 18,
                                            height: 18,
                                            borderRadius: '50%',
                                            background:
                                              'rgba(76, 175, 80, 0.25)',
                                            zIndex: 1,
                                            filter: 'blur(2px)'
                                          }}
                                        />
                                        {/* Static dot */}
                                        <span
                                          style={{
                                            position: 'absolute',
                                            top: 4,
                                            right: 4,
                                            width: 8,
                                            height: 8,
                                            borderRadius: '50%',
                                            background: '#4caf50',
                                            zIndex: 2,
                                            boxShadow:
                                              '0 0 2px 1px rgba(76,175,80,0.3)'
                                          }}
                                        />
                                      </>
                                    )}
                                  </IconButton>
                                </span>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                ) : (
                  <CircularProgress />
                )}
              </TableContainer>
              <Box p={2}>
                <TablePagination
                  component="div"
                  count={filteredProjects.length}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleLimitChange}
                  page={page}
                  rowsPerPage={limit}
                  rowsPerPageOptions={[5, 10, 15]}
                />
              </Box>
            </>
          )}
        </Card>
      )}
      {toggleView === 'grid_view' && (
        <>
          {paginatedProjects.length !== 0 && (
            <Card
              sx={{
                p: 2,
                mb: 3
              }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                <>
                  {/* <Box display="flex" alignItems="center">
                    <Tooltip
                      arrow
                      placement="top"
                      title={t('Select all projects')}
                    >
                      <Checkbox
                        checked={selectedAllProjects}
                        indeterminate={selectedSomeProjects}
                        onChange={handleSelectAllProjects}
                      />
                    </Tooltip>
                  </Box>
                  {selectedBulkActions && (
                    <Box flex={1} pl={2}>
                      <BulkActions />
                    </Box>
                  )} */}
                  {!selectedBulkActions && (
                    <TablePagination
                      component="div"
                      count={filteredProjects.length}
                      onPageChange={handlePageChange}
                      onRowsPerPageChange={handleLimitChange}
                      page={page}
                      rowsPerPage={limit}
                      rowsPerPageOptions={[5, 10, 15]}
                    />
                  )}
                </>
              </Box>
            </Card>
          )}
          {paginatedProjects.length === 0 ? (
            <Typography
              sx={{
                py: 10
              }}
              variant="h3"
              fontWeight="normal"
              color="text.secondary"
              align="center"
            >
              {t("We couldn't find any task matching your search criteria")}
            </Typography>
          ) : (
            <>
              <Grid container spacing={3}>
                {paginatedProjects.map((project) => {
                  const isProjectSelected = selectedItems.includes(
                    project.internalid
                  );

                  return (
                    <Grid item xs={12} sm={6} md={4} key={project.internalid}>
                      <CardWrapper
                        className={clsx({
                          'Mui-selected': isProjectSelected
                        })}
                      >
                        <Box
                          sx={{
                            position: 'relative',
                            zIndex: '2'
                          }}
                        >
                          <Box
                            pl={2}
                            py={1}
                            pr={1}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                          >
                            <Box>
                              <Typography component="span">
                                <b>
                                  {t('Project')}: {project.company}
                                </b>
                              </Typography>
                            </Box>
                            {/* <Checkbox
                              checked={isProjectSelected}
                              onChange={(event) =>
                                handleSelectOneProject(event, project.internalid)
                              }
                              value={isProjectSelected}
                            /> */}
                          </Box>
                          <Divider />
                          {/*  <CardMedia
                            sx={{
                              minHeight: 180
                            }}
                            image={project.screenshot}
                          /> */}
                          <Divider />
                          <Box p={2}>
                            {getProjectStatusLabel(project.status)}

                            <Typography
                              sx={{
                                mt: 2
                              }}
                              variant="h4"
                              gutterBottom
                            >
                              {project.name}
                            </Typography>

                            <Typography noWrap variant="subtitle2">
                              {project.description}
                            </Typography>
                          </Box>
                          <Box
                            px={2}
                            display="flex"
                            alignItems="flex-end"
                            justifyContent="space-between"
                          >
                            <Box>
                              <Typography variant="subtitle2">
                                {t('Started')}:{' '}
                              </Typography>
                              <Typography variant="h5">
                                {format(
                                  new Date(project.startdate),
                                  'MM/dd/yyyy'
                                )}
                              </Typography>
                            </Box>
                            <Box>
                              {project.finshbydate ? (
                                <Typography
                                  noWrap
                                  variant="subtitle1"
                                  color="text.primary"
                                >
                                  {t('Finish By')}
                                  <Text>
                                    {' '}
                                    {formatDistance(
                                      new Date(project.finshbydate),
                                      new Date(),
                                      {
                                        addSuffix: true
                                      }
                                    )}
                                  </Text>
                                </Typography>
                              ) : null}
                              <Typography
                                noWrap
                                variant="subtitle1"
                                color="text.primary"
                              >
                                {t('Due')}
                                <Text color="black">
                                  &nbsp;
                                  {formatDistance(
                                    new Date(project.enddate).setDate(
                                      new Date(project.enddate).getDate() + 1
                                    ),
                                    new Date(),
                                    {
                                      addSuffix: true
                                    }
                                  )}
                                </Text>
                              </Typography>
                            </Box>
                          </Box>

                          <Box px={2} pb={2} display="flex" alignItems="center">
                            <LinearProgress
                              sx={{
                                flex: 1,
                                mr: 1
                              }}
                              value={parseFloat(project.progress)}
                              color="primary"
                              variant="determinate"
                            />
                            <Typography variant="subtitle1">
                              {project.progress}
                            </Typography>
                          </Box>
                          <Divider />
                          <Box
                            p={2}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                          >
                            <Box>
                              <Button
                                sx={{
                                  mr: 1
                                }}
                                size="small"
                                variant="contained"
                                color="primary"
                              >
                                {t('View')}
                              </Button>
                              {currentProject?.id === project.id ? (
                                <Tooltip title={t('Cart has been set')} arrow>
                                  <IconButton color="success">
                                    <CheckTwoTone fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title={t('Set Cart')} arrow>
                                  <IconButton
                                    onClick={() =>
                                      handleProjectSetForCart(project)
                                    }
                                    color="primary"
                                  >
                                    <ShoppingCartCheckoutTwoToneIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </CardWrapper>
                    </Grid>
                  );
                })}
              </Grid>
              <Card
                sx={{
                  p: 2,
                  mt: 3,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <Box>
                  <Typography component="span" variant="subtitle1">
                    {t('Showing')}
                  </Typography>{' '}
                  <b>{limit}</b> {t('of')} <b>{filteredProjects.length}</b>{' '}
                  <b>{t('projects')}</b>
                </Box>
                <TablePagination
                  component="div"
                  count={filteredProjects.length}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleLimitChange}
                  page={page}
                  rowsPerPage={limit}
                  labelRowsPerPage=""
                  rowsPerPageOptions={[5, 10, 15]}
                />
              </Card>
            </>
          )}
        </>
      )}
      {!toggleView && (
        <Card
          sx={{
            textAlign: 'center',
            p: 3
          }}
        >
          <Typography
            align="center"
            variant="h4"
            fontWeight="normal"
            color="text.secondary"
            sx={{
              my: 5
            }}
            gutterBottom
          >
            {t(
              'Choose between table or grid views for displaying the projects list.'
            )}
          </Typography>
        </Card>
      )}

      <DialogWrapper
        open={openConfirmDelete}
        maxWidth="sm"
        fullWidth
        TransitionComponent={Transition}
        keepMounted
        onClose={closeConfirmDelete}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          flexDirection="column"
          p={5}
        >
          <AvatarError>
            <CloseIcon />
          </AvatarError>

          <Typography
            align="center"
            sx={{
              pt: 4,
              px: 6
            }}
            variant="h3"
          >
            {t('Do you really want to delete this project')}?
          </Typography>

          <Typography
            align="center"
            sx={{
              pt: 2,
              pb: 4,
              px: 6
            }}
            fontWeight="normal"
            color="text.secondary"
            variant="h4"
          >
            {t("You won't be able to revert after deletion")}
          </Typography>

          <Box>
            <Button
              variant="text"
              size="large"
              sx={{
                mx: 1
              }}
              onClick={closeConfirmDelete}
            >
              {t('Cancel')}
            </Button>
            <ButtonError
              onClick={handleDeleteCompleted}
              size="large"
              sx={{
                mx: 1,
                px: 3
              }}
              variant="contained"
            >
              {t('Delete')}
            </ButtonError>
          </Box>
        </Box>
      </DialogWrapper>
    </>
  );
};

Results.propTypes = {
  projects: PropTypes.array.isRequired
};

Results.defaultProps = {
  projects: []
};

export default Results;
