import { useContext, useEffect, useState } from 'react';
import { Helmet } from 'react-helmet-async';
import Footer from 'src/components/Footer';
import {
  Avatar,
  Box,
  Card,
  Divider,
  Grid,
  styled,
  Tab,
  Tabs
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { useTranslation } from 'react-i18next';
import useSWR from 'swr';
import PageHeader from './PageHeader';
// import TasksAnalytics from './TasksAnalytics';
// import Performance from './Performance';
// import Checklist from './Checklist';
// import Profile from './Profile';
import TaskSearch from './TaskSearch';
import Results from './Tasks/Results';
// import useRefMounted from '../../../hooks/useRefMounted';
import useAuth from '../../../hooks/useAuth';
import NewsTab from './NewsTab';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

const TabsContainerWrapper = styled(Box)(
  ({ theme }) => `
      padding: 0 ${theme.spacing(8)};
      position: relative;
      bottom: -1px;
      
      .MuiTabs-root {
        height: 100%;
      }

      .MuiTabs-scrollableX {
        overflow-x: hidden !important;
      }

      .MuiTabs-indicator {
          min-height: 4px;
          height: 4px;
          box-shadow: none;
          bottom: -4px;
          background: none;
          border: 0;

          &:after {
            position: absolute;
            left: 50%;
            width: 28px;
            content: ' ';
            margin-left: -14px;
            background: ${theme.colors.primary.main};
            border-radius: inherit;
            height: 100%;
          }
      }

      .MuiTabs-scroller {
         overflow-y: hidden !important;
      }

      .MuiTab-root {
          &.MuiButtonBase-root {
              height: 50px;
              max-height: 44px;
              min-height: 44px;
              background: ${theme.colors.alpha.white[70]};
              border: 1px solid ${theme.colors.alpha.black[10]};
              border-bottom: 0;
              position: relative;
              margin-right: ${theme.spacing(1)};
              font-size: ${theme.typography.pxToRem(14)};
              color: ${theme.colors.alpha.black[80]};
              border-bottom-left-radius: 0;
              border-bottom-right-radius: 0;

              .MuiTouchRipple-root {
                opacity: .1;
              }

              &:after {
                position: absolute;
                left: 0;
                right: 0;
                width: 100%;
                bottom: 0;
                height: 1px;
                content: '';
                background: ${theme.colors.alpha.black[10]};
              }

              &:hover {
                color: ${theme.colors.alpha.black[100]};
              }
          }

          &.Mui-selected {
              color: ${theme.colors.alpha.black[100]};
              background: ${theme.colors.alpha.white[100]};
              border-bottom-color: ${theme.colors.alpha.white[100]};

              &:after {
                height: 0;
              }
          }
      }
  `
);

// eslint-disable-next-line no-unused-vars
const AvatarPrimary = styled(Avatar)(
  ({ theme }) => `
      background-color: ${theme.colors.primary.lighter};
      color: ${theme.colors.primary.main};
      width: ${theme.spacing(10)};
      height: ${theme.spacing(10)};
      margin: 0 auto ${theme.spacing(2)};

      .MuiSvgIcon-root {
        font-size: ${theme.typography.pxToRem(42)};
      }
`
);

function DashboardExhibitTasks() {
  const { user } = useAuth();
  const [projectTasks, setProjectTasks] = useState([]);
  const { t } = useTranslation();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const getMetricsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_project_task_metrics_external
      : getDeploymentUrl.get_project_task_metrics;
  // eslint-disable-next-line no-unused-vars
  const { data: projectMetrics, error: metricsError } = useSWR(getMetricsUrl);

  useEffect(() => {
    if (user && user?.profile) {
      setProjectTasks(user.profile.projects.tasks);
      console.log('Projects:', user.profile.projects.tasks);
      console.log('Metrics:', projectMetrics);
    }
  }, [user]);

  const [currentTab, setCurrentTab] = useState('analytics');

  const tabs = [
    { value: 'analytics', label: t('Tasks Overview') },
    // { value: 'taskSearch', label: t('Project Search') },
    { value: 'newsBoard', label: t('News Board') }
  ];

  const handleTabsChange = (_event, value) => {
    setCurrentTab(value);
  };

  return (
    <>
      <Helmet>
        <title>Dashboard</title>
      </Helmet>
      <Grid display="flex" alignItems="center" item xs={12} px={4} pb={4}>
        <Box flex={1} mt={3}>
          <PageHeader />
        </Box>
      </Grid>
      <TabsContainerWrapper>
        <Tabs
          onChange={handleTabsChange}
          value={currentTab}
          variant="standard"
          scrollButtons={false}
          textColor="primary"
          indicatorColor="primary"
        >
          {tabs.map((tab) => (
            <Tab key={tab.value} label={tab.label} value={tab.value} />
          ))}
        </Tabs>
      </TabsContainerWrapper>
      <Card
        variant="outlined"
        sx={{
          mx: 4
        }}
      >
        <Grid
          container
          direction="row"
          justifyContent="center"
          alignItems="stretch"
          spacing={0}
        >
          {currentTab === 'analytics' && (
            <>
              <Grid item xs={12}>
                <Divider />
                <Box
                  p={4}
                  sx={{
                    background: `${theme.colors.alpha.black[5]}`
                  }}
                >
                  <Grid container spacing={4}>
                    <Grid item xs={12}>
                      <Results projects={projectTasks} />
                    </Grid>
                  </Grid>
                </Box>
                <Divider />
              </Grid>
              {/* <Grid item xs={12}>
                <Box p={4}>
                  <Projects projectMetrics={projectMetrics}/>
                </Box>
                <Divider />
              </Grid> */}
            </>
          )}
          {currentTab === 'taskSearch' && (
            <Grid item xs={12}>
              <Box p={4}>
                <TaskSearch projects={user.profile.projects.list} />
              </Box>
            </Grid>
          )}
          {currentTab === 'newsBoard' && (
            <Grid item xs={12}>
              <NewsTab newsItems={user.profile.news} />
            </Grid>
          )}
        </Grid>
      </Card>
      <Footer />
    </>
  );
}

export default DashboardExhibitTasks;
