import {
  Avatar,
  Box,
  <PERSON><PERSON>,
  <PERSON>Header,
  CircularProgress,
  Grid,
  IconButton,
  LinearProgress,
  styled,
  Typography,
  useTheme
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import MoreVertTwoToneIcon from '@mui/icons-material/MoreVertTwoTone';
import { Link } from 'react-router-dom';
import Text from 'src/components/Text';
import CheckTwoToneIcon from '@mui/icons-material/CheckTwoTone';
import NotificationsActiveTwoToneIcon from '@mui/icons-material/NotificationsActiveTwoTone';

const AvatarWrapperSuccess = styled(Avatar)(
  ({ theme }) => `
      background-color: ${theme.colors.success.lighter};
      color:  ${theme.colors.success.main};
`
);

// eslint-disable-next-line no-unused-vars
const DotLegend = styled('span')(
  ({ theme }) => `
    border-radius: 22px;
    width: ${theme.spacing(1.8)};
    height: ${theme.spacing(1.8)};
    display: inline-block;
    border: 2px solid ${theme.colors.alpha.white[100]};
    margin-right: ${theme.spacing(0.5)};
`
);

const LinearProgressWrapper = styled(LinearProgress)(
  ({ theme }) => `
        flex-grow: 1;
        height: 10px;
        margin: ${theme.spacing(1, 0, 2)};
        
        &.MuiLinearProgress-root {
          background-color: ${theme.colors.alpha.black[10]};
        }
        
        .MuiLinearProgress-bar {
          border-radius: ${theme.general.borderRadiusXl};
        }
`
);

const AvatarPrimary = styled(Avatar)(
  ({ theme }) => `
      background-color: ${theme.colors.primary.lighter};
      color: ${theme.colors.primary.main};
      width: ${theme.spacing(10)};
      height: ${theme.spacing(10)};
      margin: 0 auto ${theme.spacing(2)};

      .MuiSvgIcon-root {
        font-size: ${theme.typography.pxToRem(42)};
      }
`
);

function Projects({ projectMetrics }) {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <>
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          pb: 3
        }}
      >
        <Typography variant="h3">
          {t('Recently Modified Projects Tasks')}
        </Typography>
        <Box>
          <Button
            size="small"
            variant="outlined"
            component={Link}
            to="/management/projects"
          >
            {t('View all projects')}
          </Button>
        </Box>
      </Box>
      <Grid container spacing={4}>
        {projectMetrics?.metrics &&
        projectMetrics.metrics.tasks.length !== 0 ? (
          projectMetrics.metrics.tasks.map((metric, i) => {
            let projectNameAbbv = metric.project.name
              .split(':')[1]
              .trim()
              .split(' ')
              .slice(1)
              .filter((s) => s.search(/^[A-Z][a-z0-9_-]{3,19}$/) !== -1)
              .map((s) => s[0])
              .join('');
            return (
              <Grid item xs={12} md={4} key={i}>
                <Box>
                  <CardHeader
                    sx={{
                      px: 0,
                      pt: 0
                    }}
                    avatar={
                      <>
                        {metric.task_count === metric.tasks_completed ? (
                          <AvatarWrapperSuccess>
                            <CheckTwoToneIcon />
                          </AvatarWrapperSuccess>
                        ) : (
                          <Avatar
                            sx={{
                              background: `${theme.colors.gradients.blue1}`
                            }}
                          >
                            {projectNameAbbv}
                          </Avatar>
                        )}
                      </>
                    }
                    action={
                      <IconButton size="small" color="primary">
                        <MoreVertTwoToneIcon />
                      </IconButton>
                    }
                    title={t(metric.project.name)}
                    titleTypographyProps={{
                      variant: 'h5',
                      color: 'textPrimary'
                    }}
                  />
                  <Box>
                    <Typography variant="subtitle2" gutterBottom>
                      {t('Tasks done')}:{' '}
                      <Text color="black">
                        <b>{metric.tasks_completed}</b>
                      </Text>
                      <b> {t(`/ ${metric.task_count}`)}</b>
                    </Typography>
                    <LinearProgressWrapper
                      value={(metric.tasks_completed / metric.task_count) * 100}
                      color="primary"
                      variant="determinate"
                    />
                  </Box>
                  {/*  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'space-between'
                    }}
                  >
                    <Box>
                      <Tooltip
                        arrow
                        title={t('View project calendar')}
                        placement="top"
                      >
                        <IconButton
                          size="small"
                          color="secondary"
                          sx={{
                            ml: 0.5
                          }}
                        >
                          <CalendarTodayTwoToneIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                      <Tooltip
                        arrow
                        title={t('Mark project as favourite')}
                        placement="top"
                      >
                        <IconButton
                          size="small"
                          sx={{
                            color: `${theme.colors.warning.main}`,
                            ml: 0.5
                          }}
                        >
                          <StarTwoToneIcon fontSize="small" />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box> */}
                </Box>
              </Grid>
            );
          })
        ) : projectMetrics?.metrics &&
          projectMetrics.metrics.tasks.length === 0 ? (
          <Grid item xs={12}>
            <Box
              sx={{
                py: { xs: 3, md: 8, lg: 12 },
                textAlign: 'center'
              }}
            >
              <AvatarPrimary>
                <NotificationsActiveTwoToneIcon />
              </AvatarPrimary>
              <Typography variant="h2">{t('No projects available')}</Typography>
              <Typography
                variant="h4"
                sx={{
                  pt: 1,
                  pb: 3
                }}
                fontWeight="normal"
                color="text.secondary"
              >
                {t(
                  'No projects have been found against your account, create a new one here or browse projects'
                )}
                !
              </Typography>
              <Button
                color="primary"
                variant="outlined"
                sx={{
                  borderWidth: '2px',
                  '&:hover': {
                    borderWidth: '2px'
                  }
                }}
              >
                {t('Create new project')}
              </Button>
            </Box>
          </Grid>
        ) : (
          <Grid item xs={12}>
            <CircularProgress />
          </Grid>
        )}
      </Grid>
    </>
  );
}

export default Projects;
