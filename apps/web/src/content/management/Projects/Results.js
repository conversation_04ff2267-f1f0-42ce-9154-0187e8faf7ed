import { forwardRef, useContext, useEffect, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  Button,
  Card,
  Checkbox,
  Dialog,
  Divider,
  FormControl,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  LinearProgress,
  Link,
  MenuItem,
  Select,
  Slide,
  styled,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TablePagination,
  TableRow,
  TableSortLabel,
  TextField,
  Tooltip,
  Typography,
  Zoom
} from '@mui/material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import CloseIcon from '@mui/icons-material/Close';
import { useTranslation } from 'react-i18next';
import clsx from 'clsx';
import LaunchTwoToneIcon from '@mui/icons-material/LaunchTwoTone';
import Label from 'src/components/Label';
import SearchTwoToneIcon from '@mui/icons-material/SearchTwoTone';
import { CheckTwoTone } from '@mui/icons-material';
import ShoppingCartCheckoutTwoToneIcon from '@mui/icons-material/ShoppingCartCheckoutTwoTone';
import { useSnackbar } from 'notistack';
import { addDays, format, isPast, subDays } from 'date-fns';
import Text from 'src/components/Text';
import BulkActions from './BulkActions';
import { SettingsContext } from '../../../contexts/SettingsContext';
import useAuth from '../../../hooks/useAuth';
import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker';
import { SingleInputDateRangeField } from '@mui/x-date-pickers-pro/SingleInputDateRangeField';
import { DemoItem } from '@mui/x-date-pickers/internals/demo';

const DialogWrapper = styled(Dialog)(
  () => `
      .MuiDialog-paper {
        overflow: visible;
      }
`
);

const AvatarError = styled(Avatar)(
  ({ theme }) => `
      background-color: ${theme.colors.error.lighter};
      color: ${theme.colors.error.main};
      width: ${theme.spacing(12)};
      height: ${theme.spacing(12)};

      .MuiSvgIcon-root {
        font-size: ${theme.typography.pxToRem(45)};
      }
`
);

const CardWrapper = styled(Card)(
  ({ theme }) => `

  position: relative;
  overflow: visible;

  &::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    border-radius: inherit;
    z-index: 1;
    transition: ${theme.transitions.create(['box-shadow'])};
  }
      
    &.Mui-selected::after {
      box-shadow: 0 0 0 3px ${theme.colors.primary.main};
    }
  `
);

const ButtonError = styled(Button)(
  ({ theme }) => `
     background: ${theme.colors.error.main};
     color: ${theme.palette.error.contrastText};

     &:hover {
        background: ${theme.colors.error.dark};
     }
    `
);

const Transition = forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const applyFilters = (projects, query, filters, duration) => {
  return projects.filter((project) => {
    let matches = true;

    console.log('APPLYFILT', filters);

    if (query) {
      const properties = ['project', 'entityid'];
      let containsQuery = false;

      properties.forEach((property) => {
        if (project[property].toLowerCase().includes(query.toLowerCase())) {
          containsQuery = true;
        }
      });

      if (filters.status && project.status !== filters.status) {
        matches = false;
      }

      if (filters.orderedBy && project.orderedBy !== filters.orderedBy) {
        matches = false;
      }

      if (!containsQuery) {
        matches = false;
      }
    }

    Object.keys(filters).forEach((key) => {
      const value = filters[key];

      if (value && project[key] !== value) {
        matches = false;
      }
    });

    const isValidDate = (date) => !isNaN(date.getTime());

    if (duration[0] !== null && duration[1] !== null) {
      let durationStart = new Date(duration[0]);
      let durationEnd = new Date(duration[1]);
      let projectStartDate = new Date(project?.startdate);

      if (
        !isValidDate(projectStartDate) ||
        !isValidDate(durationStart) ||
        !isValidDate(durationEnd)
      ) {
      } else {
        if (
          projectStartDate < durationStart ||
          projectStartDate > durationEnd
        ) {
          matches = false;
        }
      }
    }

    return matches;
  });
};

const applyPagination = (projects, page, limit) => {
  return projects.slice(page * limit, page * limit + limit);
};

const Results = ({ projects }) => {
  const navigate = useNavigate();
  const { setProject, currentProject } = useAuth();
  const [selectedItems, setSelectedProjects] = useState([]);
  const { t } = useTranslation();
  const { enqueueSnackbar } = useSnackbar();
  const [settings] = useContext(SettingsContext);
  const [page, setPage] = useState(0);
  const [limit, setLimit] = useState(15);
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState({
    status: null
  });
  const [order, setOrder] = useState('asc');
  const [orderBy, setOrderBy] = useState('shipdate');
  const [shipCheck, setShipCheck] = useState({
    after: 0,
    prior: 0
  });
  const [duration, setDuration] = useState([null, null]);
  const [orderedByOptions, setOrderedByOptions] = useState([]);

  useEffect(() => {
    if (projects) {
      let orderedByList = [
        {
          id: 'all',
          name: 'All'
        }
      ];

      for (let i = 0; i < projects.length; i++) {
        let orderBy = projects[i]?.orderedBy;

        if (orderBy) {
          const alreadyExists = orderedByList.some(
            (item) => item.name === orderBy
          );
          if (!alreadyExists) {
            orderedByList.push({
              id: orderBy,
              name: orderBy
            });
          }
        }
      }

      setOrderedByOptions(orderedByList);
    }
  }, [projects]);

  const handleSortRequest = (property) => {
    const isAsc = orderBy === property && order === 'asc';
    setOrder(isAsc ? 'desc' : 'asc');
    setOrderBy(property);
  };

  const sortedProjects = projects.sort((a, b) => {
    if (
      orderBy === 'shipdate' ||
      orderBy === 'startdate' ||
      orderBy === 'enddate' ||
      orderBy === 'projreturn'
    ) {
      const dateA = new Date(a[orderBy]);
      const dateB = new Date(b[orderBy]);
      return order === 'asc' ? dateA - dateB : dateB - dateA;
    }

    if (orderBy === 'status') {
      return order === 'asc'
        ? a[orderBy].localeCompare(b[orderBy])
        : b[orderBy].localeCompare(a[orderBy]);
    }

    if (orderBy === 'orderedBy') {
      return order === 'asc'
        ? a[orderBy].localeCompare(b[orderBy])
        : b[orderBy].localeCompare(a[orderBy]);
    }
    return 0; // Keep existing sorting for non-date fields if needed
  });

  const withinDateThreshold = (date, position) => {
    let currentDate = new Date(date);
    let priorThreshold = subDays(currentDate, shipCheck.prior);
    let afterThreshold = addDays(currentDate, shipCheck.after);
    let disabled = false;

    if (position === 'start') {
      disabled = isPast(priorThreshold);
    } else {
      disabled = isPast(priorThreshold);
    }

    return disabled;
  };

  const projectStatusList = useMemo(() => {
    if (
      settings &&
      settings?.project_statuses &&
      Array.isArray(settings?.project_statuses)
    ) {
      return settings.project_statuses.map((entry) => ({
        id: entry.name,
        name: entry.name
      }));
    }
    return [];
  }, [settings]);

  const getProjectStatusLabel = (projectStatus) => {
    const map = {
      'Not Started': {
        text: 'Not Started',
        color: 'error'
      },
      'In Progress': {
        text: 'In Progress',
        color: 'info'
      },
      Completed: {
        text: 'Completed',
        color: 'success'
      },
      Closed: {
        text: 'Closed',
        color: 'secondary'
      },
      Awarded: {
        text: 'Awarded',
        color: 'warning'
      },
      'Not Awarded': {
        text: 'Not Awarded',
        color: 'warning'
      },
      Pending: {
        text: 'Pending',
        color: 'warning'
      },
      ' ': {
        text: 'Empty',
        color: 'secondary'
      }
    };

    if (!Object.keys(map).includes(projectStatus)) {
      return <Label color="primary">{projectStatus}</Label>;
    }

    const { text, color } = map[projectStatus];

    return <Label color={color}>{text}</Label>;
  };

  const statusOptions = [
    {
      id: 'all',
      name: 'All'
    },
    ...projectStatusList
  ];

  const handleQueryChange = (event) => {
    event.persist();
    setQuery(event.target.value);
  };

  const handleStatusChange = (e) => {
    let value = null;

    if (e.target.value !== 'all') {
      value = e.target.value;
    }

    console.log('STATUSVAL', e);

    setFilters((prevFilters) => ({
      ...prevFilters,
      status: value
    }));
  };

  const handleOrderByChange = (e) => {
    let value = null;

    if (e.target.value !== 'all') {
      value = e.target.value;
    }

    console.log('handleOrderByChange', e);

    setFilters((prevFilters) => ({
      ...prevFilters,
      orderedBy: value
    }));
  };

  const handleDateChange = (newDates) => {
    let dateOne = newDates[0];
    let dateTwo = newDates[1];

    if (dateOne !== null && dateTwo !== null) {
      setDuration(newDates);
    }
  };

  const handleSelectAllProjects = (event) => {
    setSelectedProjects(
      event.target.checked ? projects.map((project) => project.id) : []
    );
  };

  const handleSelectOneProject = (_event, projectId) => {
    if (!selectedItems.includes(projectId)) {
      setSelectedProjects((prevSelected) => [...prevSelected, projectId]);
    } else {
      setSelectedProjects((prevSelected) =>
        prevSelected.filter((id) => id !== projectId)
      );
    }
  };

  const handlePageChange = (_event, newPage) => {
    setPage(newPage);
  };

  const handleLimitChange = (event) => {
    setLimit(parseInt(event.target.value));
  };

  const filteredProjects = applyFilters(projects, query, filters, duration);
  const paginatedProjects = applyPagination(filteredProjects, page, limit);
  const selectedBulkActions = selectedItems.length > 0;
  const selectedSomeProjects =
    selectedItems.length > 0 && selectedItems.length < projects.length;
  const selectedAllProjects = selectedItems.length === projects.length;

  const [toggleView, setToggleView] = useState('table_view');

  // const handleViewOrientation = (_event, newValue) => {
  //   setToggleView(newValue);
  // };

  const [openConfirmDelete, setOpenConfirmDelete] = useState(false);

  const closeConfirmDelete = () => {
    setOpenConfirmDelete(false);
  };

  const handleDeleteCompleted = () => {
    setOpenConfirmDelete(false);

    enqueueSnackbar(t('The projects has been deleted successfully'), {
      variant: 'success',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'right'
      },
      TransitionComponent: Zoom
    });
  };

  const handleProjectSetForCart = (project) => {
    setProject(project);
    enqueueSnackbar(t(`Project ${project.project} has been set for checkout`), {
      variant: 'info',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'center'
      },
      TransitionComponent: Zoom
    });
  };

  return (
    <>
      <Card
        sx={{
          p: 1,
          mb: 3
        }}
      >
        <Grid container spacing={2}>
          <Grid item xs={12}>
            <Box p={1}>
              <TextField
                sx={{
                  m: 0
                }}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchTwoToneIcon />
                    </InputAdornment>
                  )
                }}
                onChange={handleQueryChange}
                placeholder={t('Search by project name...')}
                value={query}
                fullWidth
                variant="outlined"
              />
            </Box>
          </Grid>

          <Grid item xs={6}>
            <Box p={1}>
              <DemoItem label="Filter by Client Contact">
                <FormControl fullWidth variant="outlined">
                  <InputLabel>{t('Client Contact')}</InputLabel>
                  <Select
                    value={filters.orderedBy || 'all'}
                    onChange={handleOrderByChange}
                    label={t('Client Contact')}
                  >
                    {orderedByOptions.map((statusOption) => (
                      <MenuItem key={statusOption.id} value={statusOption.id}>
                        {statusOption.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </DemoItem>
            </Box>
          </Grid>

          <Grid item xs={6}>
            <Box p={1}>
              <DemoItem label="Filter by Status">
                <FormControl fullWidth variant="outlined">
                  <InputLabel>{t('Status')}</InputLabel>
                  <Select
                    value={filters.status || 'all'}
                    onChange={handleStatusChange}
                    label={t('Status')}
                  >
                    {statusOptions.map((statusOption) => (
                      <MenuItem key={statusOption.id} value={statusOption.id}>
                        {statusOption.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </DemoItem>
            </Box>
          </Grid>
          <Grid item xs={6}>
            <Box p={1}>
              <DemoItem label="Filter by Start Date">
                <DesktopDateRangePicker
                  fullWidth
                  slots={{ field: SingleInputDateRangeField }}
                  localeText={{ start: 'Event Start', end: 'Event End' }}
                  startText="Event start"
                  value={duration}
                  name="duration"
                  // shouldDisableDate={(date, position) => {
                  //   return withinDateThreshold(date, position);
                  // }}
                  onChange={(newValue) => {
                    console.log('Range Date:', newValue);
                    handleDateChange(newValue);
                  }}
                  renderInput={(startProps, endProps) => (
                    <>
                      <TextField required fullWidth {...startProps} />
                      <Box sx={{ mx: 2 }}> to </Box>
                      <TextField
                        required
                        fullWidth
                        {...endProps}
                        label="Event end"
                      />
                    </>
                  )}
                />
              </DemoItem>
            </Box>
          </Grid>

          {/* <Grid
            item
            xs={12}
            md={6}
            display="flex"
            justifyContent={{ xs: 'center', md: 'flex-end' }}
          >
            <Box p={1}>
              <ToggleButtonGroup
                value={toggleView}
                exclusive
                onChange={handleViewOrientation}
              >
                <ToggleButton disableRipple value="table_view">
                  <TableRowsTwoToneIcon />
                </ToggleButton>
                <ToggleButton disableRipple value="grid_view">
                  <GridViewTwoToneIcon />
                </ToggleButton>
              </ToggleButtonGroup>
            </Box>
          </Grid> */}
        </Grid>
      </Card>

      {toggleView === 'table_view' && (
        <Card>
          {selectedBulkActions && (
            <Box p={2}>
              <BulkActions />
            </Box>
          )}
          {!selectedBulkActions && (
            <Box
              p={2}
              display="flex"
              alignItems="center"
              justifyContent="space-between"
            >
              <Box>
                {/* <Typography component="span" variant="subtitle1">
                  {t('Showing')}:
                </Typography>{' '}
                <b>{paginatedProjects.length}</b> <b>{t('projects')}</b> */}
              </Box>
              <TablePagination
                component="div"
                count={filteredProjects.length}
                onPageChange={handlePageChange}
                onRowsPerPageChange={handleLimitChange}
                page={page}
                rowsPerPage={limit}
                rowsPerPageOptions={[5, 10, 15, 20, 25, 50]}
              />
            </Box>
          )}
          <Divider />

          {paginatedProjects.length === 0 ? (
            <>
              <Typography
                sx={{
                  py: 10
                }}
                variant="h3"
                fontWeight="normal"
                color="text.secondary"
                align="center"
              >
                {t(
                  "We couldn't find any projects matching your search criteria"
                )}
              </Typography>
            </>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      {/* <TableCell padding="checkbox"> */}
                      {/*  <Checkbox */}
                      {/*    checked={selectedAllProjects} */}
                      {/*    indeterminate={selectedSomeProjects} */}
                      {/*    onChange={handleSelectAllProjects} */}
                      {/*  /> */}
                      {/* </TableCell> */}
                      <TableCell>{t('Name')}</TableCell>
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'orderedBy'}
                          direction={orderBy === 'orderedBy' ? order : 'asc'}
                          onClick={() => handleSortRequest('orderedBy')}
                        >
                          {t('Client Contact')}
                        </TableSortLabel>
                      </TableCell>
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'status'}
                          direction={orderBy === 'status' ? order : 'asc'}
                          onClick={() => handleSortRequest('status')}
                        >
                          {t('Status')}
                        </TableSortLabel>
                      </TableCell>
                      {/* <TableCell>{t('Tags')}</TableCell> */}
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'shipdate'}
                          direction={orderBy === 'shipdate' ? order : 'asc'}
                          onClick={() => handleSortRequest('shipdate')}
                        >
                          {t('Ship Date')}
                        </TableSortLabel>
                      </TableCell>
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'startdate'}
                          direction={orderBy === 'startdate' ? order : 'asc'}
                          onClick={() => handleSortRequest('startdate')}
                        >
                          {t('Start Date')}
                        </TableSortLabel>
                      </TableCell>
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'enddate'}
                          direction={orderBy === 'enddate' ? order : 'asc'}
                          onClick={() => handleSortRequest('enddate')}
                        >
                          {t('End Date')}
                        </TableSortLabel>
                      </TableCell>
                      {/* <TableCell>{t('Material Arrival')}</TableCell> */}
                      {/* <TableCell>{t('Material Pickup')}</TableCell> */}
                      {/**/}
                      <TableCell>
                        <TableSortLabel
                          active={orderBy === 'projreturn'}
                          direction={orderBy === 'projreturn' ? order : 'asc'}
                          onClick={() => handleSortRequest('projreturn')}
                        >
                          {t('Return Date')}
                        </TableSortLabel>
                      </TableCell>
                      {/* <TableCell>{t('Progress')}</TableCell> */}
                      <TableCell align="center">{t('Actions')}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {projects.length !== 0 &&
                      paginatedProjects.map((project) => {
                        const isProjectSelected = selectedItems.includes(
                          project.id
                        );
                        return (
                          <TableRow
                            hover
                            key={project.id}
                            selected={isProjectSelected}
                          >
                            {/* <TableCell padding="checkbox"> */}
                            {/*  <Checkbox */}
                            {/*    checked={isProjectSelected} */}
                            {/*    onChange={(event) => */}
                            {/*      handleSelectOneProject(event, project.id) */}
                            {/*    } */}
                            {/*    value={isProjectSelected} */}
                            {/*  /> */}
                            {/* </TableCell> */}
                            <TableCell>
                              <Typography noWrap variant="h5">
                                {`${project.entityid} ${project.project}`}
                                <Tooltip title={t('View Project')} arrow>
                                  <IconButton
                                    sx={{ ml: 0.5 }}
                                    onClick={() =>
                                      navigate(
                                        `/management/projects/single/${project.internalid}`
                                      )
                                    }
                                    color="primary"
                                  >
                                    <LaunchTwoToneIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {project?.orderedBy ? (
                                <>
                                  <Typography noWrap color="text.secondary">
                                    {project?.orderedBy}
                                  </Typography>
                                </>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Client Set')}
                                </Typography>
                              )}
                            </TableCell>
                            {/* Project Status */}
                            <TableCell>
                              <Typography noWrap>
                                {getProjectStatusLabel(project.status)}
                              </Typography>
                            </TableCell>
                            {/* Ship Date */}
                            <TableCell>
                              {project?.shipdate ? (
                                <>
                                  <Typography noWrap color="text.secondary">
                                    {format(
                                      new Date(project.shipdate),
                                      'MM/dd/yyyy'
                                    )}
                                  </Typography>
                                </>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell>
                            {/* Start Date */}
                            <TableCell>
                              {project?.startdate ? (
                                <Typography noWrap color="text.secondary">
                                  {format(
                                    new Date(project.startdate),
                                    'MM/dd/yyyy'
                                  )}
                                </Typography>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell>
                            {/* End Date */}
                            <TableCell>
                              {project?.enddate ? (
                                <Typography noWrap color="text.secondary">
                                  {format(
                                    new Date(project.enddate),
                                    'MM/dd/yyyy'
                                  )}
                                </Typography>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell>
                            {/* Material Arrival */}
                            {/* <TableCell>
                              {project?.materialarrival ? (
                                <Typography noWrap color="text.secondary">
                                  {format(
                                    new Date(project.materialarrival),
                                    'MM/dd/yyyy'
                                  )}
                                </Typography>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell> */}
                            {/* Material Pickup */}
                            {/* <TableCell>
                              {project?.materialpickup ? (
                                <Typography noWrap color="text.secondary">
                                  {format(
                                    new Date(project.materialpickup),
                                    'MM/dd/yyyy'
                                  )}
                                </Typography>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell> */}
                            {/* Return Date */}
                            <TableCell>
                              {project?.projreturn ? (
                                <Typography noWrap color="text.secondary">
                                  {format(
                                    new Date(project.projreturn),
                                    'MM/dd/yyyy'
                                  )}
                                </Typography>
                              ) : (
                                <Typography noWrap color="text.secondary">
                                  {t('No Date Set')}
                                </Typography>
                              )}
                            </TableCell>

                            {/* <TableCell>
                            <Box display="flex" justifyContent="flex-start">
                              {project.memberIds.length > 0 && (
                                <AvatarGroup max={4}>
                                  {project.memberIds.map((member) => (
                                    <Tooltip
                                      arrow
                                      placement="top"
                                      key={member.id}
                                      title={member.name}
                                    >
                                      <Avatar
                                        sx={{
                                          width: 30,
                                          height: 30
                                        }}
                                        key={member.id}
                                        src={member.avatar}
                                      />
                                    </Tooltip>
                                  ))}
                                </AvatarGroup>
                              )}
                            </Box>
                          </TableCell> */}
                            {/* <TableCell align="center">
                            <Box
                              sx={{
                                minWidth: 175
                              }}
                              display="flex"
                              alignItems="center"
                            >
                              <LinearProgress
                                sx={{
                                  flex: 1,
                                  mr: 1
                                }}
                                value={parseFloat(project.progress)}
                                color="primary"
                                variant="determinate"
                              />
                              <Typography variant="subtitle1">
                                {project.progress}
                              </Typography>
                            </Box>
                          </TableCell> */}
                            <TableCell align="center">
                              <Typography noWrap>
                                {/* <Tooltip title={t('View')} arrow> */}
                                {/*  <IconButton onClick={() => navigate(`/management/projects/single/${project.internalid}`)} color="primary"> */}
                                {/*    <LaunchTwoToneIcon fontSize="small" /> */}
                                {/*  </IconButton> */}
                                {/* </Tooltip> */}
                                {currentProject?.id === project.id ? (
                                  <Tooltip title={t('Cart has been set')} arrow>
                                    <IconButton color="success">
                                      <CheckTwoTone fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                ) : (
                                  <Tooltip title={t('Set Cart')} arrow>
                                    <IconButton
                                      onClick={() =>
                                        handleProjectSetForCart(project)
                                      }
                                      color="primary"
                                    >
                                      <ShoppingCartCheckoutTwoToneIcon fontSize="small" />
                                    </IconButton>
                                  </Tooltip>
                                )}
                              </Typography>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                  </TableBody>
                </Table>
              </TableContainer>
              <Box p={2}>
                <TablePagination
                  component="div"
                  count={filteredProjects.length}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleLimitChange}
                  page={page}
                  rowsPerPage={limit}
                  rowsPerPageOptions={[5, 10, 15, 20, 25, 50]}
                />
              </Box>
            </>
          )}
        </Card>
      )}
      {toggleView === 'grid_view' && (
        <>
          {paginatedProjects.length !== 0 && (
            <Card
              sx={{
                p: 2,
                mb: 3
              }}
            >
              <Box
                display="flex"
                alignItems="center"
                justifyContent="space-between"
              >
                <>
                  <Box display="flex" alignItems="center">
                    <Tooltip
                      arrow
                      placement="top"
                      title={t('Select all projects')}
                    >
                      <Checkbox
                        checked={selectedAllProjects}
                        indeterminate={selectedSomeProjects}
                        onChange={handleSelectAllProjects}
                      />
                    </Tooltip>
                  </Box>
                  {selectedBulkActions && (
                    <Box flex={1} pl={2}>
                      <BulkActions />
                    </Box>
                  )}
                  {!selectedBulkActions && (
                    <TablePagination
                      component="div"
                      count={filteredProjects.length}
                      onPageChange={handlePageChange}
                      onRowsPerPageChange={handleLimitChange}
                      page={page}
                      rowsPerPage={limit}
                      rowsPerPageOptions={[5, 10, 15]}
                    />
                  )}
                </>
              </Box>
            </Card>
          )}
          {paginatedProjects.length === 0 ? (
            <Typography
              sx={{
                py: 10
              }}
              variant="h3"
              fontWeight="normal"
              color="text.secondary"
              align="center"
            >
              {t("We couldn't find any projects matching your search criteria")}
            </Typography>
          ) : (
            <>
              <Grid container spacing={3}>
                {paginatedProjects.map((project) => {
                  const isProjectSelected = selectedItems.includes(project.id);

                  return (
                    <Grid item xs={12} sm={6} md={4} key={project.project}>
                      <CardWrapper
                        className={clsx({
                          'Mui-selected': isProjectSelected
                        })}
                      >
                        <Box
                          sx={{
                            position: 'relative',
                            zIndex: '2'
                          }}
                        >
                          <Box
                            pl={2}
                            py={1}
                            pr={1}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                          >
                            <Checkbox
                              checked={isProjectSelected}
                              onChange={(event) =>
                                handleSelectOneProject(event, project.id)
                              }
                              value={isProjectSelected}
                            />
                          </Box>
                          <Divider />
                          <Box p={2}>
                            {getProjectStatusLabel(project.status)}

                            <Typography
                              sx={{
                                mt: 2
                              }}
                              variant="h4"
                              gutterBottom
                            >
                              {project.project}
                            </Typography>
                            <Typography noWrap variant="subtitle2">
                              <span>
                                <Link
                                  component={RouterLink}
                                  to={`/management/projects/single/${project?.internalid}`}
                                >
                                  {project.custentity_ng_eh_booth_description}
                                </Link>
                              </span>
                            </Typography>
                          </Box>
                          <Box
                            px={2}
                            display="flex"
                            alignItems="flex-end"
                            justifyContent="space-between"
                          >
                            <Box>
                              {project.shipdate && (
                                <Typography variant="subtitle2">
                                  {t('Ship Date')}:{' '}
                                  <Text color="black">
                                    {project.shipdate &&
                                      format(
                                        new Date(project.shipdate),
                                        'MM/dd/yyyy'
                                      )}{' '}
                                  </Text>
                                </Typography>
                              )}
                              {project.materialarrival && (
                                <Typography variant="subtitle2">
                                  {t('Material Arrival')}:{' '}
                                  <Text color="black">
                                    {project.materialarrival &&
                                      format(
                                        new Date(project.materialarrival),
                                        'MM/dd/yyyy'
                                      )}{' '}
                                  </Text>
                                </Typography>
                              )}
                              {project.materialpickup && (
                                <Typography variant="subtitle2">
                                  {t('Material Pickup')}:{' '}
                                  <Text color="black">
                                    {project.materialpickup &&
                                      format(
                                        new Date(project.materialpickup),
                                        'MM/dd/yyyy'
                                      )}{' '}
                                  </Text>
                                </Typography>
                              )}
                              {project.returndate && (
                                <Typography variant="subtitle2">
                                  {t('Return Date')}:{' '}
                                  <Text color="black">
                                    {project.returndate &&
                                      format(
                                        new Date(project.returndate),
                                        'MM/dd/yyyy'
                                      )}{' '}
                                  </Text>
                                </Typography>
                              )}
                            </Box>
                          </Box>

                          <Box px={2} pb={2} display="flex" alignItems="center">
                            <LinearProgress
                              sx={{
                                flex: 1,
                                mr: 1
                              }}
                              value={parseFloat(project.progress)}
                              color="primary"
                              variant="determinate"
                            />
                            <Typography variant="subtitle1">
                              {project.progress}
                            </Typography>
                          </Box>
                          <Divider />
                          <Box
                            p={2}
                            display="flex"
                            alignItems="center"
                            justifyContent="space-between"
                          >
                            <Box>
                              <Button
                                sx={{
                                  mr: 1
                                }}
                                size="small"
                                variant="contained"
                                color="primary"
                              >
                                {t('View')}
                              </Button>
                              {currentProject?.id === project.id ? (
                                <Tooltip title={t('Cart has been set')} arrow>
                                  <IconButton color="success">
                                    <CheckTwoTone fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              ) : (
                                <Tooltip title={t('Set Cart')} arrow>
                                  <IconButton
                                    onClick={() =>
                                      handleProjectSetForCart(project)
                                    }
                                    color="primary"
                                  >
                                    <ShoppingCartCheckoutTwoToneIcon fontSize="small" />
                                  </IconButton>
                                </Tooltip>
                              )}
                            </Box>
                          </Box>
                        </Box>
                      </CardWrapper>
                    </Grid>
                  );
                })}
              </Grid>
              <Card
                sx={{
                  p: 2,
                  mt: 3,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <Box>
                  <Typography component="span" variant="subtitle1">
                    {t('Showing')}
                  </Typography>{' '}
                  <b>{limit}</b> {t('of')} <b>{filteredProjects.length}</b>{' '}
                  <b>{t('projects')}</b>
                </Box>
                <TablePagination
                  component="div"
                  count={filteredProjects.length}
                  onPageChange={handlePageChange}
                  onRowsPerPageChange={handleLimitChange}
                  page={page}
                  rowsPerPage={limit}
                  labelRowsPerPage=""
                  rowsPerPageOptions={[5, 10, 15]}
                />
              </Card>
            </>
          )}
        </>
      )}
      {!toggleView && (
        <Card
          sx={{
            textAlign: 'center',
            p: 3
          }}
        >
          <Typography
            align="center"
            variant="h4"
            fontWeight="normal"
            color="text.secondary"
            sx={{
              my: 5
            }}
            gutterBottom
          >
            {t(
              'Choose between table or grid views for displaying the projects list.'
            )}
          </Typography>
        </Card>
      )}

      <DialogWrapper
        open={openConfirmDelete}
        maxWidth="sm"
        fullWidth
        TransitionComponent={Transition}
        keepMounted
        onClose={closeConfirmDelete}
      >
        <Box
          display="flex"
          alignItems="center"
          justifyContent="center"
          flexDirection="column"
          p={5}
        >
          <AvatarError>
            <CloseIcon />
          </AvatarError>

          <Typography
            align="center"
            sx={{
              pt: 4,
              px: 6
            }}
            variant="h3"
          >
            {t('Do you really want to delete this project')}?
          </Typography>

          <Typography
            align="center"
            sx={{
              pt: 2,
              pb: 4,
              px: 6
            }}
            fontWeight="normal"
            color="text.secondary"
            variant="h4"
          >
            {t("You won't be able to revert after deletion")}
          </Typography>

          <Box>
            <Button
              variant="text"
              size="large"
              sx={{
                mx: 1
              }}
              onClick={closeConfirmDelete}
            >
              {t('Cancel')}
            </Button>
            <ButtonError
              onClick={handleDeleteCompleted}
              size="large"
              sx={{
                mx: 1,
                px: 3
              }}
              variant="contained"
            >
              {t('Delete')}
            </ButtonError>
          </Box>
        </Box>
      </DialogWrapper>
    </>
  );
};

Results.propTypes = {
  projects: PropTypes.array.isRequired
};

Results.defaultProps = {
  projects: []
};

export default Results;
