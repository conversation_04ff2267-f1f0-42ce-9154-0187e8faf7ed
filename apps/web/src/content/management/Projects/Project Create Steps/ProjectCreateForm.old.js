import { motion } from 'framer-motion';
import {
  Box,
  <PERSON>ton,
  Card,
  CardContent,
  CircularProgress,
  Container,
  Grid,
  IconButton,
  Step,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>lt<PERSON>,
  Typography,
  useMediaQuery,
  <PERSON>m,
  Alert,
  <PERSON>ertT<PERSON>le,
  Link
} from '@mui/material';
import {
  ElectricalServices,
  Info,
  LocalShipping,
  CheckCircle,
  Description
} from '@mui/icons-material';
import React, {
  Fragment,
  memo,
  useContext,
  useEffect,
  useState,
  useMemo
} from 'react';
import { useTheme } from '@mui/material/styles';
import * as Yup from 'yup';
import { Formik } from 'formik';
import axios from 'axios';
import EventInformation from './steps/EventInformation.js';
import ShippingInformation from './steps/ShippingInformation.js';
import ShowServices from './steps/ShowServices.js';
import useAuth from '../../../../hooks/useAuth.js';
import { SettingsContext } from '../../../../contexts/SettingsContext.js';
import { useSnackbar } from 'notistack';
import { useTranslation } from 'react-i18next';
import { useDropzone } from 'react-dropzone';
import { useNavigate, useParams } from 'react-router-dom';
import ArrowBackTwoToneIcon from '@mui/icons-material/ArrowBackTwoTone';
import useRefMounted from '../../../../hooks/useRefMounted.js';
import useHoistedUrls from '../../../../hooks/useHoistedUrls.js';
import {
  TabbedFields,
  StackedFields,
  fieldPresent
} from '../../../../utils/formik-utils.js';
import DynamicFormikField from 'src/components/dynamicFields/DynamicFormikField';
import getValidationSchema from 'src/components/dynamicFields/dynamicFieldYupValidation.jsx';
import FieldHelpPopper from 'src/components/dynamicFields/FieldHelpPopper.jsx';

const ProjectCreateStepper = memo(({ formik, activeStep, setActiveStep }) => {
  const [skipped, setSkipped] = useState(new Set());
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const isStepOptional = (step) => {
    const optionalSteps = [];

    return optionalSteps.includes(step);
  };

  const isStepSkipped = (step) => {
    return skipped.has(step);
  };

  /**
   * Validate individual fields using the formik.validateField method
   * @param {Array} fields - Array of field names to validate
   * */
  const validateFields = (fields) => {
    if (Array.isArray(fields)) {
      fields.forEach((field) => {
        formik.validateField(field);
      });
    }
  };

  const handleNext = (e) => {
    e.stopPropagation();

    let newSkipped = skipped;
    if (isStepSkipped(activeStep)) {
      newSkipped = new Set(newSkipped.values());
      newSkipped.delete(activeStep);
    }
    let errorsPresent = [];

    let eventInformationFields = [
      'projname'
      // "exhibitCompany",
      // "exhibitContact",
      // "boothNumber",
      // "exhibitContactPhone",
    ];
    let shippingInformationFields = [
      // "numberOfSeparateBoothShipments",
      // "company",
      // "contact",
      // "contactPhone",
      // "address",
      // "city",
      // "state",
      // "zipCode",
      // "country",
    ];
    let showServicesFields = [
      // "carrierName",
      // "typeOfService"
    ];

    console.log('Errors present: ', formik.errors);
    console.log('Values present: ', formik.values);
    let erroredFields = Object.keys(formik.errors);

    console.log('Errored fields: ', erroredFields);
    let eventInformationValidated = [];
    let shippingInformationValidated = [];
    let showServicesValidated = [];

    switch (activeStep) {
      case 0: // Event Information
        validateFields(eventInformationFields);

        break;
      case 1: // Shipping Information
        validateFields(shippingInformationFields);

        break;
      case 2: // Show Services
        validateFields(showServicesFields);

        break;
      default:
        console.log('No validation for this step:', activeStep);
    }

    eventInformationValidated = eventInformationFields.filter((field) =>
      erroredFields.includes(field)
    );

    if (eventInformationValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 0
      });
    }
    shippingInformationValidated = shippingInformationFields.filter((field) =>
      erroredFields.includes(field)
    );

    if (shippingInformationValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 1
      });
    }
    showServicesValidated = showServicesFields.filter((field) =>
      erroredFields.includes(field)
    );

    if (showServicesValidated.length !== 0) {
      errorsPresent.push({
        error: true,
        step: 2
      });
    }

    console.log('Errors built: ', errorsPresent);

    let firstErrorAt =
      errorsPresent.length !== 0
        ? Math.min(...errorsPresent.map((error) => error.step))
        : -1;

    console.log('First error at: ', firstErrorAt);

    let lastStep = activeStep === steps.length - 1;
    if (lastStep) {
      console.log('Submitting form for validation...');
      if (firstErrorAt !== -1) {
        setActiveStep(firstErrorAt);
      }
    } else if (firstErrorAt !== -1 && activeStep >= firstErrorAt) {
      console.log('Error at:', firstErrorAt);
      setActiveStep(firstErrorAt);
    } else {
      e.preventDefault();
      setActiveStep(activeStep + 1);
      setSkipped(newSkipped);
    }
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleSkip = () => {
    if (!isStepOptional(activeStep)) {
      // You probably want to guard against something like this,
      // it should never occur unless someone's actively trying to break something.
      throw new Error("You can't skip a step that isn't optional.");
    }

    setActiveStep((prevActiveStep) => prevActiveStep + 1);
    setSkipped((prevSkipped) => {
      const newSkipped = new Set(prevSkipped.values());
      newSkipped.add(activeStep);
      return newSkipped;
    });
  };

  const handleReset = () => {
    setActiveStep(0);
  };

  const lastStep = activeStep === steps.length - 1;

  return (
    <Box sx={{ width: '100%' }}>
      <Stepper activeStep={activeStep}>
        {steps.map((step, index) => {
          const stepProps = {};
          const labelProps = {};
          if (isStepOptional(index)) {
            labelProps.optional = (
              <Typography variant="caption">Optional</Typography>
            );
          }
          if (isStepSkipped(index)) {
            stepProps.completed = false;
          }
          return (
            <Step key={step.id} {...stepProps}>
              <StepLabel icon={step.icon} {...labelProps}>
                {step.label}
              </StepLabel>
            </Step>
          );
        })}
      </Stepper>
      {activeStep === steps.length ? (
        <>
          <Typography sx={{ mt: 2, mb: 1 }}>
            All steps completed - you&apos;re finished
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Box sx={{ flex: '1 1 auto' }} />
            <Button onClick={handleReset}>Reset</Button>
          </Box>
        </>
      ) : (
        <>
          <Box sx={{ display: 'flex', flexDirection: 'row', pt: 2 }}>
            <Box sx={{ flex: '1 1 auto' }}>
              {activeStep === 0 && <EventInformation key={0} formik={formik} />}
              {activeStep === 1 && (
                <ShippingInformation key={1} formik={formik} />
              )}
              {activeStep === 2 && <ShowServices key={2} formik={formik} />}

              <Box display="flex" justifyContent="space-between">
                <Button
                  color="inherit"
                  disabled={activeStep === 0}
                  onClick={handleBack}
                  sx={{ mr: 1 }}
                >
                  Back
                </Button>

                <Box sx={{ pt: 1 }}>
                  {isStepOptional(activeStep) && (
                    <Button color="inherit" onClick={handleSkip} sx={{ mr: 1 }}>
                      Skip
                    </Button>
                  )}
                  <NextButton
                    handleNext={handleNext}
                    activeStep={activeStep}
                    formik={formik}
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        </>
      )}
    </Box>
  );
});

const NextButton = memo(({ formik, activeStep, handleNext }) => {
  return (
    <Button
      type={activeStep === steps.length - 1 ? 'submit' : 'button'}
      startIcon={formik.isSubmitting ? <CircularProgress size="1rem" /> : null}
      disabled={Boolean(formik.errors.submit) || formik.isSubmitting}
      onClick={handleNext}
      variant={activeStep === steps.length - 1 ? 'contained' : 'outlined'}
    >
      {activeStep === steps.length - 1 ? 'Submit' : 'Next'}
    </Button>
  );
});

const ProjectCreateForm = memo(({ open, setOpen, copy }) => {
  const [settings] = useContext(SettingsContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const navigate = useNavigate();
  const { projectId } = useParams();
  const { t } = useTranslation();
  const { user, userMutate, currentProject, setProject } = useAuth();
  const isMountedRef = useRefMounted();
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [projectForms, setProjectForms] = useState(null);
  const [projectFormId, setProjectFormId] = useState(null);
  const fetcher = (url) => axios.get(url).then((res) => res.data);

  const formValidationSchema = useMemo(() => {
    const baseValidations = {
      items: Yup.array().required(
        t('Items are required in order to create an order')
      )
    };

    let customValidations = {};
    if (customFields) {
      customValidations = Object.fromEntries(
        customFieldsForUse
          .filter((field) => field.validations.length > 0)
          .map((field) => [
            field.name,
            getValidationSchema(field.validations, t)
          ])
      );
    }

    let shipAddressValidations = {};
    if (fieldPresent(customFields, 'shipAddress')) {
      shipAddressValidations = {
        ship_to_address: Yup.object().required(t('This field is required'))
      };
    }

    let defaultValidations = {};
    if (!customFields) {
      defaultValidations = {
        ship_to_address: Yup.object().required(t('This field is required')),
        po_number: Yup.string().max(255),
        comments: Yup.string().max(255)
      };
    }

    return Yup.object().shape({
      ...baseValidations,
      ...customValidations,
      ...shipAddressValidations,
      ...defaultValidations
    });
  }, [customFields]);

  const getProjectFormDataUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_project_form_data_external
      : getDeploymentUrl.get_project_form_data_internal;

  // TODO: Use SWR to load the project forms for rendering.
  const {
    data: projectFormsData,
    loading: projectFormsLoading,
    error: projectFormsError
  } = useSWR(
    projectFormId
      ? `${getProjectFormDataUrl}&formId=${projectFormId}`
      : `${getProjectFormDataUrl}`,
    fetcher
  );

  const { CustomFields, useTabs } = useMemo(() => {
    const useTabs = formLayout === 'tab' && !mobile;
    const CustomFields = useTabs ? TabbedFields : StackedFields;
    return { CustomFields, useTabs };
  }, [formLayout]);

  const [activeStep, setActiveStep] = useState(0);
  const [copyingProject, setCopyingProject] = useState([]);
  const [newProject, setNewProject] = useState(null);

  console.log('PROJECTIDS', projectId);

  console.log('THEUSERR', user);

  useEffect(() => {
    if (isMountedRef && user && user?.profile) {
      if (projectId) {
        let foundProject = user?.profile?.projects?.list.filter((project) => {
          return project?.id === projectId;
        });
        console.log('foundProject', foundProject);
        if (foundProject.length !== 0) {
          setCopyingProject(foundProject[0]?.project);
        }
      } else {
        setCopyingProject(currentProject?.project);
      }
    }
  }, [user]);

  const ForwardButton = React.forwardRef((props, ref) => (
    <Button ref={ref} {...props}>
      {props.children}
    </Button>
  ));

  const MotionMuiButton = motion(ForwardButton);

  const {
    acceptedFiles
    // isDragActive,
    // isDragAccept,
    // isDragReject,
    // getRootProps,
    // getInputProps
  } = useDropzone({
    accept:
      'image/jpeg, image/png, text/plain, application/pdf, application/docx, .gltf, .glb'
  });

  const handleCreateProjectSuccess = (
    values,
    setStatus,
    setSubmitting,
    resetForm
  ) => {
    setSubmitting(true);
    console.log('Values:', values);
    // eslint-disable-next-line camelcase
    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    console.log('Files Added?', acceptedFiles);

    const formData = new FormData();
    let allFileNames = acceptedFiles.map((fil, i) => `file${i}`);
    formData.append('fileObjNames', JSON.stringify(allFileNames));
    if (acceptedFiles && acceptedFiles.length !== 0) {
      // eslint-disable-next-line array-callback-return
      acceptedFiles.map((fl, i) => {
        formData.append(`file${i}`, fl);
      });
    }

    if (Object.keys(values).length !== 0) {
      let formInputValues = Object.keys(values);
      formInputValues.forEach((key) => {
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else {
          formData.append(key, values[key]);
        }
      });
    }

    let requestOptions = {
      method: 'POST',
      redirect: 'follow',
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    let action = '';
    if (projectId) {
      action = 'COPYPROJ';
      formData.append('projectId', projectId);
    } else {
      action = 'PROJCREATE';
    }

    return new Promise(() => {
      // eslint-disable-next-line camelcase
      axios
        .post(
          // eslint-disable-next-line camelcase
          `${get_record_function_url}&type=${action}`,
          formData,
          requestOptions
        )
        .then((res) => {
          // res.data;

          let newProject;

          if (res.data.message.includes('created')) {
            newProject = res.data.projectId;
            resetForm();
            setStatus({ success: true });
            setSubmitting(false);
            // eslint-disable-next-line camelcase
            const user_suitelet =
              process.env.NODE_ENV === 'development'
                ? process.env.REACT_APP_GET_USER_SUITELET
                : window.env.REACT_APP_GET_USER_SUITELET;
            // userMutate(user_suitelet);
            console.log('Created success!', user.profile.projects);

            enqueueSnackbar(t('A new project has been created successfully'), {
              variant: 'success',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          } else if (res.data.saved) {
            setSubmitting(false);
            enqueueSnackbar(
              t('New project failed to add files but created successfully'),
              {
                variant: 'warning',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'right'
                },
                TransitionComponent: Zoom
              }
            );
          } else {
            setSubmitting(false);
            enqueueSnackbar(t('New project failed to save'), {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          }

          // eslint-disable-next-line camelcase
          const user_suitelet =
            process.env.NODE_ENV === 'development'
              ? process.env.REACT_APP_GET_USER_SUITELET
              : window.env.REACT_APP_GET_USER_SUITELET;

          return axios.get(user_suitelet).then((res) => {
            enqueueSnackbar(t('Redirecting to Items'), {
              variant: 'success',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });

            let projects = res.data.profile.projects.list;

            if (projects.length !== 0) {
              const newProjectFound = projects.filter((proj) => {
                if (Number(proj.id) === Number(newProject)) {
                  return proj;
                }
              });

              if (newProjectFound.length !== 0) {
                setProject(newProjectFound[0]);
                navigate(`/inventory/nonrentals`);
              }
            }

            return res;
          });
        })
        .catch((err) => {
          setStatus({ success: false });
          setSubmitting(false);
          console.error(err);
          enqueueSnackbar(t('New project failed to create'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
          Promise.reject(err);
        })
        .finally(() => {
          console.log('Finally');
        });
    });
  };

  return (
    <>
      <Box
        display="flex"
        mb={1}
        sx={{
          padding: 2,
          backgroundColor: `${
            theme.palette.mode === 'dark'
              ? theme.colors.alpha.trueWhite[10]
              : theme.colors.alpha.white[70]
          }`,
          borderRadius: 1
        }}
      >
        <Tooltip arrow placement="top" title={t('Go back')}>
          <IconButton
            onClick={() => navigate('/management/projects')}
            color="primary"
            sx={{
              p: 2,
              mr: 2
            }}
          >
            <ArrowBackTwoToneIcon />
          </IconButton>
        </Tooltip>
        {projectId ? (
          <Box alignItems="center">
            <Typography variant="h3" component="h3">
              Enter Project Details
            </Typography>
            <Typography variant="subtitle2">
              {t(`Copying From Project: ${copyingProject}`)}
            </Typography>
          </Box>
        ) : (
          <Box alignItems="center">
            <Typography
              variant="h3"
              component="h3"
              sx={{
                p: 2,
                mr: 2
              }}
            >
              Enter Project Details
            </Typography>
          </Box>
        )}
      </Box>
      <Container sx={{ paddingTop: '3vh' }}>
        <motion.div
          key="addresses-tab"
          initial={{
            x: 22,
            opacity: 0
          }}
          animate={{
            opacity: 1,
            x: 0
          }}
          transition={{
            duration: 0.5
          }}
        >
          <Grid container spacing={2}>
            <Grid item xs={12}>
              <Card>
                <CardContent>
                  <Box
                    sx={{
                      backgroundColor: 'background.paper',
                      minHeight: '100%',
                      p: 3
                    }}
                  >
                    <Formik
                      initialValues={{
                        projname: '',
                        eventName: '',
                        eventwebsite: '',
                        eventvenuenumber: '',
                        boothnumber: '',
                        boothsize: '',
                        maincontactnameandphone: '',
                        advwarehouselocation: '',
                        decoratorurl: '',
                        decoratorclientusername: '',
                        decoratorclientuserpassword: '',
                        carrierCheckInTime: null,
                        materialarrival: null,
                        materialpickup: '',
                        moveindate: null,
                        moveInTime: null,
                        moveOutTime: null,
                        moveoutdate: null,
                        exhibitSpace: '',
                        electricity: '',
                        generalContractorValue: '',
                        shipToAddressValue: '',
                        showServicesOrderedByValue: '',
                        otherShowServiceRequests: '',
                        cardForShowServicesValue: '',
                        idLaborByValue: '',
                        laborBy: '',
                        unionLaborValue: '',
                        installationAndDismantle: '',
                        materialAndHandling: '',
                        shippingaddress: '',
                        toshippinginstructions: '',
                        pickupAddress: '',
                        fromshowShippingInstructions: '',
                        laborCheckbox: false,
                        internetCheckbox: false,
                        carpetAndPaddingCheckbox: false,
                        leadRetrievalCheckbox: false,
                        electricalCheckbox: false,
                        electricalTextbox: '',
                        furnitureCheckbox: false,
                        furtnitureTextbox: '',
                        riggingCheckbox: false,
                        avCheckbox: false,
                        avTextbox: '',
                        materialHandlingCheckbox: false,
                        materialHandlingTextbox: '',
                        otherCheckbox: false,
                        otherTextbox: '',
                        projectBlurb: '',
                        duration: [null, null],
                        submit: null
                      }}
                      validationSchema={Yup.object().shape({
                        projname: Yup.string()
                          .max(
                            80,
                            'Project Name must be at most of 80 characters'
                          )
                          .required(t('The Project Name field is required')),
                        materialarrival: Yup.date()
                          .nullable()
                          .required(
                            t('The Material Arrival field is required')
                          ),
                        duration: Yup.array()
                          .of(
                            Yup.date()
                              .nullable()
                              .required('Both start and end dates are required')
                          )
                          .test(
                            'both-dates-required',
                            'Both start and end dates are required',
                            function (value) {
                              const [start, end] = value || [];
                              if (!start || !end) return false; // Ensure both dates are present
                              return true;
                            }
                          )
                        // materialpickup: Yup.string().max(255),
                        // moveindate: Yup.string(),
                        // moveoutdate: Yup.string(),
                        // boothsize: Yup.string().max(255),
                        // shippingaddress: Yup.string().max(255)
                      })}
                      onSubmit={async (
                        _values,
                        { resetForm, setErrors, setStatus, setSubmitting }
                      ) => {
                        try {
                          await handleCreateProjectSuccess(
                            _values,
                            setStatus,
                            setSubmitting,
                            resetForm
                          );
                        } catch (err) {
                          console.error(err);
                          setStatus({ success: false });
                          setErrors({ submit: err.message });
                          setSubmitting(false);
                        }
                      }}
                    >
                      {({
                        errors,
                        handleBlur,
                        handleChange,
                        handleSubmit,
                        isSubmitting,
                        touched,
                        values,
                        setFieldValue,
                        validateForm,
                        validateField
                      }) => (
                        <form onSubmit={handleSubmit}>
                          <ProjectCreateStepper
                            activeStep={activeStep}
                            setActiveStep={setActiveStep}
                            formik={{
                              errors,
                              handleBlur,
                              handleChange,
                              handleSubmit,
                              isSubmitting,
                              touched,
                              values,
                              setFieldValue,
                              validateForm,
                              validateField
                            }}
                          />
                        </form>
                      )}
                    </Formik>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </motion.div>
      </Container>
    </>
  );
});

const steps = [
  {
    id: 1,
    label: 'Form Selection',
    description: 'Select a form to use for this project',
    icon: <Info />
  },
  {
    id: 2,
    label: 'Shipping Information',
    description: 'Fill in Shipping Information',
    icon: <LocalShipping />
  },
  {
    id: 3,
    label: 'Show Services',
    description: 'Fill in Show Service Details',
    icon: <ElectricalServices />
  }
];

export default ProjectCreateForm;
