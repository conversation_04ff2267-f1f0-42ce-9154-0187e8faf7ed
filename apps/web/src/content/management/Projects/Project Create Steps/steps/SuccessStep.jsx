import { Box, Button, Typography, Paper, useTheme, alpha } from '@mui/material';
import { motion } from 'framer-motion';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import EditIcon from '@mui/icons-material/Edit';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';

const SuccessStep = ({ projectId, onReset, mode }) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Dynamic messages based on mode
  const getSuccessMessage = () => {
    if (mode === 'edit') {
      return {
        title: t('Project Updated Successfully!'),
        subtitle: t('Your project has been updated successfully.')
      };
    } else if (mode === 'copy') {
      return {
        title: t('Project Copied Successfully!'),
        subtitle: t('Your project has been copied successfully.')
      };
    }
    return {
      title: t('Project Created Successfully!'),
      subtitle: t('Your project has been created successfully.')
    };
  };

  const { title, subtitle } = getSuccessMessage();

  // Get icon and color based on mode
  const getIconAndColor = () => {
    switch (mode) {
      case 'edit':
        return {
          icon: EditIcon,
          color: theme.palette.info.main,
          bgColor: alpha(theme.palette.info.main, 0.1)
        };
      case 'copy':
        return {
          icon: ContentCopyIcon,
          color: theme.palette.secondary.main,
          bgColor: alpha(theme.palette.secondary.main, 0.1)
        };
      default:
        return {
          icon: CheckCircleIcon,
          color: theme.palette.success.main,
          bgColor: alpha(theme.palette.success.main, 0.1)
        };
    }
  };

  const { icon: IconComponent, color, bgColor } = getIconAndColor();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Paper
        elevation={3}
        sx={{
          p: 4,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          maxWidth: 600,
          mx: 'auto',
          my: 4,
          textAlign: 'center',
          background:
            theme.palette.mode === 'dark'
              ? `linear-gradient(135deg, ${alpha(color, 0.05)} 0%, ${alpha(
                  color,
                  0.02
                )} 100%)`
              : `linear-gradient(135deg, ${alpha(color, 0.08)} 0%, ${alpha(
                  color,
                  0.03
                )} 100%)`,
          border: `1px solid ${alpha(color, 0.2)}`
        }}
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{
            delay: 0.2,
            type: 'spring',
            stiffness: 200,
            damping: 15
          }}
        >
          <Box
            sx={{
              width: 100,
              height: 100,
              borderRadius: '50%',
              bgcolor: bgColor,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              mb: 3,
              boxShadow: `0 4px 20px ${alpha(color, 0.2)}`
            }}
          >
            <IconComponent
              sx={{
                fontSize: 56,
                color: color
              }}
            />
          </Box>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <Typography
            variant="h4"
            component="h2"
            gutterBottom
            sx={{ fontWeight: 600 }}
          >
            {title}
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <Typography variant="body1" color="text.secondary" paragraph>
            {subtitle}
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5, duration: 0.5 }}
          style={{ width: '100%' }}
        >
          <Box
            sx={{
              mt: 3,
              display: 'flex',
              gap: 2,
              width: '100%',
              justifyContent: 'center'
            }}
          >
            <Button
              variant="contained"
              color="primary"
              onClick={() =>
                navigate(`/management/projects/single/${projectId}`)
              }
              sx={{ minWidth: 200 }}
            >
              {mode === 'edit' ? t('View Updated Project') : t('View Project')}
            </Button>
            {mode !== 'edit' && (
              <Button
                variant="outlined"
                color="primary"
                onClick={onReset}
                sx={{ minWidth: 200 }}
              >
                {mode === 'copy' ? t('Copy Another') : t('Create Another')}
              </Button>
            )}
          </Box>
        </motion.div>
      </Paper>
    </motion.div>
  );
};

SuccessStep.propTypes = {
  projectId: PropTypes.string.isRequired,
  onReset: PropTypes.func.isRequired,
  mode: PropTypes.oneOf(['create', 'edit', 'copy'])
};

SuccessStep.defaultProps = {
  mode: 'create'
};

export default SuccessStep;
