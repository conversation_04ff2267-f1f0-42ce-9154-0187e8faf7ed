import { memo, useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import 'react-quill/dist/quill.snow.css';

import {
  Box,
  DialogContent,
  Divider,
  FormControl,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  TextField,
  Typography,
  useTheme
} from '@mui/material';

import { SettingsContext } from '../../../../../contexts/SettingsContext';
import { LocalizationProvider } from '@mui/lab';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import DatePicker from '@mui/lab/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';

const ShippingInformation = memo(({ formik }) => {
  const { t } = useTranslation();
  const [settings] = useContext(SettingsContext);
  const theme = useTheme();

  const [shipToAddress, setShipToAddress] = useState(null);
  const [matArrivalDate, setmatArrivalDate] = useState(null);
  const shipToAddressChange = (event) => {
    setShipToAddress(event.target.value);
  };

  console.log('TESTmatArrivalDate', matArrivalDate);
  return (
    <Grid container spacing={1}>
      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          pr={3}
          sx={{
            pt: `${theme.spacing(4)}`,
            pb: { xs: 1, md: 0 }
          }}
          alignSelf="center"
        >
          <Typography variant="h3">{t('Shipping Information')}</Typography>
        </Box>
        <Box
          alignSelf="center"
          sx={{
            pt: `${theme.spacing(1)}`,
            pb: { xs: 1, md: 2 }
          }}
        >
          <Divider />
        </Box>
      </Grid>

      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <Grid container spacing={10} alignItems="center">
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <FormControl fullWidth>
              <InputLabel id="shipToAddressInputLabel">
                Ship to Address
              </InputLabel>
              <Select
                labelId="shipToAddressLabel"
                id="shipToAddress"
                value={formik.values.shipToAddressValue}
                onChange={(event) => {
                  shipToAddressChange(event);
                  formik.setFieldValue(
                    'shipToAddressValue',
                    event.target.value
                  );
                }}
                label="Ship to Address"
              >
                <MenuItem value="">
                  <em>None</em>
                </MenuItem>
                {settings.shipToAddressList.map((item) => (
                  <MenuItem key={item.id} value={item.id}>
                    {item.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid
            sx={{
              mb: `${theme.spacing(1)}`
            }}
            item
            xs={6}
            md={6}
          >
            <DatePicker
              fullWidth
              label="Material Arrival Date"
              value={formik.values.materialarrival}
              onChange={(newValue) => {
                formik.setFieldValue('materialarrival', newValue);
                console.log('NEWVALUEHERE', newValue);
                console.log('NEWVALUEHERETYPE', typeof newValue);
              }}
              validationError={Boolean(
                formik.touched.materialarrival && formik.errors.materialarrival
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.materialarrival &&
                    formik.errors.materialarrival
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  name="materialarrival"
                  value={formik.values.materialarrival}
                  error={Boolean(
                    formik.touched.materialarrival &&
                      formik.errors.materialarrival
                  )}
                  helperText={
                    (formik.touched.materialarrival &&
                      formik.errors.materialarrival) ||
                    `* Required`
                  }
                  placeholder={t('Select arrival date...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
        </Grid>

        <Grid container spacing={10} alignItems="center">
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <TextField
              error={Boolean(
                formik.touched.shippingaddress && formik.errors.shippingaddress
              )}
              fullWidth
              helperText={
                formik.touched.shippingaddress && formik.errors.shippingaddress
              }
              name="shippingaddress"
              label="Shipping Address"
              defaultValue={t('Street Address \n' + 'City, State \n' + 'Zip')}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.shippingaddress}
              variant="outlined"
              multiline
              rows={3}
              maxRows={3}
            />
          </Grid>
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <TextField
              error={Boolean(
                formik.touched.toshippinginstructions &&
                  formik.errors.toshippinginstructions
              )}
              fullWidth
              helperText={
                formik.touched.toshippinginstructions &&
                formik.errors.toshippinginstructions
              }
              name="toshippinginstructions"
              label="To - Show Shipping Instructions"
              placeholder={t('To - Show Shipping Instructions here...')}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.toshippinginstructions}
              variant="outlined"
              multiline
              rows={3}
              maxRows={3}
            />
          </Grid>
        </Grid>

        <Grid container spacing={10} alignItems="center">
          <Grid
            sx={{
              mb: `${theme.spacing(2)}`
            }}
            item
            xs={3}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(0)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Move In Date')}:</b>
            </Box>
            <DatePicker
              fullWidth
              sx={{ width: '100%' }}
              value={formik.values.moveindate}
              onChange={(newValue) => {
                formik.setFieldValue('moveindate', newValue);
              }}
              validationError={Boolean(
                formik.touched.moveindate && formik.errors.moveindate
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.moveindate && formik.errors.moveindate
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select move in date...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>

          <Grid
            sx={{
              mb: `${theme.spacing(2)}`
            }}
            item
            xs={3}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(1)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Move In Time')}:</b>
            </Box>
            <TimePicker
              fullWidth
              sx={{ width: '100%' }}
              value={formik.values.moveInTime}
              onChange={(newValue) => {
                formik.setFieldValue('moveInTime', newValue);
              }}
              validationError={Boolean(
                formik.touched.moveInTime && formik.errors.moveInTime
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.moveInTime && formik.errors.moveInTime
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select Carrier Check-In Time...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            sx={{
              mb: `${theme.spacing(2)}`
            }}
            item
            xs={3}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(0)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Move Out Date')}:</b>
            </Box>
            <DatePicker
              fullWidth
              sx={{ width: '100%' }}
              value={formik.values.moveoutdate}
              onChange={(newValue) => {
                formik.setFieldValue('moveoutdate', newValue);
              }}
              validationError={Boolean(
                formik.touched.moveoutdate && formik.errors.moveoutdate
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.moveoutdate && formik.errors.moveoutdate
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select move out date...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            sx={{
              mb: `${theme.spacing(2)}`
            }}
            item
            xs={3}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(1)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Move Out Time')}:</b>
            </Box>
            <TimePicker
              fullWidth
              sx={{ width: '100%' }}
              value={formik.values.moveOutTime}
              onChange={(newValue) => {
                formik.setFieldValue('moveOutTime', newValue);
              }}
              validationError={Boolean(
                formik.touched.moveOutTime && formik.errors.moveOutTime
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.moveOutTime && formik.errors.moveOutTime
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select Carrier Check-In Time...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
        </Grid>

        <Grid container spacing={10} alignItems="center">
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(1)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Material Pick Up Date')}:</b>
            </Box>
            <DatePicker
              fullWidth
              value={formik.values.materialpickup}
              onChange={(newValue) => {
                formik.setFieldValue('materialpickup', newValue);
              }}
              validationError={Boolean(
                formik.touched.materialpickup && formik.errors.materialpickup
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.materialpickup && formik.errors.materialpickup
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select material pick up date...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <Box
              pr={3}
              sx={{
                pt: `${theme.spacing(1)}`,
                pb: { xs: 1, md: 0 }
              }}
              alignSelf="center"
            >
              <b>{t('Carrier Check-In Time')}:</b>
            </Box>
            <TimePicker
              fullWidth
              sx={{ width: '100%' }}
              value={formik.values.carrierCheckInTime}
              onChange={(newValue) => {
                formik.setFieldValue('carrierCheckInTime', newValue);
              }}
              validationError={Boolean(
                formik.touched.carrierCheckInTime &&
                  formik.errors.carrierCheckInTime
              )}
              InputProps={{
                error: Boolean(
                  formik.touched.carrierCheckInTime &&
                    formik.errors.carrierCheckInTime
                )
              }}
              renderInput={(params) => (
                <TextField
                  fullWidth
                  placeholder={t('Select Carrier Check-In Time...')}
                  {...params}
                  onKeyDown={(e) => {
                    e.key === 'Enter' && e.preventDefault();
                  }}
                />
              )}
            />
          </Grid>
        </Grid>
        <Grid container spacing={10} alignItems="center">
          <Grid
            sx={{
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <TextField
              error={Boolean(
                formik.touched.pickupAddress && formik.errors.pickupAddress
              )}
              fullWidth
              helperText={
                formik.touched.pickupAddress && formik.errors.pickupAddress
              }
              name="pickupAddress"
              label="Pickup Address"
              defaultValue={t('Street Address \n' + 'City, State \n' + 'Zip')}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.pickupAddress}
              variant="outlined"
              multiline
              rows={3}
              maxRows={3}
            />
          </Grid>
          <Grid
            sx={{
              pt: `${theme.spacing(1)}`,
              mb: `${theme.spacing(3)}`
            }}
            item
            xs={6}
          >
            <TextField
              error={Boolean(
                formik.touched.fromshowShippingInstructions &&
                  formik.errors.fromshowShippingInstructions
              )}
              fullWidth
              helperText={
                formik.touched.fromshowShippingInstructions &&
                formik.errors.fromshowShippingInstructions
              }
              name="fromshowShippingInstructions"
              label="From - Show Shipping Instructions"
              placeholder={t('From - Show Shipping Instructions here...')}
              onBlur={formik.handleBlur}
              onChange={formik.handleChange}
              value={formik.values.fromshowShippingInstructions}
              variant="outlined"
              multiline
              rows={3}
              maxRows={3}
            />
          </Grid>
        </Grid>
      </LocalizationProvider>
    </Grid>
  );
});

export default ShippingInformation;
