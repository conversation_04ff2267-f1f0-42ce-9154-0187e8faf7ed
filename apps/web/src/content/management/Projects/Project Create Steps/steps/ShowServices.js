import { memo, useContext, useEffect, useLayoutEffect, useState } from 'react';
import * as Yup from 'yup';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/material/styles';
import 'react-quill/dist/quill.snow.css';
import ReactQuill from 'react-quill';

import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  CircularProgress,
  createFilterOptions,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormControlLabel,
  FormGroup,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  Link,
  List,
  ListItem,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
  Zoom
} from '@mui/material';
import { CancelTwoTone, Visibility, VisibilityOff } from '@mui/icons-material';
import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker';
import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker';

import { useSnackbar } from 'notistack';
import { addDays, isPast, subDays } from 'date-fns';
import { SettingsContext } from '../../../../../contexts/SettingsContext';
import { LocalizationProvider } from '@mui/lab';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import DatePicker from '@mui/lab/DatePicker';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { TimePicker } from '@mui/x-date-pickers/TimePicker';

const EditorWrapper = styled(Box)(
  ({ theme }) => `

    .ql-editor {
      min-height: 100px;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: ${theme.general.borderRadius};
      border-top-right-radius: ${theme.general.borderRadius};
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border-color: ${theme.colors.alpha.black[30]};
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: ${theme.general.borderRadius};
      border-bottom-right-radius: ${theme.general.borderRadius};
    }

    &:hover {
      .ql-toolbar.ql-snow,
      .ql-container.ql-snow {
        border-color: ${theme.colors.alpha.black[50]};
      }
    }
`
);

const ShowServices = memo(({ formik }) => {
  const [settings] = useContext(SettingsContext);
  const { t } = useTranslation();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [showServicesOrderedBy, setShowServicesOrderedBy] = useState(null);

  const handleShowServicesOrderedByChange = (event) => {
    setShowServicesOrderedBy(event.target.value);
  };

  const [cardForShowServices, setCardForShowServices] = useState(null);

  const handleCardForShowServicesChange = (event) => {
    setCardForShowServices(event.target.value);
  };

  const [idLaborBy, setIdLaborBy] = useState(null);

  const handleIdLaborByChange = (event) => {
    setIdLaborBy(event.target.value);
  };

  const [unionLabor, setUnionLabor] = useState(null);

  const handleUnionLaborChange = (event) => {
    setUnionLabor(event.target.value);
  };

  return (
    <Grid container spacing={1}>
      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          pr={3}
          sx={{
            pt: `${theme.spacing(4)}`,
            pb: { xs: 1, md: 0 }
          }}
          alignSelf="center"
        >
          <Typography variant="h3">{t('Show Services')}</Typography>
        </Box>
        <Box
          alignSelf="center"
          sx={{
            pt: `${theme.spacing(1)}`,
            pb: { xs: 1, md: 2 }
          }}
        >
          <Divider />
        </Box>
      </Grid>

      <Grid container spacing={1} alignItems="center">
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <FormControl fullWidth>
            <InputLabel id="showServicesOrderedByInputLabel">
              Show Services Ordered By
            </InputLabel>
            <Select
              labelId="showServicesOrderedByLabel"
              id="showServicesOrderedBy"
              value={formik.values.showServicesOrderedByValue}
              onChange={(event) => {
                handleShowServicesOrderedByChange(event);
                formik.setFieldValue(
                  'showServicesOrderedByValue',
                  event.target.value
                );
              }}
              label="Show Services Ordered By"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {settings.showServicesOrderedByList.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>

        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <FormControl fullWidth>
            <InputLabel id="cardForShowServicesInputLabel">
              Card For Show Services
            </InputLabel>
            <Select
              labelId="cardForShowServicesLabel"
              id="cardForShowServices"
              value={formik.values.cardForShowServicesValue}
              onChange={(event) => {
                handleCardForShowServicesChange(event);
                formik.setFieldValue(
                  'cardForShowServicesValue',
                  event.target.value
                );
              }}
              label="Card For Show Services"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {settings.cardForShowServicesList.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Box
        pr={3}
        sx={{
          pt: `${theme.spacing(2)}`,
          pb: { xs: 1, md: 0 }
        }}
        alignSelf="center"
      >
        <b>
          {t(
            'If Exhibit House is ordering show services, what show services are required?'
          )}
          :
        </b>
      </Box>

      <Grid
        container
        spacing={1}
        sx={{
          pt: `${theme.spacing(1)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={3}
        >
          <List>
            <ListItem>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'laborCheckbox',
                    !formik.values.laborCheckbox
                  )
                }
                control={<Checkbox checked={formik.values.laborCheckbox} />}
                label="Labor"
              />
            </ListItem>
            <ListItem>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'carpetAndPaddingCheckbox',
                    !formik.values.carpetAndPaddingCheckbox
                  )
                }
                control={
                  <Checkbox checked={formik.values.carpetAndPaddingCheckbox} />
                }
                label="Carpet and Padding"
              />
            </ListItem>
            <ListItem>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'internetCheckbox',
                    !formik.values.internetCheckbox
                  )
                }
                control={<Checkbox checked={formik.values.internetCheckbox} />}
                label="Internet"
              />
            </ListItem>
            <ListItem>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'leadRetrievalCheckbox',
                    !formik.values.leadRetrievalCheckbox
                  )
                }
                control={
                  <Checkbox checked={formik.values.leadRetrievalCheckbox} />
                }
                label="Lead Retrieval"
              />
            </ListItem>
            <ListItem>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'riggingCheckbox',
                    !formik.values.riggingCheckbox
                  )
                }
                control={<Checkbox checked={formik.values.riggingCheckbox} />}
                label="Rigging"
              />
            </ListItem>
          </List>
        </Grid>
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={4}
        >
          <List>
            <ListItem alignItems="center" disablePadding={true}>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'electricalCheckbox',
                    !formik.values.electricalCheckbox
                  )
                }
                control={
                  <Checkbox checked={formik.values.electricalCheckbox} />
                }
                label="Electrical"
              />
            </ListItem>
            <ListItem alignItems="center" disablePadding={true}>
              <TextField
                fullWidth
                size="small"
                name="electricalTextbox"
                label="Electrical Notes"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.electricalTextbox}
                onKeyDown={(e) => {
                  e.key === 'Enter' && e.preventDefault();
                }}
                // helperText="Outside of booth needs, please note additional request:"
              />
            </ListItem>
            <ListItem
              alignItems="center"
              disablePadding={true}
              sx={{
                pt: `${theme.spacing(2)}`
              }}
            >
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'furnitureCheckbox',
                    !formik.values.furnitureCheckbox
                  )
                }
                control={<Checkbox checked={formik.values.furnitureCheckbox} />}
                label="Furniture"
              />
            </ListItem>
            <ListItem alignItems="center" disablePadding={true}>
              <TextField
                fullWidth
                size="small"
                name="furtnitureTextbox"
                label="Furniture Notes"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.furtnitureTextbox}
                onKeyDown={(e) => {
                  e.key === 'Enter' && e.preventDefault();
                }}
                // helperText="Please note needs:"
              />
            </ListItem>
            <ListItem
              alignItems="center"
              disablePadding={true}
              sx={{
                pt: `${theme.spacing(2)}`
              }}
            >
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue('avCheckbox', !formik.values.avCheckbox)
                }
                control={<Checkbox checked={formik.values.avCheckbox} />}
                label="AV"
              />
            </ListItem>
            <ListItem alignItems="center" disablePadding={true}>
              <TextField
                fullWidth
                size="small"
                name="avTextbox"
                label="AV Notes"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.avTextbox}
                onKeyDown={(e) => {
                  e.key === 'Enter' && e.preventDefault();
                }}
                // helperText="Please note needs:"
              />
            </ListItem>
          </List>
        </Grid>

        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={4}
        >
          <List>
            <ListItem alignItems="center" disablePadding={true}>
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'materialHandlingCheckbox',
                    !formik.values.materialHandlingCheckbox
                  )
                }
                control={
                  <Checkbox checked={formik.values.materialHandlingCheckbox} />
                }
                label="Material Handling"
              />
            </ListItem>
            <ListItem alignItems="center" disablePadding={true}>
              <TextField
                fullWidth
                size="small"
                name="materialHandlingTextbox"
                label="Material Handling Notes"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.materialHandlingTextbox}
                onKeyDown={(e) => {
                  e.key === 'Enter' && e.preventDefault();
                }}
              />
            </ListItem>
            <ListItem
              alignItems="center"
              disablePadding={true}
              sx={{
                pt: `${theme.spacing(2)}`
              }}
            >
              <FormControlLabel
                onChange={() =>
                  formik.setFieldValue(
                    'otherCheckbox',
                    !formik.values.otherCheckbox
                  )
                }
                control={<Checkbox checked={formik.values.otherCheckbox} />}
                label="Other"
              />
            </ListItem>
            <ListItem alignItems="center" disablePadding={true}>
              <TextField
                fullWidth
                size="small"
                name="otherTextbox"
                label="Other Notes"
                onBlur={formik.handleBlur}
                onChange={formik.handleChange}
                value={formik.values.otherTextbox}
                onKeyDown={(e) => {
                  e.key === 'Enter' && e.preventDefault();
                }}
              />
            </ListItem>
          </List>
        </Grid>
      </Grid>

      <Box
        pr={3}
        sx={{
          pt: `${theme.spacing(2)}`,
          pb: { xs: 1, md: 2 }
        }}
        alignSelf="center"
      >
        <b>{t('If Exhibit House is not ordering show services')}:</b>
      </Box>

      <Grid
        container
        spacing={1}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(2)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(formik.touched.laborBy && formik.errors.laborBy)}
            fullWidth
            helperText={formik.touched.laborBy && formik.errors.laborBy}
            name="laborBy"
            label="Labor By"
            placeholder={t('Labor By here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.laborBy}
            variant="outlined"
            multiline
          />
        </Grid>
      </Grid>

      {/*<Grid*/}
      {/*  container*/}
      {/*  spacing={1}*/}
      {/*  alignItems="center"*/}
      {/*  sx={{*/}
      {/*    pt: `${theme.spacing(2)}`*/}
      {/*  }}*/}
      {/*>*/}
      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    <FormControl fullWidth>*/}
      {/*      <InputLabel id="installDismantleInputLabel">I&D Labor By</InputLabel>*/}
      {/*      <Select*/}
      {/*        labelId="installDismantleLabel"*/}
      {/*        id="installDismantle"*/}
      {/*        value={formik.values.idLaborByValue}*/}
      {/*        onChange={(event) => {*/}
      {/*          handleIdLaborByChange(event);*/}
      {/*          formik.setFieldValue("idLaborByValue", event.target.value);*/}
      {/*        }}*/}
      {/*        label="I&D Labor By"*/}
      {/*      >*/}
      {/*        <MenuItem value="">*/}
      {/*          <em>None</em>*/}
      {/*        </MenuItem>*/}
      {/*        {settings.idLaborByList.map((item) => (*/}
      {/*            <MenuItem key={item.id} value={item.id}>{item.name}</MenuItem>*/}
      {/*          )*/}
      {/*        )}*/}
      {/*      </Select>*/}
      {/*    </FormControl>*/}
      {/*  </Grid>*/}
      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    <TextField*/}
      {/*      error={Boolean(formik.touched.installationAndDismantle && formik.errors.installationAndDismantle)}*/}
      {/*      fullWidth*/}
      {/*      helperText={formik.touched.installationAndDismantle && formik.errors.installationAndDismantle}*/}
      {/*      name="installationAndDismantle"*/}
      {/*      label="Installation & Dismantle"*/}
      {/*      placeholder={t("Installation & Dismantle here...")}*/}
      {/*      onBlur={formik.handleBlur}*/}
      {/*      onChange={formik.handleChange}*/}
      {/*      value={formik.values.installationAndDismantle}*/}
      {/*      variant="outlined"*/}
      {/*      multiline*/}
      {/*    />*/}
      {/*  </Grid>*/}
      {/*</Grid>*/}

      {/*<Grid*/}
      {/*  container*/}
      {/*  spacing={1}*/}
      {/*  alignItems="center"*/}
      {/*  sx={{*/}
      {/*    pt: `${theme.spacing(2)}`*/}
      {/*  }}*/}
      {/*>*/}
      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    <FormControl fullWidth>*/}
      {/*      <InputLabel id="unionLaborByInputLabel">Union Labor By</InputLabel>*/}
      {/*      <Select*/}
      {/*        labelId="unionLaborByLabel"*/}
      {/*        id="unionLaborBy"*/}
      {/*        value={formik.values.unionLaborValue}*/}
      {/*        onChange={(event) => {*/}
      {/*          handleUnionLaborChange(event);*/}
      {/*          formik.setFieldValue("unionLaborValue", event.target.value);*/}
      {/*        }}*/}
      {/*        label="Union Labor By"*/}
      {/*      >*/}
      {/*        <MenuItem value="">*/}
      {/*          <em>None</em>*/}
      {/*        </MenuItem>*/}
      {/*        {settings.unionLaborCoordByList.map((item) => (*/}
      {/*            <MenuItem key={item.id} value={item.id}>{item.name}</MenuItem>*/}
      {/*          )*/}
      {/*        )}*/}
      {/*      </Select>*/}
      {/*    </FormControl>*/}
      {/*  </Grid>*/}

      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    /!*<Box*!/*/}
      {/*    /!*  pr={3}*!/*/}
      {/*    /!*  sx={{*!/*/}
      {/*    /!*    pt: `${theme.spacing(1)}`,*!/*/}
      {/*    /!*    pb: { xs: 1, md: 1 }*!/*/}
      {/*    /!*  }}*!/*/}
      {/*    /!*  alignSelf="center"*!/*/}
      {/*    /!*>*!/*/}
      {/*    /!*  <b>{t("Material & Handling")}:</b>*!/*/}
      {/*    /!*</Box>*!/*/}
      {/*    <TextField*/}
      {/*      error={Boolean(formik.touched.materialAndHandling && formik.errors.materialAndHandling)}*/}
      {/*      fullWidth*/}
      {/*      helperText={formik.touched.materialAndHandling && formik.errors.materialAndHandling}*/}
      {/*      name="materialAndHandling"*/}
      {/*      label="Material & Handling"*/}
      {/*      placeholder={t("Material & Handling here...")}*/}
      {/*      onBlur={formik.handleBlur}*/}
      {/*      onChange={formik.handleChange}*/}
      {/*      value={formik.values.materialAndHandling}*/}
      {/*      variant="outlined"*/}
      {/*      multiline*/}
      {/*    />*/}
      {/*  </Grid>*/}
      {/*</Grid>*/}

      {/*<Grid*/}
      {/*  container*/}
      {/*  spacing={1}*/}
      {/*  alignItems="center"*/}
      {/*  sx={{*/}
      {/*    pt: `${theme.spacing(2)}`*/}
      {/*  }}*/}
      {/*>*/}
      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    <TextField*/}
      {/*      error={Boolean(formik.touched.electricity && formik.errors.electricity)}*/}
      {/*      fullWidth*/}
      {/*      helperText={formik.touched.electricity && formik.errors.electricity}*/}
      {/*      name="electricity"*/}
      {/*      label="Electricity"*/}
      {/*      placeholder={t("Electricity here...")}*/}
      {/*      onBlur={formik.handleBlur}*/}
      {/*      onChange={formik.handleChange}*/}
      {/*      value={formik.values.electricity}*/}
      {/*      variant="outlined"*/}
      {/*      multiline*/}
      {/*    />*/}
      {/*  </Grid>*/}

      {/*  <Grid*/}
      {/*    sx={{*/}
      {/*      mb: `${theme.spacing(1)}`*/}
      {/*    }}*/}
      {/*    item*/}
      {/*    xs={6}*/}
      {/*  >*/}
      {/*    /!*<Box*!/*/}
      {/*    /!*  pr={3}*!/*/}
      {/*    /!*  sx={{*!/*/}
      {/*    /!*    pt: `${theme.spacing(1)}`,*!/*/}
      {/*    /!*    pb: { xs: 1, md: 1 }*!/*/}
      {/*    /!*  }}*!/*/}
      {/*    /!*  alignSelf="center"*!/*/}
      {/*    /!*>*!/*/}
      {/*    /!*  <b>{t("Other Show Service Requests")}:</b>*!/*/}
      {/*    /!*</Box>*!/*/}
      {/*    <TextField*/}
      {/*      error={Boolean(formik.touched.otherShowServiceRequests && formik.errors.otherShowServiceRequests)}*/}
      {/*      fullWidth*/}
      {/*      helperText={formik.touched.otherShowServiceRequests && formik.errors.otherShowServiceRequests}*/}
      {/*      name="otherShowServiceRequests"*/}
      {/*      label="Other Show Service Requests"*/}
      {/*      placeholder={t("Other Show Service Requests here...")}*/}
      {/*      onBlur={formik.handleBlur}*/}
      {/*      onChange={formik.handleChange}*/}
      {/*      value={formik.values.otherShowServiceRequests}*/}
      {/*      variant="outlined"*/}
      {/*      multiline*/}
      {/*    />*/}
      {/*  </Grid>*/}
      {/*</Grid>*/}

      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          alignSelf="center"
          sx={{
            pt: `${theme.spacing(1)}`,
            pb: { xs: 1, md: 0 }
          }}
        >
          <Divider />
        </Box>
      </Grid>

      <Grid item xs={12} textAlign={{ sm: 'left' }}>
        <Box
          pr={3}
          sx={{
            pb: { xs: 1, md: 0 }
          }}
        >
          <b>{t('Portal Whiteboard Notes')}:</b>
        </Box>
      </Grid>
      <Grid
        item
        xs={12}
        sx={{
          mb: `${theme.spacing(3)}`
        }}
      >
        <EditorWrapper>
          <ReactQuill
            theme="snow"
            value={formik.values.projectBlurb}
            onChange={(v) => formik.setFieldValue('projectBlurb', v)}
          />
        </EditorWrapper>
      </Grid>
    </Grid>
  );
});

export default ShowServices;
