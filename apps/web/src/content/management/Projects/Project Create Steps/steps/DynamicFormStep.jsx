import { memo, useMemo } from 'react';
import {
  Box,
  CircularProgress,
  Typography,
  useTheme,
  Grid,
  Divider
} from '@mui/material';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';
import { StackedFields, TabbedFields } from '../../../../../utils/formik-utils';

const DynamicFormStep = ({ formData, formik }) => {
  const theme = useTheme();
  const { formFields, formGroups, formLayout } = formData || {};

  if (!formData) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: 300
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  console.log('formik', formik);

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Typography
        variant="h4"
        gutterBottom
        sx={{ mb: 2, mx: 'auto', textAlign: 'center' }}
      >
        {formData.formSelected?.name || 'Form Details'}
      </Typography>
      <Divider sx={{ mb: 2, mx: 2 }} />
      <Grid container spacing={3} sx={{ p: 3 }}>
        {formLayout === 'stack' ? (
          <StackedFields
            customFields={formFields}
            fieldGroups={formGroups}
            errors={formik.errors}
            handleBlur={formik.handleBlur}
            handleChange={formik.handleChange}
            setFieldValue={formik.setFieldValue}
            touched={formik.touched}
            values={formik.values}
          />
        ) : (
          <TabbedFields
            customFields={formFields}
            fieldGroups={formGroups}
            errors={formik.errors}
            handleBlur={formik.handleBlur}
            handleChange={formik.handleChange}
            setFieldValue={formik.setFieldValue}
            touched={formik.touched}
            values={formik.values}
          />
        )}
      </Grid>
    </motion.div>
  );
};

DynamicFormStep.propTypes = {
  formData: PropTypes.shape({
    formFields: PropTypes.array,
    formGroups: PropTypes.array,
    formSelected: PropTypes.shape({
      id: PropTypes.string,
      name: PropTypes.string
    })
  }),
  formik: PropTypes.object.isRequired
};

export default DynamicFormStep;
