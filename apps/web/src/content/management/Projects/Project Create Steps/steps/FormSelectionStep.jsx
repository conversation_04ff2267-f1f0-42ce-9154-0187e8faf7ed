import {
  Autocomplete,
  Box,
  TextField,
  Typography,
  useTheme,
  CircularProgress
} from '@mui/material';
import { motion } from 'framer-motion';
import PropTypes from 'prop-types';

const FormSelectionStep = ({
  forms,
  selectedForm,
  onFormSelect,
  loading,
  error
}) => {
  const theme = useTheme();

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      exit={{ opacity: 0, x: -20 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: 300,
          p: 3
        }}
      >
        <Typography variant="h5" gutterBottom sx={{ mb: 4 }}>
          Select a Form
        </Typography>

        <Box sx={{ width: '100%', maxWidth: 500 }}>
          <Autocomplete
            options={forms || []}
            getOptionLabel={(option) => option.name}
            value={selectedForm}
            onChange={(_, value) => onFormSelect(value)}
            loading={loading}
            loadingText="Loading forms..."
            noOptionsText="No forms available"
            renderInput={(params) => (
              <TextField
                {...params}
                label="Select a form"
                variant="outlined"
                fullWidth
                error={!!error}
                helperText={error || 'Select a form to continue'}
                InputProps={{
                  ...params.InputProps,
                  endAdornment: (
                    <>
                      {loading ? (
                        <CircularProgress color="inherit" size={20} />
                      ) : null}
                      {params.InputProps.endAdornment}
                    </>
                  )
                }}
              />
            )}
            isOptionEqualToValue={(option, value) => option.id === value.id}
            disableClearable
          />
        </Box>
      </Box>
    </motion.div>
  );
};

FormSelectionStep.propTypes = {
  forms: PropTypes.arrayOf(
    PropTypes.shape({
      id: PropTypes.string.isRequired,
      name: PropTypes.string.isRequired
    })
  ),
  selectedForm: PropTypes.object,
  onFormSelect: PropTypes.func.isRequired,
  loading: PropTypes.bool,
  error: PropTypes.string
};

export default FormSelectionStep;
