import { memo, useContext, useEffect, useLayoutEffect, useState } from 'react';
import * as Yup from 'yup';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/material/styles';
import 'react-quill/dist/quill.snow.css';
import ReactQuill from 'react-quill';

import {
  Autocomplete,
  Box,
  Button,
  Chip,
  CircularProgress,
  createFilterOptions,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputAdornment,
  InputLabel,
  Link,
  MenuItem,
  Select,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme,
  Zoom
} from '@mui/material';
import { CancelTwoTone, Visibility, VisibilityOff } from '@mui/icons-material';
import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker';
import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker';

import { useSnackbar } from 'notistack';
import { addDays, isPast, subDays } from 'date-fns';
import { SettingsContext } from '../../../../../contexts/SettingsContext';

const EventInformation = memo(({ formik }) => {
  const { t } = useTranslation();
  const [open, setOpen] = useState(false);
  const [settings] = useContext(SettingsContext);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const filter = createFilterOptions();

  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [shipCheck, setShipCheck] = useState({
    after: 0,
    prior: 0
  });

  const [exhibitSpaceValue, setExhibitSpaceValue] = useState(null);

  const handleExhibitSpaceChange = (event) => {
    setExhibitSpaceValue(event.target.value);
  };

  const [generalContractor, setGeneralContractor] = useState(null);
  const [openGeneralContractor, toggleGeneralContractorOpen] = useState(false);

  const handleGeneralContractorClose = () => {
    setGeneralContractorDialogValue({
      name: ''
    });
    toggleGeneralContractorOpen(false);
  };

  const [generalContractorDialogValue, setGeneralContractorDialogValue] =
    useState({
      name: ''
    });

  const handleGeneralContractorSubmit = (event) => {
    event.preventDefault();
    setGeneralContractor({
      name: generalContractorDialogValue.name
    });
    handleGeneralContractorClose();
  };

  const [showGcWebsitePassword, setShowGcWebsitePassword] = useState(false);

  const handleClickShowGcWebsitePassword = () =>
    setShowGcWebsitePassword((show) => !show);

  const handleGcWebsiteMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const withinDateThreshold = (date, position) => {
    let currentDate = new Date(date);
    let priorThreshold = subDays(currentDate, shipCheck.prior);
    let afterThreshold = addDays(currentDate, shipCheck.after);
    let disabled = false;

    if (position === 'start') {
      disabled = isPast(priorThreshold);
    } else {
      disabled = isPast(priorThreshold);
    }

    return disabled;
  };

  return (
    <Grid container spacing={1}>
      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          pr={3}
          sx={{
            pt: `${theme.spacing(2)}`,
            pb: { xs: 1, md: 0 }
          }}
          alignSelf="center"
        >
          <Typography variant="h3">{t('Project Name')}</Typography>
        </Box>
      </Grid>

      <Grid
        sx={{
          mb: `${theme.spacing(3)}`
        }}
        item
        xs={12}
      >
        <TextField
          error={Boolean(formik.touched.projname && formik.errors.projname)}
          fullWidth
          helperText={
            (formik.touched.projname && formik.errors.projname) || '* Required'
          }
          name="projname"
          placeholder={t('Event name here...')}
          onBlur={formik.handleBlur}
          onChange={formik.handleChange}
          value={formik.values.projname}
          variant="outlined"
          onKeyDown={(e) => {
            e.key === 'Enter' && e.preventDefault();
          }}
        />
      </Grid>

      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          pr={3}
          sx={{
            pt: `${theme.spacing(2)}`,
            pb: { xs: 1, md: 0 }
          }}
          alignSelf="center"
        >
          <Typography variant="h3">{t('Event Information')}</Typography>
        </Box>
        <Box
          alignSelf="center"
          sx={{
            pt: `${theme.spacing(2)}`,
            pb: { xs: 1, md: 0 }
          }}
        >
          <Divider />
        </Box>
      </Grid>

      <Grid container spacing={10} alignItems="center">
        <Grid
          sx={{
            mb: `${theme.spacing(2)}`
          }}
          item
          xs={12}
        >
          <Box
            pr={3}
            sx={{
              pt: `${theme.spacing(1)}`,
              pb: { xs: 1, md: 2 }
            }}
            alignSelf="center"
          >
            <b>{t('Event Start And End Dates')}:</b>
          </Box>
          {mobile ? (
            <MobileDateRangePicker
              startText="Event start"
              value={formik.values.duration}
              name="duration"
              onChange={(newValue) => {
                console.log('Range Date:', newValue);
                formik.setFieldValue('duration', newValue);
              }}
              shouldDisableDate={(date, position) => {
                return withinDateThreshold(date, position);
              }}
              renderInput={(startProps, endProps) => (
                <>
                  <TextField
                    required
                    fullWidth
                    {...startProps}
                    error={Boolean(
                      formik.touched.duration[0] && formik.errors.duration?.[0]
                    )}
                    helperText={
                      formik.touched.duration && formik.errors.duration?.[0]
                    }
                  />
                  <Box sx={{ mx: 2 }}> to </Box>
                  <TextField
                    required
                    fullWidth
                    {...endProps}
                    label="Event end"
                    error={Boolean(
                      formik.touched.duration && formik.errors.duration?.[1]
                    )}
                    helperText={
                      formik.touched.duration && formik.errors.duration?.[1]
                    }
                  />
                </>
              )}
            />
          ) : (
            <DesktopDateRangePicker
              fullWidth
              localeText={{ start: 'Event Start', end: 'Event End' }}
              startText="Event start"
              value={formik.values.duration}
              name="duration"
              shouldDisableDate={(date, position) => {
                return withinDateThreshold(date, position);
              }}
              onChange={(newValue) => {
                console.log('Range Date:', newValue);
                formik.setFieldValue('duration', newValue);
              }}
              slotProps={{
                textField: ({ position }) => ({
                  color:
                    position === 'start'
                      ? Boolean(
                          formik.touched.duration?.[0] &&
                            formik.errors.duration?.[0]
                        )
                        ? 'error'
                        : 'secondary'
                      : Boolean(
                          formik.touched.duration?.[1] &&
                            formik.errors.duration?.[1]
                        )
                      ? 'error'
                      : 'secondary',
                  helperText:
                    formik.touched.duration?.[0] && formik.errors.duration?.[0]
                      ? 'Enter a valid date MM/DD/YYYY'
                      : '* Required',
                  focused: true
                })
              }}
              // renderInput={(startProps, endProps) => (
              //   <>
              //     <TextField
              //       required
              //       fullWidth
              //       label="Event Start"
              //       {...startProps}
              //       error={Boolean(formik.touched.duration?.[0] && formik.errors.duration?.[0])}
              //       helperText={
              //         formik.touched.duration?.[0] && formik.errors.duration?.[0]
              //           ? formik.errors.duration[0]
              //           : "* Required"
              //       }
              //     />
              //     <Box sx={{ mx: 2 }}> to </Box>
              //     <TextField
              //       required
              //       fullWidth
              //       {...endProps}
              //       label="Event End"
              //       error={Boolean(formik.touched.duration?.[1] && formik.errors.duration?.[1])}
              //       helperText={
              //         formik.touched.duration?.[1] && formik.errors.duration?.[1]
              //           ? formik.errors.duration[1]
              //           : "* Required"
              //       }
              //     />
              //   </>
              // )}
            />
          )}
        </Grid>
      </Grid>

      <Grid
        container
        spacing={10}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(2)}`
        }}
      >
        <Grid
          item
          xs={6}
          sx={{
            mb: `${theme.spacing(1)}`
          }}
        >
          <TextField
            error={Boolean(
              formik.touched.eventwebsite && formik.errors.eventwebsite
            )}
            fullWidth
            helperText={
              formik.touched.eventwebsite && formik.errors.eventwebsite
            }
            name="eventwebsite"
            label="Event Website"
            placeholder={t('Event Website here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.eventwebsite}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>

        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.eventvenuenumber && formik.errors.eventvenuenumber
            )}
            fullWidth
            helperText={
              formik.touched.eventvenuenumber && formik.errors.eventvenuenumber
            }
            name="eventvenuenumber"
            label="Event Venue Number"
            placeholder={t('Event Venue Number here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.eventvenuenumber}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
      </Grid>

      <Grid
        container
        spacing={10}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(2)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.boothnumber && formik.errors.boothnumber
            )}
            fullWidth
            helperText={formik.touched.boothnumber && formik.errors.boothnumber}
            name="boothnumber"
            label="Booth Number"
            placeholder={t('Booth Number here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.boothnumber}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(formik.touched.boothsize && formik.errors.boothsize)}
            fullWidth
            helperText={formik.touched.boothsize && formik.errors.boothsize}
            name="boothsize"
            label="Booth Size"
            placeholder={t('Booth Size here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.boothsize}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
      </Grid>

      <Grid
        container
        spacing={10}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(2)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.maincontactnameandphone &&
                formik.errors.maincontactnameandphone
            )}
            fullWidth
            helperText={
              formik.touched.maincontactnameandphone &&
              formik.errors.maincontactnameandphone
            }
            name="maincontactnameandphone"
            label="Main Contact Name & Phone #"
            placeholder={t('Main Contact Name & Phone # here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.maincontactnameandphone}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>

        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <FormControl fullWidth>
            <InputLabel id="exhibitSpaceInputlabel">Exhibit Space</InputLabel>
            <Select
              labelId="exhibitSpaceLabel"
              id="exhibitSpace"
              value={formik.values.exhibitSpace}
              name="exhibitSpace"
              onChange={(event) => {
                handleExhibitSpaceChange(event);
                formik.setFieldValue('exhibitSpace', event.target.value);
              }}
              label="Exhibit Space"
            >
              <MenuItem value="">
                <em>None</em>
              </MenuItem>
              {settings.exhibitSpaceList.map((item) => (
                <MenuItem key={item.id} value={item.id}>
                  {item.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>

      <Grid item xs={12} justifyContent="flex-start" textAlign={{ sm: 'left' }}>
        <Box
          alignSelf="center"
          sx={{
            pt: `${theme.spacing(2)}`,
            pb: { xs: 1, md: 0 }
          }}
        >
          <Divider />
        </Box>
      </Grid>

      <Box
        pr={3}
        sx={{
          pt: `${theme.spacing(2)}`,
          pb: { xs: 1, md: 2 }
        }}
        alignSelf="center"
      >
        <b>{t('General Contractor Information')}:</b>
      </Box>

      <Grid
        container
        spacing={10}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(0)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <Autocomplete
            value={formik.values.generalContractorValue}
            onChange={(event, newValue) => {
              if (typeof newValue === 'string') {
                setTimeout(() => {
                  toggleGeneralContractorOpen(true);
                  setGeneralContractorDialogValue({
                    name: newValue
                  });
                });
              } else if (newValue && newValue.inputValue) {
                toggleGeneralContractorOpen(true);
                setGeneralContractorDialogValue({
                  name: newValue.inputValue
                });
                formik.setFieldValue(
                  'generalContractorValue',
                  newValue.inputValue
                );
              } else {
                setGeneralContractor(newValue);
                formik.setFieldValue('generalContractorValue', newValue?.name);
              }
            }}
            filterOptions={(options, params) => {
              const filtered = filter(options, params);

              if (params.inputValue !== '') {
                filtered.push({
                  inputValue: params.inputValue,
                  name: `Add "${params.inputValue}"`
                });
              }

              return filtered;
            }}
            id="generalContractorOptionsBox"
            options={[{ name: 'Freeman' }, { name: 'GES' }]}
            getOptionLabel={(option) => {
              if (typeof option === 'string') {
                return option;
              }
              if (option.inputValue) {
                return option.inputValue;
              }
              return option.name;
            }}
            selectOnFocus
            clearOnBlur
            handleHomeEndKeys
            renderOption={(props, option) => <li {...props}>{option.name}</li>}
            fullWidth
            freeSolo
            name="generalContractor"
            renderInput={(params) => (
              <TextField
                {...params}
                placeholder={t('Select an option or type to enter your own...')}
                label="General Contractor"
              />
            )}
          />
          <Dialog
            open={openGeneralContractor}
            onClose={handleGeneralContractorClose}
          >
            <form onClick={handleGeneralContractorSubmit}>
              <DialogTitle>Add a new general contractor</DialogTitle>
              <DialogContent>
                <DialogContentText>
                  Click "Add" to add your own general contractor.
                </DialogContentText>
                <TextField
                  autoFocus
                  margin="dense"
                  id="name"
                  value={generalContractorDialogValue.name}
                  onChange={(event) =>
                    setGeneralContractorDialogValue({
                      ...generalContractorDialogValue,
                      name: event.target.value
                    })
                  }
                  label="General Contractor"
                  type="text"
                  variant="standard"
                />
              </DialogContent>
              <DialogActions>
                <Button onClick={handleGeneralContractorClose}>Cancel</Button>
                <Button type="submit">Add</Button>
              </DialogActions>
            </form>
          </Dialog>
        </Grid>
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.decoratorurl && formik.errors.decoratorurl
            )}
            fullWidth
            helperText={
              formik.touched.decoratorurl && formik.errors.decoratorurl
            }
            name="decoratorurl"
            label="General Contractor Website"
            placeholder={t('General Contractor Website here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.decoratorurl}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
      </Grid>

      <Grid
        container
        spacing={10}
        alignItems="center"
        sx={{
          pt: `${theme.spacing(2)}`
        }}
      >
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.decoratorclientusername &&
                formik.errors.decoratorclientusername
            )}
            fullWidth
            helperText={
              formik.touched.decoratorclientusername &&
              formik.errors.decoratorclientusername
            }
            name="decoratorclientusername"
            label="General Contractor Website User Name"
            placeholder={t('General Contractor Website User Name here...')}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.decoratorclientusername}
            variant="outlined"
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
        <Grid
          sx={{
            mb: `${theme.spacing(1)}`
          }}
          item
          xs={6}
        >
          <TextField
            error={Boolean(
              formik.touched.decoratorclientuserpassword &&
                formik.errors.decoratorclientuserpassword
            )}
            fullWidth
            helperText={
              formik.touched.decoratorclientuserpassword &&
              formik.errors.decoratorclientuserpassword
            }
            name="decoratorclientuserpassword"
            label="General Contractor Website Password"
            placeholder={t('General Contractor Website Password here...')}
            type={showGcWebsitePassword ? 'text' : 'password'}
            onBlur={formik.handleBlur}
            onChange={formik.handleChange}
            value={formik.values.decoratorclientuserpassword}
            variant="outlined"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <Tooltip
                    arrow
                    placement="top"
                    title={t('Toggle Password Visibility')}
                  >
                    <IconButton
                      aria-label="toggle password visibility"
                      onClick={handleClickShowGcWebsitePassword}
                      onMouseDown={handleGcWebsiteMouseDownPassword}
                      edge="end"
                    >
                      {showGcWebsitePassword ? (
                        <Visibility />
                      ) : (
                        <VisibilityOff />
                      )}
                    </IconButton>
                  </Tooltip>
                </InputAdornment>
              )
            }}
            onKeyDown={(e) => {
              e.key === 'Enter' && e.preventDefault();
            }}
          />
        </Grid>
      </Grid>
    </Grid>
  );
});

export default EventInformation;
