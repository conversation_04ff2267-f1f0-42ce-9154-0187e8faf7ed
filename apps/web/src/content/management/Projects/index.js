import { useEffect, useState } from 'react';

import { Helmet } from 'react-helmet-async';
import { Box, Grid } from '@mui/material';
import Footer from 'src/components/Footer';
import useRefMounted from 'src/hooks/useRefMounted';

import PageHeader from './PageHeader';

import Results from './Results';
import useAuth from '../../../hooks/useAuth';

function ManagementProjects() {
  const isMountedRef = useRefMounted();
  const [projects, setProjects] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    if (isMountedRef && user && user?.profile) {
      setProjects(user.profile.projects.list);
    }
  }, [user]);

  useEffect(() => {
    console.log('Projects: ', projects);
  }, [projects]);

  return (
    <>
      <Helmet>
        <title>Projects</title>
      </Helmet>
      <Grid display="flex" alignItems="center" item xs={12} px={4} pb={4}>
        <Box flex={1} mt={3}>
          <PageHeader projects={projects} />
        </Box>
      </Grid>
      <Grid
        sx={{
          px: 4
        }}
        container
        direction="row"
        justifyContent="center"
        alignItems="stretch"
        spacing={4}
      >
        <Grid item xs={12}>
          <Results projects={projects} />
        </Grid>
      </Grid>
      <Footer />
    </>
  );
}

export default ManagementProjects;
