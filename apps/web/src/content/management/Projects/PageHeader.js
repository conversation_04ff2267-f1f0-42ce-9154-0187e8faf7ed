import { memo, useContext, useEffect, useLayoutEffect, useState } from 'react';
import * as Yup from 'yup';
import { Formik } from 'formik';
import { useTranslation } from 'react-i18next';
import { styled } from '@mui/material/styles';
import 'react-quill/dist/quill.snow.css';
import ReactQuill from 'react-quill';

import {
  Box,
  Button,
  Chip,
  CircularProgress,
  createFilterOptions,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  Link,
  TextField,
  Typography,
  useMediaQuery,
  useTheme,
  Zoom
} from '@mui/material';
import axios from 'axios';
import { CancelTwoTone } from '@mui/icons-material';
import { MobileDateRangePicker } from '@mui/x-date-pickers-pro/MobileDateRangePicker';
import { DesktopDateRangePicker } from '@mui/x-date-pickers-pro/DesktopDateRangePicker';
import { useDropzone } from 'react-dropzone';
import { useSnackbar } from 'notistack';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import useAuth from '../../../hooks/useAuth';
import { SettingsContext } from '../../../contexts/SettingsContext';
import { addDays, isPast, subDays } from 'date-fns';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

/* May use later
const AvatarPageTitle = styled(Avatar)(
  ({ theme }) => `
      width: ${theme.spacing(8)};
      height: ${theme.spacing(8)};
      color: ${theme.colors.primary.main};
      margin-right: ${theme.spacing(2)};
      background: ${
        theme.palette.mode === 'dark'
          ? theme.colors.alpha.trueWhite[10]
          : theme.colors.alpha.white[50]
      };
      box-shadow: ${
        theme.palette.mode === 'dark'
          ? `0 1px 0 ${alpha(
              lighten(theme.colors.primary.main, 0.8),
              0.2
            )}, 0px 2px 4px -3px rgba(0, 0, 0, 0.3), 0px 5px 16px -4px rgba(0, 0, 0, .5)`
          : `0px 2px 4px -3px ${alpha(
              theme.colors.alpha.black[100],
              0.4
            )}, 0px 5px 16px -4px ${alpha(theme.colors.alpha.black[100], 0.2)}`
      };
`
);

const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.general.borderRadius};
    padding: ${theme.spacing(3)};
    background: ${theme.colors.alpha.black[5]};
    border: 1px dashed ${theme.colors.alpha.black[30]};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(['border', 'background'])};

    &:hover {
      background: ${theme.colors.alpha.white[100]};
      border-color: ${theme.colors.primary.main};
    }
`
);
*/

const withinDateThreshold = (date, position, shipCheck) => {
  let currentDate = new Date(date);
  let priorThreshold = subDays(currentDate, shipCheck.prior);
  let afterThreshold = addDays(currentDate, shipCheck.after);
  let disabled = false;

  if (position === 'start') {
    disabled = isPast(priorThreshold);
  } else {
    disabled = isPast(priorThreshold);
  }

  return disabled;
};

const EditorWrapper = styled(Box)(
  ({ theme }) => `

    .ql-editor {
      min-height: 100px;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: ${theme.general.borderRadius};
      border-top-right-radius: ${theme.general.borderRadius};
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border-color: ${theme.colors.alpha.black[30]};
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: ${theme.general.borderRadius};
      border-bottom-right-radius: ${theme.general.borderRadius};
    }

    &:hover {
      .ql-toolbar.ql-snow,
      .ql-container.ql-snow {
        border-color: ${theme.colors.alpha.black[50]};
      }
    }
`
);
//
// const AvatarWrapper = styled(Avatar)(
//   ({ theme }) => `
//     background: ${theme.colors.primary.lighter};
//     color: ${theme.colors.primary.main};
//     width: ${theme.spacing(7)};
//     height: ${theme.spacing(7)};
// `
// );
//
// const AvatarSuccess = styled(Avatar)(
//   ({ theme }) => `
//     background: ${theme.colors.success.light};
//     width: ${theme.spacing(7)};
//     height: ${theme.spacing(7)};
// `
// );
//
// const AvatarDanger = styled(Avatar)(
//   ({ theme }) => `
//     background: ${theme.colors.error.light};
//     width: ${theme.spacing(7)};
//     height: ${theme.spacing(7)};
// `
// );

const CreateNewProjectDialog = memo(
  ({
    open,
    mobile,
    handleCreateProjectClose,
    handleCreateProjectSuccess,
    shipCheck
  }) => {
    const { t } = useTranslation();
    const theme = useTheme();

    return (
      <Dialog
        fullWidth
        maxWidth="md"
        open={open}
        onClose={handleCreateProjectClose}
      >
        <DialogTitle
          sx={{
            p: 3
          }}
        >
          <Typography variant="h4" gutterBottom>
            {t('Create new project')}
          </Typography>
          <Typography variant="subtitle2">
            {t(
              'Please fill in the following fields to create a new event/project. After submit this event will be available for item ordering.'
            )}
          </Typography>
        </DialogTitle>
        <Formik
          initialValues={{
            projname: '',
            materialarrival: '',
            materialpickup: '',
            moveindate: '',
            moveoutdate: '',
            boothsize: '',
            shippingaddress: '',
            projectBlurb: '',
            duration: [null, null],
            submit: null
          }}
          validationSchema={Yup.object().shape({
            projname: Yup.string()
              .max(255)
              .required(t('The title field is required')),
            materialarrival: Yup.string().max(255),
            materialpickup: Yup.string().max(255),
            moveindate: Yup.string(),
            moveoutdate: Yup.string(),
            boothsize: Yup.string().max(255),
            shippingaddress: Yup.string().max(255)
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              setSubmitting(true);
              await handleCreateProjectSuccess(
                _values,
                setStatus,
                setSubmitting,
                resetForm
              );
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values,
            setFieldValue
          }) => (
            <form onSubmit={handleSubmit}>
              <DialogContent
                dividers
                sx={{
                  p: 3
                }}
              >
                <Grid container spacing={1}>
                  {/* Label of Project Title */}
                  <Grid
                    item
                    xs={12}
                    justifyContent="flex-start"
                    textAlign={{ sm: 'left' }}
                  >
                    <Box
                      pr={3}
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                      alignSelf="center"
                    >
                      <b>{t('Project Name')}:</b>
                    </Box>
                  </Grid>
                  {/* Project Title Input */}
                  <Grid
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                    item
                    xs={12}
                  >
                    <TextField
                      error={Boolean(touched.projname && errors.projname)}
                      fullWidth
                      helperText={touched.projname && errors.projname}
                      name="projname"
                      placeholder={t('Event name here...')}
                      onBlur={handleBlur}
                      onChange={handleChange}
                      value={values.title}
                      variant="outlined"
                    />
                  </Grid>
                  {/* Material Arrival Date Field */}
                  {/* <Grid
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                    item
                    xs={12}
                    md={6}
                  >
                    <Box
                      pr={3}
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                      alignSelf="center"
                    >
                      <b>{t('Material Arrival Date')}:</b>
                    </Box>
                    <DatePicker
                      fullWidth
                      value={values.materialarrival}
                      onChange={(newValue) => {
                        console.log('Material Arrival:', newValue);
                        setFieldValue('materialarrival', newValue);
                      }}
                      validationError={Boolean(
                        touched.materialarrival && errors.materialarrival
                      )}
                      InputProps={{
                        error: Boolean(
                          touched.materialarrival && errors.materialarrival
                        )
                      }}
                      renderInput={(params) => (
                        <TextField
                          fullWidth
                          onBlur={handleBlur}
                          name="materialarrival"
                          placeholder={t('Select material arrival date...')}
                          helperText={
                            touched.materialarrival && errors.materialarrival
                          }
                          {...params}
                        />
                      )}
                    />
                  </Grid>
                  <Grid
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                    item
                    xs={12}
                    md={6}
                  >
                    <Box
                      pr={3}
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                      alignSelf="center"
                    >
                      <b>{t('Move In Date')}:</b>
                    </Box>
                    <DatePicker
                      fullWidth
                      value={values.moveindate}
                      onChange={(newValue) => {
                        setFieldValue('moveindate', newValue);
                      }}
                      validationError={Boolean(
                        touched.moveindate && errors.moveindate
                      )}
                      InputProps={{
                        error: Boolean(touched.moveindate && errors.moveindate)
                      }}
                      renderInput={(params) => (
                        <TextField
                          fullWidth
                          name="moveindate"
                          onBlur={handleBlur}
                          placeholder={t('Select move in date...')}
                          helperText={touched.moveindate && errors.moveindate}
                          {...params}
                        />
                      )}
                    />
                  </Grid>

                  */}
                  <Grid
                    item
                    xs={12}
                    justifyContent="flex-start"
                    textAlign={{ sm: 'left' }}
                  >
                    <Box
                      pr={3}
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                      alignSelf="center"
                    >
                      <Typography variant="h4">
                        {t('Event Duration')}
                      </Typography>
                    </Box>
                    <Box
                      alignSelf="center"
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                    >
                      <Divider />
                    </Box>
                  </Grid>
                  <Grid
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                    item
                    xs={12}
                  >
                    {mobile ? (
                      <MobileDateRangePicker
                        startText="Event start"
                        value={values.duration}
                        name="duration"
                        onChange={(newValue) => {
                          console.log('Range Date:', newValue);
                          setFieldValue('duration', newValue);
                        }}
                        shouldDisableDate={(date, position) => {
                          return withinDateThreshold(date, position, shipCheck);
                        }}
                        renderInput={(startProps, endProps) => (
                          <>
                            <TextField required fullWidth {...startProps} />
                            <Box sx={{ mx: 2 }}> to </Box>
                            <TextField
                              required
                              fullWidth
                              {...endProps}
                              label="Event end"
                            />
                          </>
                        )}
                      />
                    ) : (
                      <DesktopDateRangePicker
                        fullWidth
                        startText="Event start"
                        value={values.duration}
                        name="duration"
                        shouldDisableDate={(date, position) => {
                          return withinDateThreshold(date, position);
                        }}
                        onChange={(newValue) => {
                          console.log('Range Date:', newValue);
                          setFieldValue('duration', newValue);
                        }}
                        renderInput={(startProps, endProps) => (
                          <>
                            <TextField required fullWidth {...startProps} />
                            <Box sx={{ mx: 2 }}> to </Box>
                            <TextField
                              required
                              fullWidth
                              {...endProps}
                              label="Event end"
                            />
                          </>
                        )}
                      />
                    )}
                    <Box
                      alignSelf="center"
                      sx={{
                        pt: `${theme.spacing(2)}`,
                        pb: { xs: 1, md: 0 }
                      }}
                    >
                      <Divider />
                    </Box>
                  </Grid>

                  <Grid item xs={12} textAlign={{ sm: 'left' }}>
                    <Box
                      pr={3}
                      sx={{
                        pb: { xs: 1, md: 0 }
                      }}
                    >
                      <b>{t('Portal Whiteboard')}:</b>
                    </Box>
                  </Grid>
                  <Grid
                    item
                    xs={12}
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                  >
                    <EditorWrapper>
                      <ReactQuill
                        theme="snow"
                        value={values.projectBlurb}
                        onChange={(v) => setFieldValue('projectBlurb', v)}
                      />
                    </EditorWrapper>
                  </Grid>
                </Grid>
                <Box
                  sx={{
                    display: { xs: 'block', sm: 'flex' },
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    p: 3
                  }}
                >
                  <Box>
                    <Button
                      fullWidth={mobile}
                      sx={{
                        mr: 2
                      }}
                      color="secondary"
                      size="large"
                      variant="outlined"
                      onClick={handleCreateProjectClose}
                    >
                      {t('Cancel')}
                    </Button>
                    <Button
                      fullWidth={mobile}
                      type="submit"
                      startIcon={
                        isSubmitting ? <CircularProgress size="1rem" /> : null
                      }
                      disabled={Boolean(errors.submit) || isSubmitting}
                      variant="contained"
                      size="large"
                    >
                      {t('Create project')}
                    </Button>
                  </Box>
                </Box>
              </DialogContent>
            </form>
          )}
        </Formik>
      </Dialog>
    );
  }
);

function PageHeader({ projects }) {
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useContext(SettingsContext);
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const { user, userMutate, currentProject, setProject } = useAuth();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [shipCheck, setShipCheck] = useState({
    after: 0,
    prior: 0
  });
  const [currAddressBook, setCurrAddressBook] = useState([]);
  const customerRec = user?.profile?.record;

  const filter = createFilterOptions();

  const [showGcWebsitePassword, setShowGcWebsitePassword] = useState(false);

  const handleClickShowGcWebsitePassword = () =>
    setShowGcWebsitePassword((show) => !show);

  const handleGcWebsiteMouseDownPassword = (event) => {
    event.preventDefault();
  };

  const [generalContractor, setGeneralContractor] = useState(null);
  const [openGeneralContractor, toggleGeneralContractorOpen] = useState(false);

  const handleGeneralContractorClose = () => {
    setGeneralContractorDialogValue({
      name: ''
    });
    toggleGeneralContractorOpen(false);
  };

  const [generalContractorDialogValue, setGeneralContractorDialogValue] =
    useState({
      name: ''
    });

  const handleGeneralContractorSubmit = (event) => {
    event.preventDefault();
    setGeneralContractor({
      name: generalContractorDialogValue.name
    });
    handleGeneralContractorClose();
  };

  const [exhibitSpaceValue, setExhibitSpaceValue] = useState(null);

  const handleExhibitSpaceChange = (event) => {
    setExhibitSpaceValue(event.target.value);
  };

  const [shipToAddress, setShipToAddress] = useState(null);

  const shipToAddressChange = (event) => {
    setShipToAddress(event.target.value);
  };

  const [showServicesOrderedBy, setShowServicesOrderedBy] = useState(null);

  const handleShowServicesOrderedByChange = (event) => {
    setShowServicesOrderedBy(event.target.value);
  };

  const [cardForShowServices, setCardForShowServices] = useState(null);

  const handleCardForShowServicesChange = (event) => {
    setCardForShowServices(event.target.value);
  };

  const [idLaborBy, setIdLaborBy] = useState(null);

  const handleIdLaborByChange = (event) => {
    setIdLaborBy(event.target.value);
  };

  const [unionLabor, setUnionLabor] = useState(null);

  const handleUnionLaborChange = (event) => {
    setUnionLabor(event.target.value);
  };

  const [materialArrivalDate, setmaterialArrivalDate] = useState(new Date());

  useLayoutEffect(() => {
    if (user && settings) {
      if (user?.profile?.record) {
        let shipPrior =
          customerRec?.fields?.custentity_ng_eh_default_days_to_ship_pr;
        let shipAfter =
          customerRec?.fields?.custentity_ng_eh_default_days_to_ship_af;

        if (shipPrior || shipAfter) {
          setShipCheck({
            prior: Number(shipPrior) || 0,
            after: Number(shipAfter) || 0
          });
        }
      }
    }
  }, [settings, user]);

  useEffect(() => {
    console.log('Ship check:', shipCheck);
  }, [shipCheck]);

  const {
    acceptedFiles
    // isDragActive,
    // isDragAccept,
    // isDragReject,
    // getRootProps,
    // getInputProps
  } = useDropzone({
    accept:
      'image/jpeg, image/png, text/plain, application/pdf, application/docx, .gltf, .glb'
  });

  // const files = acceptedFiles.map((file, index) => (
  //   <ListItem disableGutters component="div" key={index}>
  //     <ListItemText primary={file.name} />
  //     <b>{file.size} bytes</b>
  //     <Divider />
  //   </ListItem>
  // ));

  const getAddressBook = () => {
    if (user && user?.profile) {
      let addressBook = user?.profile?.record?.sublists.addressbook;
      let addressBookKeys = Object.keys(addressBook);
      let addressArr = [];

      addressBookKeys.forEach((key) => {
        if (key !== 'currentline') {
          addressArr.push(addressBook[key]);
          setCurrAddressBook(addressArr);
        }
      });
    }
  };

  useEffect(() => {
    if (user) {
      getAddressBook();
    }
  }, [user]);

  const handleCreateProjectOpen = () => {
    setOpen(true);
  };

  const handleCreateProjectClose = (values) => {
    console.log('Values:', values);
    setOpen(false);
  };

  const handleCreateProjectSuccess = async (
    values,
    setStatus,
    setSubmitting,
    resetForm
  ) => {
    console.log('Values:', values);
    // eslint-disable-next-line camelcase
    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    console.log('Files Added?', acceptedFiles);

    const formData = new FormData();
    let allFileNames = acceptedFiles.map((fil, i) => `file${i}`);
    formData.append('fileObjNames', JSON.stringify(allFileNames));
    if (acceptedFiles && acceptedFiles.length !== 0) {
      // eslint-disable-next-line array-callback-return
      acceptedFiles.map((fl, i) => {
        formData.append(`file${i}`, fl);
      });
    }

    if (Object.keys(values).length !== 0) {
      let formInputValues = Object.keys(values);
      formInputValues.forEach((key) => {
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else {
          formData.append(key, values[key]);
        }
      });
    }

    let requestOptions = {
      method: 'POST',
      redirect: 'follow',
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    return new Promise(() => {
      // eslint-disable-next-line camelcase
      axios
        .post(
          // eslint-disable-next-line camelcase
          `${get_record_function_url}&type=PROJCREATE`,
          formData,
          requestOptions
        )
        .then((res) => res.data)
        .then((resJSON) => {
          if (resJSON.message.includes('created')) {
            resetForm();
            setStatus({ success: true });
            setSubmitting(false);
            // eslint-disable-next-line camelcase
            const user_suitelet =
              process.env.NODE_ENV === 'development'
                ? process.env.REACT_APP_GET_USER_SUITELET
                : window.env.REACT_APP_GET_USER_SUITELET;
            userMutate(user_suitelet);
            console.log('Created success!');
            enqueueSnackbar(t('A new project has been created successfully'), {
              variant: 'success',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          } else if (resJSON.saved) {
            setSubmitting(false);
            enqueueSnackbar(
              t('New project failed to add files but created successfully'),
              {
                variant: 'warning',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'right'
                },
                TransitionComponent: Zoom
              }
            );
          } else {
            setSubmitting(false);
            enqueueSnackbar(t('New project failed to save'), {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          }
          console.log(resJSON);
          return resJSON;
        })
        .catch((err) => {
          setStatus({ success: false });
          setSubmitting(false);
          console.error(err);
          enqueueSnackbar(t('New project failed to create'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
          Promise.reject(err);
        })
        .finally(() => {
          setOpen(false);
        });
    });
  };

  return (
    <>
      <Box
        display="flex"
        alignItems={{ xs: 'stretch', md: 'center' }}
        flexDirection={{ xs: 'column', md: 'row' }}
        justifyContent="space-between"
        sx={{
          padding: 3,
          backgroundColor: `${
            theme.palette.mode === 'dark'
              ? theme.colors.alpha.trueWhite[10]
              : theme.colors.alpha.white[70]
          }`,
          borderRadius: 1
        }}
      >
        <Box display="flex" alignItems="center">
          <Box>
            <Typography variant="h3" component="h3" gutterBottom>
              {t('Projects')}
            </Typography>
            {currentProject ? (
              <>
                <Typography variant="body1">
                  Shopping cart set to{' '}
                  <span>
                    <Chip
                      color="primary"
                      variant="outlined"
                      deleteIcon={
                        <CancelTwoTone
                          style={{ color: theme.colors.secondary.main }}
                        />
                      }
                      label={
                        <Link
                          color="text.primary"
                          component={RouterLink}
                          to={`/management/projects/single/${currentProject?.internalid}`}
                        >
                          {currentProject?.project}
                        </Link>
                      }
                      onDelete={() => setProject(null)}
                    />
                  </span>
                </Typography>
              </>
            ) : (
              <Typography variant="subtitle2">
                No{' '}
                <span>
                  <Link component={RouterLink} to="/management/projects/list">
                    project
                  </Link>
                </span>{' '}
                has been set to shopping cart
              </Typography>
            )}
          </Box>
        </Box>
        <Box mt={{ xs: 3, md: 0 }}>
          {settings.custrecord_ng_eh_ext_prj_options === 'T' ? (
            <Button
              sx={{
                mt: { xs: 2, sm: 0 }
              }}
              onClick={() => navigate(`/management/projects/create`)}
              variant="contained"
              startIcon={<AddTwoToneIcon fontSize="small" />}
            >
              {t('Create new project')}
            </Button>
          ) : (
            <Button
              sx={{
                mt: { xs: 2, sm: 0 }
              }}
              onClick={handleCreateProjectOpen}
              variant="contained"
              startIcon={<AddTwoToneIcon fontSize="small" />}
            >
              {t('Create new project')}
            </Button>
          )}
        </Box>
      </Box>
      {settings.custrecord_ng_eh_ext_prj_options === 'T' ? null : (
        <CreateNewProjectDialog
          open={open}
          mobile={mobile}
          handleCreateProjectClose={handleCreateProjectClose}
          handleCreateProjectSuccess={handleCreateProjectSuccess}
          shipCheck={shipCheck}
        />
      )}
    </>
  );
}

export default PageHeader;
