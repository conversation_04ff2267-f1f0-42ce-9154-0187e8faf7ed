import React, { memo, useState } from 'react';
import {
  alpha,
  Avatar,
  Badge,
  Box,
  Card,
  CardContent,
  Chip,
  Divider,
  Grid,
  Paper,
  Skeleton,
  Stack,
  styled,
  Tab,
  Tabs,
  Typography,
  useMediaQuery
} from '@mui/material';
import {
  ArrowDropDown,
  AttachFile,
  CalendarMonth,
  Category,
  CheckBox,
  DateRange,
  Description,
  FolderOpen,
  Info,
  Label,
  Link as LinkIcon,
  Numbers,
  OpenInNew,
  PunchClockTwoTone,
  RadioButtonChecked,
  Schedule,
  TextFields,
  ToggleOn
} from '@mui/icons-material';
import { useTheme } from '@mui/material/styles';
import axios from 'axios';
import useSWR from 'swr';
import { useTranslation } from 'react-i18next';
import { format, parseISO } from 'date-fns';
import DOMPurify from 'dompurify';
import parse from 'html-react-parser';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';
import { AnimatePresence, motion } from 'framer-motion';

// Styled components for beautiful design
const StyledCard = styled(Card)(({ theme }) => ({
  background:
    theme.palette.mode === 'dark'
      ? `linear-gradient(135deg, ${alpha(
          theme.palette.primary.dark,
          0.05
        )} 0%, ${alpha(theme.palette.primary.main, 0.03)} 100%)`
      : `linear-gradient(135deg, ${alpha(
          theme.palette.primary.light,
          0.08
        )} 0%, ${alpha(theme.palette.primary.main, 0.04)} 100%)`,
  backdropFilter: 'blur(10px)',
  borderRadius: theme.spacing(2),
  border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  overflow: 'visible',
  position: 'relative',
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: '4px',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
    borderRadius: `${theme.spacing(2)} ${theme.spacing(2)} 0 0`
  }
}));

const FieldCard = styled(Paper)(({ theme }) => ({
  padding: theme.spacing(2.5),
  borderRadius: theme.spacing(1.5),
  background:
    theme.palette.mode === 'dark'
      ? alpha(theme.palette.background.paper, 0.8)
      : theme.palette.background.paper,
  border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
  transition: 'all 0.3s ease',
  '&:hover': {
    transform: 'translateY(-2px)',
    boxShadow: theme.shadows[4],
    borderColor: alpha(theme.palette.primary.main, 0.3)
  }
}));

const StyledTabs = styled(Tabs)(({ theme }) => ({
  '& .MuiTabs-indicator': {
    height: 3,
    borderRadius: '3px 3px 0 0',
    background: `linear-gradient(90deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`
  }
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  textTransform: 'none',
  fontWeight: 600,
  fontFamily: 'Roboto',
  borderRadius: '10px',
  fontSize: theme.typography.pxToRem(15),
  marginRight: theme.spacing(1),
  color: theme.palette.text.secondary,
  '&.Mui-selected': {
    color: theme.palette.text.dark
  },
  '&:hover': {
    color: theme.palette.primary.dark,
    opacity: 1
  }
}));

const SectionHeader = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  marginBottom: theme.spacing(2),
  paddingBottom: theme.spacing(1.5),
  borderBottom: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
  '& .MuiTypography-root': {
    fontWeight: 600,
    color: theme.palette.text.primary
  }
}));

const GroupDescription = styled(Typography)(({ theme }) => ({
  marginTop: theme.spacing(1),
  marginBottom: theme.spacing(2),
  padding: theme.spacing(1.5),
  backgroundColor: alpha(theme.palette.info.main, 0.05),
  borderLeft: `4px solid ${theme.palette.info.main}`,
  borderRadius: theme.spacing(0.5),
  color: theme.palette.text.secondary,
  fontSize: theme.typography.pxToRem(14),
  lineHeight: 1.5,
  '&:empty': {
    display: 'none'
  }
}));

// Field type icon mapping
const getFieldIcon = (fieldType) => {
  const iconMap = {
    text: <TextFields />,
    number: <Numbers />,
    date: <CalendarMonth />,
    time: <PunchClockTwoTone />,
    daterange: <DateRange />,
    boolean: <ToggleOn />,
    checkbox: <CheckBox />,
    radio: <RadioButtonChecked />,
    select: <ArrowDropDown />,
    file: <AttachFile />,
    url: <LinkIcon />,
    textarea: <Description />,
    richtext: <Description />
  };
  return iconMap[fieldType] || <Info />;
};

// Tab panel component
function TabPanel({ children, value, index, ...other }) {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`project-tabpanel-${index}`}
      aria-labelledby={`project-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
          >
            {children}
          </motion.div>
        </Box>
      )}
    </div>
  );
}

// Empty state component
const EmptyState = ({ message }) => {
  const theme = useTheme();
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <Box
        sx={{
          textAlign: 'center',
          py: 4,
          px: 2,
          bgcolor: alpha(theme.palette.grey[100], 0.5),
          borderRadius: 2,
          border: `1px dashed ${theme.palette.grey[300]}`
        }}
      >
        <Info sx={{ fontSize: 48, color: theme.palette.grey[400], mb: 2 }} />
        <Typography color="text.secondary">{message}</Typography>
      </Box>
    </motion.div>
  );
};

const htmlFrom = (htmlString) => {
  const options = {
    replace: (domNode) => {
      /* if (domNode.type === 'tag' && domNode.name === 'img') {
            return null;
        } */
      return domNode;
    }
  };
  const cleanHtmlString = DOMPurify.sanitize(htmlString, {
    USE_PROFILES: { html: true }
  });
  const html = parse(cleanHtmlString, options);
  return html;
};

// Read-only field renderer
const ReadOnlyField = ({ field }) => {
  const theme = useTheme();
  const [isHovered, setIsHovered] = useState(false);

  const DynamicFieldValue = () => {
    if (!field.value && field.value !== 0 && field.value !== false) {
      return (
        <Typography color="text.secondary" variant="body2">
          N/A
        </Typography>
      );
    }

    switch (field.type) {
      case 'boolean':
      case 'checkbox':
        return (
          <Chip
            label={field.value ? 'Yes' : 'No'}
            color={field.value ? 'success' : 'default'}
            size="small"
            icon={field.value ? <CheckBox /> : null}
          />
        );

      case 'date':
        try {
          return (
            <Chip
              icon={<CalendarMonth />}
              label={format(parseISO(field.value), 'MMM dd, yyyy')}
              variant="outlined"
              color="primary"
            />
          );
        } catch {
          return <Typography>{field.value}</Typography>;
        }

      case 'time':
        try {
          return (
            <Chip
              icon={<Schedule />}
              label={format(parseISO(field.value), 'hh:mm a')}
              variant="outlined"
              color="primary"
            />
          );
        } catch {
          return <Typography>{field.value}</Typography>;
        }

      case 'daterange':
        try {
          const [start, end] = field.value.split(',');
          return (
            <Stack direction="row" spacing={1} alignItems="center">
              <Chip
                size="small"
                label={format(parseISO(start), 'MMM dd, yyyy')}
                color="primary"
                variant="outlined"
              />
              <Typography variant="caption">to</Typography>
              <Chip
                size="small"
                label={format(parseISO(end), 'MMM dd, yyyy')}
                color="primary"
                variant="outlined"
              />
            </Stack>
          );
        } catch {
          return <Typography>{field.value}</Typography>;
        }

      case 'file':
        return (
          <motion.div
            className="inline-block"
            animate={{
              scale: isHovered && field.fileUrl ? 1.02 : 1
            }}
            transition={{ duration: 0.2, ease: 'easeInOut' }}
          >
            <Chip
              icon={
                <motion.div
                  className="relative flex items-center justify-center w-5 h-5"
                  animate={{
                    scale: isHovered && field.fileUrl ? 1.05 : 1
                  }}
                  transition={{ duration: 0.25, ease: 'easeInOut' }}
                >
                  <motion.div
                    animate={{
                      opacity: isHovered && field.fileUrl ? 0 : 1,
                      scale: isHovered && field.fileUrl ? 0.8 : 1
                    }}
                    transition={{ duration: 0.2, ease: 'easeInOut' }}
                    className="absolute"
                  >
                    <AttachFile fontSize="small" />
                  </motion.div>
                  <motion.div
                    animate={{
                      opacity: isHovered && field.fileUrl ? 1 : 0,
                      scale: isHovered && field.fileUrl ? 1 : 0.8
                    }}
                    transition={{ duration: 0.2, ease: 'easeInOut' }}
                    className="absolute"
                  >
                    <OpenInNew fontSize="small" />
                  </motion.div>
                </motion.div>
              }
              label={field.value}
              color="secondary"
              variant="outlined"
              size="small"
              clickable={!!field.fileUrl}
              component={field.fileUrl ? 'a' : 'div'}
              href={field.fileUrl}
              target={field.fileUrl ? '_blank' : undefined}
              rel={field.fileUrl ? 'noopener noreferrer' : undefined}
              onMouseEnter={() => setIsHovered(true)}
              onMouseLeave={() => setIsHovered(false)}
              sx={{
                cursor: field.fileUrl ? 'pointer' : 'default',
                transition: 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)',
                transform:
                  isHovered && field.fileUrl
                    ? 'translateY(-1px)'
                    : 'translateY(0)',
                '&:hover': field.fileUrl
                  ? {
                      backgroundColor: 'secondary.light',
                      borderColor: 'secondary.main',
                      boxShadow: theme.shadows[4]
                    }
                  : {}
              }}
            />
          </motion.div>
        );

      case 'url':
        return (
          <Chip
            icon={<LinkIcon />}
            label="View Link"
            component="a"
            href={field.value}
            target="_blank"
            clickable
            color="info"
            variant="outlined"
            size="small"
          />
        );

      case 'richText':
        return (
          <Box
            className="ql-editor"
            sx={{
              '& *': { margin: 0 },
              '& p': { marginBottom: theme.spacing(1) },
              '& ul, & ol': { paddingLeft: theme.spacing(3) }
            }}
          >
            {htmlFrom(field.value)}
          </Box>
        );

      case 'select':
      case 'radio':
        if (field.multiple) {
          return (
            <Stack spacing={1}>
              {field.value.map((value) => {
                const displayValue = field.options.find(
                  (option) => option.value === value
                )?.text;
                return (
                  <Chip
                    key={value}
                    label={displayValue || value}
                    color="primary"
                    variant="outlined"
                    size="small"
                  />
                );
              })}
            </Stack>
          );
        }

        const displayValue = field.options.find(
          (option) => option.value === field.value
        )?.text;

        return (
          <Chip
            label={displayValue || field.displayValue || field.value}
            color="primary"
            variant="outlined"
            size="small"
          />
        );

      default:
        return (
          <Typography variant="body1" sx={{ whiteSpace: 'pre-wrap' }}>
            {field.value}
          </Typography>
        );
    }
  };

  return (
    <FieldCard elevation={0}>
      <Stack spacing={1.5}>
        <Box display="flex" alignItems="center" gap={1}>
          <Avatar
            sx={{
              width: 32,
              height: 32,
              bgcolor: alpha(theme.palette.primary.main, 0.1),
              color: theme.palette.primary.main
            }}
          >
            {getFieldIcon(field.type)}
          </Avatar>
          <Typography variant="subtitle2" color="text.secondary">
            {field.label}
          </Typography>
        </Box>
        <Box pl={5}>
          <DynamicFieldValue />
        </Box>
      </Stack>
    </FieldCard>
  );
};

// Main component
const ProjectFormReadOnlyView = memo(({ projectId, formId }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { getDeploymentUrl } = useHoistedUrls();

  const [selectedTab, setSelectedTab] = useState(0);
  const [formData, setFormData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // SWR fetcher
  const fetcher = (url) => axios.get(url).then((res) => res.data);

  // URL for fetching form data
  const getProjectFormDataUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_project_form_data_external
      : getDeploymentUrl.get_project_form_data_internal;

  // Fetch form data
  const { data: response, error: fetchError } = useSWR(
    projectId && formId
      ? `${getProjectFormDataUrl}&formId=${formId}&projectId=${projectId}`
      : null,
    fetcher,
    {
      revalidateOnFocus: false,
      onSuccess: (data) => {
        if (data.status === 'success' || data.formFields) {
          setFormData(data.data || data);
          setLoading(false);
        } else {
          setError('Failed to load form data');
          setLoading(false);
        }
      },
      onError: (err) => {
        console.error('Error fetching form data:', err);
        setError('Failed to load form data. Please try again.');
        setLoading(false);
      }
    }
  );

  const handleTabChange = (event, newValue) => {
    setSelectedTab(newValue);
  };

  // Group fields by their group property
  const groupFieldsBySection = () => {
    if (!formData?.formFields) return {};

    const sections = {};

    // First, handle fields without a group
    const ungroupedFields = formData.formFields.filter((field) => !field.group);
    if (ungroupedFields.length > 0) {
      sections['General'] = {
        fields: ungroupedFields,
        columns: 1, // Default to single column for ungrouped fields
        description: null // No description for general section
      };
    }

    // Then, group fields by their group property
    formData.formFields.forEach((field) => {
      if (field.group) {
        if (!sections[field.group]) {
          // Find the group configuration
          const groupConfig = formData.formGroups?.find(
            (g) => g.title === field.group
          );
          sections[field.group] = {
            fields: [],
            columns: groupConfig?.columns || '1',
            description: groupConfig?.description || null
          };
        }
        sections[field.group].fields.push(field);
      }
    });

    return sections;
  };

  // Calculate grid size based on columns configuration
  const getGridSize = (columns) => {
    const cols = parseInt(columns) || 1;
    return 12 / cols; // Material-UI uses 12-column grid
  };

  if (loading) {
    return (
      <StyledCard>
        <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
          <Box mb={3}>
            <Stack direction="row" alignItems="center" spacing={2} mb={1}>
              <Skeleton variant="circular" width={48} height={48} />
              <Box flex={1}>
                <Skeleton variant="text" width="60%" height={32} />
                <Skeleton variant="text" width="40%" height={20} />
              </Box>
            </Stack>
            <Divider sx={{ mt: 2 }} />
          </Box>
          <Stack spacing={3}>
            {[1, 2].map((section) => (
              <Box key={section}>
                <Stack direction="row" alignItems="center" spacing={2} mb={2}>
                  <Skeleton variant="circular" width={28} height={28} />
                  <Skeleton variant="text" width={150} height={24} />
                </Stack>
                <Grid container spacing={2}>
                  {[1, 2, 3, 4].map((field) => (
                    <Grid item xs={12} sm={6} key={field}>
                      <Skeleton
                        variant="rounded"
                        height={100}
                        sx={{ borderRadius: 1.5 }}
                        animation="wave"
                      />
                    </Grid>
                  ))}
                </Grid>
              </Box>
            ))}
          </Stack>
        </CardContent>
      </StyledCard>
    );
  }

  if (error) {
    return (
      <Box textAlign="center" py={4}>
        <Typography color="error">{error}</Typography>
      </Box>
    );
  }

  if (!formData) {
    return (
      <Box textAlign="center" py={4}>
        <Typography>No form data available</Typography>
      </Box>
    );
  }

  const layout = formData.formLayout || 'stack';
  const sections = groupFieldsBySection();
  const sectionKeys = Object.keys(sections);

  // Order sections to ensure groups follow the order in formGroups
  const orderedSectionKeys = formData.formGroups
    ? ['General', ...formData.formGroups.map((g) => g.title)].filter(
        (key) => sections[key]
      )
    : sectionKeys;

  return (
    <StyledCard>
      <CardContent sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
        <Box mb={3}>
          <Stack direction="row" alignItems="center" spacing={2} mb={1}>
            <Avatar
              sx={{
                bgcolor: theme.palette.primary.main,
                width: 48,
                height: 48
              }}
            >
              <FolderOpen />
            </Avatar>
            <Box>
              <Typography variant="h5" fontWeight="bold">
                {formData.formSelected?.name ||
                  formData.formName ||
                  t('Project Details')}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {t('View project information')}
              </Typography>
            </Box>
          </Stack>
          <Divider sx={{ mt: 2 }} />
        </Box>

        {layout === 'tab' ? (
          <>
            <StyledTabs
              value={selectedTab}
              onChange={handleTabChange}
              aria-label="project form sections"
              variant={mobile ? 'scrollable' : 'standard'}
              scrollButtons="auto"
            >
              {orderedSectionKeys.map((section, index) => (
                <StyledTab
                  key={section}
                  label={
                    <Stack direction="row" spacing={1} alignItems="center">
                      <Category fontSize="small" />
                      <Typography component="span">{section}</Typography>
                      <Badge
                        sx={{
                          pl: 1
                        }}
                        badgeContent={sections[section].fields.length}
                        color="primary"
                      />
                    </Stack>
                  }
                />
              ))}
            </StyledTabs>

            <AnimatePresence mode="wait">
              {orderedSectionKeys.map((section, index) => (
                <TabPanel key={section} value={selectedTab} index={index}>
                  <motion.div
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    variants={{
                      hidden: { opacity: 0 },
                      visible: {
                        opacity: 1,
                        transition: {
                          staggerChildren: 0.05
                        }
                      },
                      exit: { opacity: 0 }
                    }}
                  >
                    {/* Display group description if available */}
                    {sections[section].description && (
                      <motion.div
                        variants={{
                          hidden: { y: -10, opacity: 0 },
                          visible: {
                            y: 0,
                            opacity: 1,
                            transition: {
                              type: 'spring',
                              stiffness: 100,
                              damping: 12
                            }
                          }
                        }}
                      >
                        <GroupDescription variant="body2">
                          {sections[section].description}
                        </GroupDescription>
                      </motion.div>
                    )}

                    {sections[section].fields.length === 0 ? (
                      <EmptyState
                        message={`No fields available in ${section} section`}
                      />
                    ) : (
                      <Grid container spacing={2}>
                        {sections[section].fields.map((field, fieldIndex) => (
                          <Grid
                            item
                            xs={12}
                            sm={
                              mobile
                                ? 12
                                : getGridSize(sections[section].columns)
                            }
                            key={field.name}
                          >
                            <motion.div
                              variants={{
                                hidden: { y: 20, opacity: 0 },
                                visible: {
                                  y: 0,
                                  opacity: 1,
                                  transition: {
                                    type: 'spring',
                                    stiffness: 100,
                                    damping: 12
                                  }
                                }
                              }}
                            >
                              <ReadOnlyField field={field} />
                            </motion.div>
                          </Grid>
                        ))}
                      </Grid>
                    )}
                  </motion.div>
                </TabPanel>
              ))}
            </AnimatePresence>
          </>
        ) : (
          <Stack spacing={4}>
            {orderedSectionKeys.map((section, sectionIndex) => (
              <motion.div
                key={section}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{
                  delay: sectionIndex * 0.1,
                  type: 'spring',
                  stiffness: 100,
                  damping: 15
                }}
              >
                <Box>
                  <SectionHeader
                    sx={{ mb: sections[section].description ? 2 : 3 }}
                  >
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{
                        delay: sectionIndex * 0.1 + 0.2,
                        type: 'spring',
                        stiffness: 200
                      }}
                    >
                      <Avatar
                        sx={{
                          width: 28,
                          height: 28,
                          bgcolor: alpha(theme.palette.primary.main, 0.1),
                          color: theme.palette.primary.main,
                          mr: 1.5
                        }}
                      >
                        <Label fontSize="small" />
                      </Avatar>
                    </motion.div>
                    <Typography variant="h6">{section}</Typography>
                    <Box sx={{ flexGrow: 1 }} />
                    <Chip
                      size="small"
                      label={`${sections[section].fields.length} field${
                        sections[section].fields.length > 1 ? 's' : ''
                      }`}
                      sx={{
                        bgcolor: alpha(theme.palette.primary.main, 0.1),
                        color: theme.palette.primary.main
                      }}
                    />
                  </SectionHeader>

                  {/* Display group description if available */}
                  {sections[section].description && (
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{
                        delay: sectionIndex * 0.1 + 0.3,
                        type: 'spring',
                        stiffness: 100,
                        damping: 12
                      }}
                    >
                      <GroupDescription variant="body2" sx={{ mb: 3 }}>
                        {sections[section].description}
                      </GroupDescription>
                    </motion.div>
                  )}

                  {sections[section].fields.length === 0 ? (
                    <EmptyState
                      message={`No fields available in ${section} section`}
                    />
                  ) : (
                    <Grid container spacing={2}>
                      {sections[section].fields.map((field, fieldIndex) => (
                        <Grid
                          item
                          xs={12}
                          sm={
                            mobile ? 12 : getGridSize(sections[section].columns)
                          }
                          key={field.name}
                        >
                          <motion.div
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{
                              delay: sectionIndex * 0.1 + fieldIndex * 0.05,
                              type: 'spring',
                              stiffness: 100,
                              damping: 12
                            }}
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <ReadOnlyField field={field} />
                          </motion.div>
                        </Grid>
                      ))}
                    </Grid>
                  )}
                </Box>
              </motion.div>
            ))}
          </Stack>
        )}
      </CardContent>
    </StyledCard>
  );
});

ProjectFormReadOnlyView.defaultProps = {
  projectId: null,
  formId: null
};

export default ProjectFormReadOnlyView;
