import React, { useEffect, useRef, useState } from 'react';

import { Helmet } from 'react-helmet-async';
import Footer from 'src/components/Footer';

import {
  Badge,
  Box,
  Button,
  Card,
  CardContent,
  Grid,
  styled,
  Tab,
  Tabs,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { Player } from '@lottiefiles/react-lottie-player';
import useSWR from 'swr';

import AssignmentTwoToneIcon from '@mui/icons-material/AssignmentTwoTone';
import Inventory2TwoToneIcon from '@mui/icons-material/Inventory2TwoTone';
import AccountTreeTwoToneIcon from '@mui/icons-material/AccountTreeTwoTone';
import CasesTwoToneIcon from '@mui/icons-material/CasesTwoTone';

import ProfileCover from './ProfileCover';
import EditProfileTab from './EditProfileTab';
import NotificationsTab from './NotificationsTab';
import SecurityTab from './SecurityTab';
import {
  EmptyFiles,
  LoadingPaperAirplane,
  LoadingTruck
} from '../../../../utils/lottieAnimate';
import FilesTab from './FilesTab';
import DiscussionNavigation from './DiscussionNavigation';
import Items from './Items';
import { useTheme } from '@mui/material/styles';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';
import useSettings from '../../../../hooks/useSettings';

import ProjectTaskTable from './ProjectTaskTable';

const TabsWrapper = styled(Tabs)(
  () => `
    .MuiTabs-scrollableX {
      overflow-x: auto !important;

      .MuiTabs-indicator {
        box-shadow: none;
      }
    }
`
);
// eslint-disable-next-line no-unused-vars
const LabelWrapper = styled(Box)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(10)};
    font-weight: bold;
    text-transform: uppercase;
    border-radius: ${theme.general.borderRadiusSm};
    padding: ${theme.spacing(0.5, 1)};
  `
);

function ProjectDetail() {
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useSettings();
  const { projectId } = useParams();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const [isUpdating, setUpdating] = useState(false);
  const getProjectContentsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_project_details_external
      : getDeploymentUrl.get_project_details_internal;

  const {
    data: project,
    error,
    mutate: mutateProj
  } = useSWR(`${getProjectContentsUrl}&projId=${projectId}`);

  const [currentTab, setCurrentTab] = useState('tasks');

  const profileCoverEl = useRef(null);
  const [tabContainerHeight, setTabContainerHeight] = useState(0);

  useEffect(() => {
    setTabContainerHeight(profileCoverEl.current?.clientHeight);
  });

  const tabs = [
    {
      value: 'tasks',
      label: t('Tasks'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          variant="dot"
          badgeContent={project && project?.tasks && project?.tasks.length}
        >
          <AssignmentTwoToneIcon />
        </Badge>
      )
    },
    {
      value: 'files',
      label: t('Files'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          variant="dot"
          badgeContent={project && project?.files && project?.files.length}
        >
          <Inventory2TwoToneIcon />
        </Badge>
      )
    },
    {
      value: 'items',
      label: t('Items'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          variant="dot"
          badgeContent={
            project?.salesOrderItems && project?.salesOrderItems.length
          }
        >
          <AccountTreeTwoToneIcon />
        </Badge>
      )
    }
    /*  { value: 'edit_profile', label: t('Edit Profile') },
    { value: 'notifications', label: t('Notifications') },
    { value: 'security', label: t('Passwords/Security') } */
  ];

  useEffect(() => {
    if (settings?.custrecord_enable_cases_in_portal === 'T') {
      tabs.push({
        value: 'cases',
        label: t('Cases'),
        icon: (
          <Badge
            sx={{ zIndex: 1000 }}
            color="primary"
            variant="dot"
            badgeContent={project && project?.cases && project?.cases.length}
          >
            <CasesTwoToneIcon />
          </Badge>
        )
      });
    }
  }, [settings]);

  const handleTabsChange = (_event, value) => {
    setCurrentTab(value);
  };

  useEffect(() => {
    console.log('Project Id:', projectId);
    console.log('Project:', project);
  }, [project]);

  const ForwardTypography = React.forwardRef((props, ref) => (
    <Typography ref={ref} {...props}>
      {props.children}
    </Typography>
  ));

  const MotionTypography = motion(ForwardTypography);

  const ForwardButton = React.forwardRef((props, ref) => (
    <Button ref={ref} {...props}>
      {props.children}
    </Button>
  ));

  const MotionMuiButton = motion(ForwardButton);

  const containerStyles = `
     perspective: 500;
     height: 400px;
     /* center card item */
     display: flex;
     justify-content: center;
     align-items: center;
  `;

  const [angle] = React.useState(8);

  // we replace the useState with two motion values. One for each axis.
  // Since we want the card to start out flat we set the initial
  // values to x=0.5 y=0.5 which equals to no transformation
  const y = useMotionValue(0.5);
  const x = useMotionValue(0.5);

  const rotateY = useTransform(x, [0, 1], [-angle, angle], {
    clamp: true
  });
  const rotateX = useTransform(y, [0, 1], [angle, -angle], {
    clamp: true
  });

  const onMove = (e) => {
    // get position information for the card
    const bounds = e.currentTarget.getBoundingClientRect();

    // set x,y local coordinates
    const xValue = (e.clientX - bounds.x) / e.currentTarget.clientWidth;
    const yValue = (e.clientY - bounds.y) / e.currentTarget.clientHeight;

    // update MotionValues
    x.set(xValue, true);
    y.set(yValue, true);
  };

  if (isUpdating) {
    return (
      <Grid container spacing={3} justifyContent="center" alignItems="center">
        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              p={5}
              pt={20}
            >
              <Card>
                <CardContent>
                  <Player
                    loop
                    autoplay
                    controls={false}
                    src={LoadingPaperAirplane()}
                    style={{
                      height: '100%',
                      width: '90%',
                      borderRadius: '1rem'
                    }}
                  />
                </CardContent>
              </Card>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <MotionTypography
              variant="h2"
              initial={{
                opacity: 0,
                y: -15
              }}
              animate={{
                opacity: 1,
                y: 0
              }}
              transition={{
                ease: [0.17, 0.67, 0.83, 0.67],
                y: { type: 'spring', stiffness: 100 },
                default: {
                  duration: 1,
                  repeat: Infinity,
                  repeatDelay: 1,
                  repeatType: 'reverse'
                }
              }}
              style={{
                textAlign: 'center'
              }}
            >
              Uploading files to project...
            </MotionTypography>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  if (error) {
    return (
      <Grid container spacing={3} alignItems="center" sx={{ flexGrow: 1 }}>
        <Grid item xs={12}>
          <Grid
            contianer
            justifyContent="center"
            alignItems="center"
            spacing={3}
          >
            <Grid item xs={12} alignSelf="center">
              <motion.div styles={containerStyles}>
                <motion.div
                  onPointerMove={onMove}
                  style={{
                    rotateY,
                    rotateX,
                    textAlign: 'center',
                    fontFamily: 'Montserrat'
                  }}
                  initial={{
                    y: 1000,
                    opacity: 0
                  }}
                  animate={{
                    y: 0,
                    scale: 1.5,
                    opacity: 1,
                    alignItems: 'center'
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 260,
                    damping: 20
                  }}
                >
                  <Box
                    p={5}
                    pt={20}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Card>
                      <CardContent>
                        <Box p={3}>
                          <Player
                            src={EmptyFiles()}
                            autoplay
                            loop
                            controls={false}
                            style={{
                              height: '80%',
                              width: '80%',
                              borderRadius: '1rem'
                            }}
                          />
                        </Box>
                        <MotionTypography
                          sx={{ fontFamily: 'Montserrat' }}
                          component={ForwardTypography}
                        >
                          Error Occurred Loading Project!
                        </MotionTypography>
                        <MotionTypography
                          gutterBottom
                          variant="subtitle2"
                          color="secondary"
                          sx={{ fontFamily: 'Montserrat' }}
                          fontSize="small"
                        >
                          Check permissions or contact administrator for details
                        </MotionTypography>
                        <Box p={3}>
                          <MotionMuiButton
                            variant="outlined"
                            fullWidth
                            transition={{
                              type: 'fade'
                            }}
                            whileHover={{
                              backdropFilter: 'blur(2px)'
                            }}
                            whileTap={{
                              scale: 1.05
                            }}
                            onClick={() => navigate('/management/projects')}
                          >
                            Go Back
                          </MotionMuiButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                </motion.div>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  if (!project) {
    return (
      <Grid container spacing={3} justifyContent="center" alignItems="center">
        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              p={5}
              pt={20}
            >
              <Card>
                <CardContent>
                  <Player
                    src={LoadingTruck()}
                    autoplay
                    loop
                    controls={false}
                    style={{
                      height: '50%',
                      width: '50%',
                      borderRadius: '1rem'
                    }}
                  />
                </CardContent>
              </Card>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <MotionTypography
              variant="h2"
              initial={{
                opacity: 0,
                y: -15
              }}
              animate={{
                opacity: 1,
                y: 0
              }}
              transition={{
                ease: [0.17, 0.67, 0.83, 0.67],
                y: { type: 'spring', stiffness: 100 },
                default: {
                  duration: 1,
                  repeat: Infinity,
                  repeatDelay: 1,
                  repeatType: 'reverse'
                }
              }}
              style={{
                textAlign: 'center'
              }}
            >
              Loading Project Details...
            </MotionTypography>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  if (project && !project?.name) {
    return (
      <Grid container spacing={3} alignItems="center" sx={{ flexGrow: 1 }}>
        <Grid item xs={12}>
          <Grid
            contianer
            justifyContent="center"
            alignItems="center"
            spacing={3}
          >
            <Grid item xs={12} alignSelf="center">
              <motion.div styles={containerStyles}>
                <motion.div
                  onPointerMove={onMove}
                  style={{
                    rotateY,
                    rotateX,
                    textAlign: 'center',
                    fontFamily: 'Montserrat'
                  }}
                  initial={{
                    y: 1000,
                    opacity: 0
                  }}
                  animate={{
                    y: 0,
                    scale: 1.5,
                    opacity: 1,
                    alignItems: 'center'
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 260,
                    damping: 20
                  }}
                >
                  <Box
                    p={5}
                    pt={20}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Card>
                      <CardContent>
                        <Box p={3}>
                          <Player
                            src={EmptyFiles()}
                            autoplay
                            loop
                            controls={false}
                            style={{
                              height: '80%',
                              width: '80%',
                              borderRadius: '1rem'
                            }}
                          />
                        </Box>
                        <MotionTypography
                          sx={{ fontFamily: 'Montserrat' }}
                          component={ForwardTypography}
                        >
                          Project Not Found!
                        </MotionTypography>
                        <MotionTypography
                          gutterBottom
                          variant="subtitle2"
                          color="secondary"
                          sx={{ fontFamily: 'Montserrat' }}
                          fontSize="small"
                        >
                          No information was found with this project id
                        </MotionTypography>
                        <Box p={3}>
                          <MotionMuiButton
                            variant="outlined"
                            fullWidth
                            transition={{
                              type: 'fade'
                            }}
                            whileHover={{
                              backdropFilter: 'blur(2px)'
                            }}
                            whileTap={{
                              scale: 1.05
                            }}
                            onClick={() => navigate('/management/projects')}
                          >
                            Go Back
                          </MotionMuiButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                </motion.div>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  return (
    <>
      <Helmet>
        <title>Project Details</title>
      </Helmet>
      <Box
        sx={{
          mt: 3,
          minHeight: '95vh'
        }}
      >
        <Grid
          sx={{
            px: 4
          }}
          container
          direction="row"
          justifyContent="center"
          alignItems="stretch"
          spacing={4}
        >
          <Grid item xs={12} ref={profileCoverEl}>
            <ProfileCover project={project} mutateProj={mutateProj} />
          </Grid>
          {/* <Grid item xs={12} md={8}> */}
          {/*  <DateActivity project={project} /> */}
          {/* </Grid> */}
          {/* <Grid item xs={12} md={4}> */}
          {/*  <RecentActivity project={project} /> */}
          {/* </Grid> */}
          <Grid item xs={12}>
            <TabsWrapper
              onChange={handleTabsChange}
              value={currentTab}
              variant={mobile ? 'fullWidth' : 'scrollable'}
              scrollButtons="auto"
              textColor="secondary"
              indicatorColor="primary"
              centered={mobile}
            >
              {tabs.map((tab) =>
                mobile ? (
                  <Tab
                    key={tab.value}
                    aria-label={tab.label}
                    value={tab.value}
                    iconPosition="bottom"
                    icon={tab.icon}
                  />
                ) : (
                  <Tab
                    key={tab.value}
                    label={tab.label}
                    value={tab.value}
                    iconPosition="end"
                    icon={tab.icon}
                  />
                )
              )}
            </TabsWrapper>
          </Grid>
          <Grid
            item
            xs={12}
            sx={{ height: `calc(${tabContainerHeight}px + 70vh)` }}
          >
            {currentTab === 'tasks' && (
              <ProjectTaskTable
                tasks={project?.tasks || []}
                className="h-full"
              />
            )}
            {currentTab === 'items' && <Items project={project} />}
            {currentTab === 'files' && (
              <FilesTab
                tabHeight={tabContainerHeight}
                files={project?.files}
                project={project}
                mutateProj={mutateProj}
                setUpdating={(e) => setUpdating(e)}
                isUpdating={isUpdating}
              />
            )}
            {currentTab === 'cases' && (
              <DiscussionNavigation
                key={`cases-${projectId}`}
                cases={project?.cases}
              />
            )}
            {currentTab === 'edit_profile' && <EditProfileTab />}
            {currentTab === 'notifications' && <NotificationsTab />}
            {currentTab === 'security' && <SecurityTab />}
          </Grid>
        </Grid>
      </Box>
      <Footer />
    </>
  );
}

export default ProjectDetail;
