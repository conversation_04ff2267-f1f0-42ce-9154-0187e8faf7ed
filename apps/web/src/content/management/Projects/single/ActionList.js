import {
  Avatar,
  Box,
  But<PERSON>,
  Card,
  CardHeader,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Typography,
  useTheme
} from '@mui/material';
import { format } from 'date-fns';

import { useTranslation } from 'react-i18next';
import ExpandMoreTwoToneIcon from '@mui/icons-material/ExpandMoreTwoTone';
import LocalShippingTwoToneIcon from '@mui/icons-material/LocalShippingTwoTone';
import KeyboardReturnTwoToneIcon from '@mui/icons-material/KeyboardReturnTwoTone';
import CampaignTwoToneIcon from '@mui/icons-material/CampaignTwoTone';
import DirectionsRunTwoToneIcon from '@mui/icons-material/DirectionsRunTwoTone';
import WarehouseTwoToneIcon from '@mui/icons-material/WarehouseTwoTone';
import ExitToAppTwoToneIcon from '@mui/icons-material/ExitToAppTwoTone';
import HailTwoToneIcon from '@mui/icons-material/HailTwoTone';

import Scrollbar from '../../../../components/Scrollbar';

function ActionList({ project }) {
  const { t } = useTranslation();
  const theme = useTheme();

  return (
    <Card variant="outlined">
      <CardHeader
        sx={{
          p: 2,
          background: `${theme.colors.alpha.black[5]}`
        }}
        disableTypography
        title={
          <Typography
            variant="h4"
            sx={{
              fontSize: `${theme.typography.pxToRem(16)}`
            }}
          >
            {t('Latest Action Dates')}
          </Typography>
        }
        action={
          <Button
            variant="contained"
            size="small"
            endIcon={<ExpandMoreTwoToneIcon />}
            color="primary"
          >
            {t('Export')}
          </Button>
        }
      />
      <Divider />
      <Box
        sx={{
          height: 560
        }}
      >
        <Scrollbar>
          <List disablePadding>
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <LocalShippingTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Ship Date')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.shipdate
                      ? format(new Date(project.shipdate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <KeyboardReturnTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Return Date')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.returndate
                      ? format(new Date(project.returndate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <WarehouseTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Material Arrival')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.materialarrival
                      ? format(
                          new Date(project.materialarrival),
                          'MMMM dd yyyy'
                        )
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <DirectionsRunTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Move In Date')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.moveindate
                      ? format(new Date(project.moveindate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <CampaignTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Event Start')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.eventstartdate
                      ? format(new Date(project.eventstartdate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <ExitToAppTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Event End')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.eventenddate
                      ? format(new Date(project.eventenddate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <DirectionsRunTwoToneIcon
                    style={{ transform: 'rotateY(180deg)' }}
                  />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Move Out')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.moveoutdate
                      ? format(new Date(project.moveoutdate), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
            <Divider />
            <ListItem
              sx={{
                p: 2
              }}
            >
              <ListItemAvatar
                sx={{
                  mr: 2,
                  display: 'flex',
                  alignItems: 'center',
                  minWidth: 0
                }}
              >
                <Avatar
                  variant="rounded"
                  sx={{
                    background: `${theme.colors.alpha.black[10]}`,
                    color: `${theme.colors.primary.main}`,
                    width: 64,
                    height: 64
                  }}
                >
                  <HailTwoToneIcon />
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Typography gutterBottom variant="h4">
                    {t('Material Pickup')}
                  </Typography>
                }
                secondary={
                  <Typography variant="subtitle2">
                    {project.materialpickup
                      ? format(new Date(project.materialpickup), 'MMMM dd yyyy')
                      : 'None Selected'}
                  </Typography>
                }
              />
            </ListItem>
          </List>
        </Scrollbar>
      </Box>
    </Card>
  );
}

export default ActionList;
