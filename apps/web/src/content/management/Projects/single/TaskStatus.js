import React from 'react';
import {
  alpha,
  Box,
  Card,
  CardHeader,
  Divider,
  IconButton,
  LinearProgress,
  linearProgressClasses,
  List,
  ListItem,
  ListItemText,
  styled,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';

import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import Scrollbar from 'src/components/Scrollbar';
import { formatDistance } from 'date-fns';
import ChatBubbleOutlineTwoToneIcon from '@mui/icons-material/ChatBubbleOutlineTwoTone';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';
import Cookies from 'js-cookie';
import { motion } from 'framer-motion';

const CardContentWrapper = styled(Box)(
  ({ theme }) => `
    background: ${theme.colors.alpha.white[100]};
    border-radius: ${theme.general.borderRadius};
  `
);

const LabelWrapper = styled(Box)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(10)};
    font-weight: bold;
    text-transform: uppercase;
    border-radius: ${theme.general.borderRadiusSm};
    padding: ${theme.spacing(0.9, 1.5, 0.7)};
    line-height: 1;
  `
);

const ListWrapper = styled(List)(
  () => `
    .MuiDivider-root:last-of-type {
        display: none;
    }
  `
);

// eslint-disable-next-line no-unused-vars
const LinearProgressWarning = styled(LinearProgress)(
  ({ theme }) => `
        height: 6px;
        border-radius: ${theme.general.borderRadiusLg};

        &.${linearProgressClasses.colorPrimary} {
            background: ${alpha(theme.colors.alpha.black[100], 0.1)};
        }
        
        & .${linearProgressClasses.bar} {
            border-radius: ${theme.general.borderRadiusLg};
            background: ${theme.colors.gradients.orange1};
        }
    `
);

const LinearProgressSuccess = styled(LinearProgress)(
  ({ theme }) => `
        height: 6px;
        border-radius: ${theme.general.borderRadiusLg};

        &.${linearProgressClasses.colorPrimary} {
            background: ${alpha(theme.colors.alpha.black[100], 0.1)};
        }
        
        & .${linearProgressClasses.bar} {
            border-radius: ${theme.general.borderRadiusLg};
            background: ${theme.colors.gradients.green1};
        }
    `
);

function TaskStatus({ project }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { getDeploymentUrl } = useHoistedUrls();
  const chatBaseUrl = getDeploymentUrl?.get_cs_chat_url;

  const renderProjectTask = (tsk) => {
    let renderingTask = null;
    switch (tsk.status) {
      case 'Not Started':
        renderingTask = (
          <ListItem
            sx={{
              p: 2
            }}
          >
            <ListItemText
              primary={
                <>
                  <Typography color="text.primary" variant="h4">
                    {tsk.name}
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="subtitle2"
                    component="span"
                    sx={{
                      fontSize: `${theme.typography.pxToRem(13)}`
                    }}
                  >
                    &nbsp;{tsk.startdate} - {tsk.enddate}
                  </Typography>
                </>
              }
              secondary={<></>}
            />
            <Box alignSelf="flex-start">
              <LabelWrapper
                sx={{
                  background: `${theme.colors.error.main}`,
                  color: `${theme.palette.getContrastText(
                    theme.colors.error.dark
                  )}`
                }}
              >
                {t('Not Started')}
              </LabelWrapper>
            </Box>
          </ListItem>
        );
        break;
      case 'Completed':
        renderingTask = (
          <ListItem
            sx={{
              p: 2
            }}
          >
            <ListItemText
              primary={
                <>
                  <Typography color="text.primary" variant="h4">
                    {tsk.name}
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="subtitle2"
                    component="span"
                    sx={{
                      fontSize: `${theme.typography.pxToRem(13)}`
                    }}
                  >
                    &nbsp;{tsk.startdate} - {tsk.enddate}
                  </Typography>
                </>
              }
              secondary={
                <>
                  <LinearProgressSuccess
                    sx={{
                      mt: 1,
                      mb: 0.5
                    }}
                    variant="determinate"
                    value={parseFloat(tsk.progress)}
                  />
                  <Box mt={0.7} display="flex" alignItems="center">
                    <Typography
                      sx={{
                        fontSize: `${theme.typography.pxToRem(13)}`,
                        fontWeight: 800
                      }}
                      href="#"
                      variant="h6"
                    >
                      {tsk.company.split(':')[0].trim()}
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: `${theme.typography.pxToRem(12)}`,
                        pl: 1
                      }}
                      variant="subtitle2"
                    >
                      {t('end date is')}{' '}
                      {formatDistance(new Date(tsk.enddate), new Date(), {
                        addSuffix: true
                      })}
                    </Typography>
                  </Box>
                </>
              }
            />
            <Box alignSelf="flex-start">
              <LabelWrapper
                sx={{
                  background: `${theme.colors.success.main}`,
                  color: `${theme.palette.getContrastText(
                    theme.colors.success.dark
                  )}`
                }}
              >
                {t('Completed')}
              </LabelWrapper>
            </Box>
          </ListItem>
        );
        break;
      case 'In Progress':
        renderingTask = (
          <ListItem
            sx={{
              p: 2
            }}
          >
            <ListItemText
              primary={
                <>
                  <Typography color="text.primary" variant="h4">
                    {tsk.name}
                  </Typography>
                  <Typography
                    gutterBottom
                    variant="subtitle2"
                    component="span"
                    sx={{
                      fontSize: `${theme.typography.pxToRem(13)}`
                    }}
                  >
                    &nbsp;{tsk.startdate} - {tsk.enddate}
                  </Typography>
                </>
              }
              secondary={
                <>
                  <Box p={1}>
                    <LinearProgressSuccess
                      sx={{
                        mt: 1,
                        mb: 0.5
                      }}
                      variant="determinate"
                      value={
                        parseFloat(tsk.progress) > 100
                          ? 100
                          : parseFloat(tsk.progress)
                      }
                    />
                  </Box>
                  <Box mt={0.7} display="flex" alignItems="center">
                    <Typography
                      sx={{
                        fontSize: `${theme.typography.pxToRem(13)}`,
                        fontWeight: 800
                      }}
                      href="#"
                      variant="h6"
                    >
                      {tsk.company.split(':')[0].trim()}
                    </Typography>
                    <Typography
                      sx={{
                        fontSize: `${theme.typography.pxToRem(12)}`,
                        pl: 1
                      }}
                      variant="subtitle2"
                    >
                      {t('end date is')}{' '}
                      {formatDistance(new Date(tsk.enddate), new Date(), {
                        addSuffix: true
                      })}
                    </Typography>
                  </Box>
                </>
              }
            />
            <Box alignSelf="flex-start">
              <LabelWrapper
                sx={{
                  background: `${theme.colors.info.main}`,
                  color: `${theme.palette.getContrastText(
                    theme.colors.info.dark
                  )}`
                }}
              >
                {t('In Progress')}
              </LabelWrapper>
            </Box>
          </ListItem>
        );
        break;
      default:
        console.error('Status not found!', tsk.status);
    }

    if (renderingTask && chatBaseUrl) {
      const chatUrl = `${chatBaseUrl}?taskId=${tsk.internalid}&mode=view`;
      const isChatEnabled = tsk.externalChat;
      const chatButton = (
        <Tooltip
          title={
            !isChatEnabled
              ? t('Chat is not enabled externally for this task')
              : t('Open Task Chat')
          }
          arrow
        >
          <span>
            <IconButton
              size="small"
              sx={{ ml: 0.5 }}
              disabled={!isChatEnabled}
              onClick={() => {
                if (isChatEnabled) {
                  const isDevelopment = process.env.NODE_ENV === 'development';
                  const baseUrl = isDevelopment
                    ? 'http://localhost:5173/'
                    : chatBaseUrl;

                  if (baseUrl) {
                    const chatUrl = `${baseUrl}?taskId=${tsk.internalid}&mode=view`;
                    Cookies.set('ept-taskChatId', tsk.internalid, {
                      expires: 7
                    });
                    Cookies.set('ept-internalChat', 'F', { expires: 7 });
                    navigate('/view-chat', {
                      state: { chatUrl, taskName: tsk.name }
                    });
                  } else {
                    console.error('Chat base URL is not configured.');
                  }
                }
              }}
              color="primary"
            >
              <ChatBubbleOutlineTwoToneIcon fontSize="small" />
              {isChatEnabled && (
                <>
                  {/* Animated ring pulse */}
                  <motion.span
                    initial={{ scale: 0.7, opacity: 0.5 }}
                    animate={{
                      scale: [0.7, 1.6],
                      opacity: [0.5, 0]
                    }}
                    transition={{
                      duration: 1.3,
                      repeat: Infinity,
                      repeatType: 'loop',
                      ease: 'easeInOut'
                    }}
                    style={{
                      position: 'absolute',
                      top: 0,
                      right: 0,
                      width: 18,
                      height: 18,
                      borderRadius: '50%',
                      background: 'rgba(76, 175, 80, 0.25)',
                      zIndex: 1,
                      filter: 'blur(2px)'
                    }}
                  />
                  {/* Static dot */}
                  <span
                    style={{
                      position: 'absolute',
                      top: 4,
                      right: 4,
                      width: 8,
                      height: 8,
                      borderRadius: '50%',
                      background: '#4caf50',
                      zIndex: 2,
                      boxShadow: '0 0 2px 1px rgba(76,175,80,0.3)'
                    }}
                  />
                </>
              )}
            </IconButton>
          </span>
        </Tooltip>
      );

      const originalListItem = renderingTask;
      const originalChildren = React.Children.toArray(
        originalListItem.props.children
      );
      const boxIndex = originalChildren.findIndex(
        (child) => child.type === Box && child.props.alignSelf === 'flex-start'
      );

      if (boxIndex !== -1) {
        const targetBox = originalChildren[boxIndex];
        const labelWrapper = targetBox.props.children;

        const newInnerBox = (
          <Box display="flex" alignItems="center">
            {labelWrapper}
            {chatButton}
          </Box>
        );

        const newOuterBox = React.cloneElement(targetBox, {
          children: newInnerBox
        });

        originalChildren[boxIndex] = newOuterBox;

        renderingTask = React.cloneElement(originalListItem, {
          children: originalChildren
        });
      } else {
        renderingTask = React.cloneElement(originalListItem, {
          ...originalListItem.props,
          secondaryAction: chatButton
        });
      }
    }

    return renderingTask;
  };

  return (
    <Card
      variant="outlined"
      sx={{
        background:
          theme.palette.mode === 'dark'
            ? `${theme.colors.alpha.white[70]}`
            : `${alpha(theme.colors.alpha.trueWhite[100], 0.7)}`,
        height: '100%'
      }}
    >
      <CardHeader
        sx={{
          p: 2
        }}
        disableTypography
        title={
          <Box>
            <Typography fontWeight="bold" variant="caption">
              {t('Task Queue')}
            </Typography>
            <Typography variant="body2">
              {t('View the tasks below related to the current project')}
            </Typography>
          </Box>
        }
      />
      <CardContentWrapper
        sx={{
          mx: 3,
          mb: 3,
          minHeight: 400,
          height: '85%'
        }}
      >
        {project.tasks.length !== 0 ? (
          <Scrollbar>
            <ListWrapper disablePadding>
              <Divider />
              {project.tasks.map((tsk, i) => (
                <React.Fragment key={`${tsk.internalid}-${i}`}>
                  {renderProjectTask(tsk)}
                  <Divider />
                </React.Fragment>
              ))}
              <Divider />
            </ListWrapper>
          </Scrollbar>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center'
            }}
          >
            <Typography variant="h1" sx={{ textAlign: 'center', pt: 10 }}>
              No Tasks In Queue
            </Typography>
          </Box>
        )}
      </CardContentWrapper>
    </Card>
  );
}

export default TaskStatus;
