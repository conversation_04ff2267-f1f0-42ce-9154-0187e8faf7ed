import React from 'react';
import { Container, Box, Typography, Paper } from '@mui/material';
import ProjectFormReadOnlyView from './ProjectFormReadOnlyView';

// Example usage of ProjectFormReadOnlyView component
const ProjectFormReadOnlyExample = () => {
  // Example 1: Basic usage with projectId and formId
  const projectId = '12345'; // Replace with actual project ID
  const formId = 'form_001'; // Replace with actual form ID

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Typography variant="h4" gutterBottom>
        Project Form Read-Only View Examples
      </Typography>

      {/* Example 1: Basic Usage */}
      <Box mb={4}>
        <Typography variant="h6" gutterBottom>
          Example 1: Basic Usage
        </Typography>
        <ProjectFormReadOnlyView projectId={projectId} formId={formId} />
      </Box>

      {/* Example 2: Inside a Paper component with custom styling */}
      <Box mb={4}>
        <Typography variant="h6" gutterBottom>
          Example 2: With Custom Container
        </Typography>
        <Paper elevation={3} sx={{ p: 2, borderRadius: 2 }}>
          <ProjectFormReadOnlyView projectId={projectId} formId={formId} />
        </Paper>
      </Box>

      {/* Example 3: Usage notes */}
      <Box mt={4}>
        <Typography variant="h6" gutterBottom>
          Usage Notes:
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • The component automatically fetches form data using the provided
          projectId and formId
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • It supports both 'tab' and 'stack' layouts based on form
          configuration
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • Fields are grouped by their 'group' property and displayed in
          sections
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • Column layout is dynamic based on formGroups configuration (1, 2, 3,
          etc. columns)
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • Fields are displayed with appropriate icons and formatting based on
          their type
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • Beautiful animations and microinteractions using Framer Motion
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • The component handles loading states and errors gracefully
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          • All fields are displayed in read-only mode with beautiful Material
          Design styling
        </Typography>
      </Box>

      {/* Example of how to get formId dynamically */}
      <Box mt={4} p={2} bgcolor="grey.100" borderRadius={1}>
        <Typography variant="subtitle2" gutterBottom>
          Code Example: Getting formId dynamically
        </Typography>
        <pre style={{ overflow: 'auto' }}>
          {`// In your component
const [formId, setFormId] = useState(null);

useEffect(() => {
  const fetchFormId = async () => {
    const response = await axios.get(
      \`\${getProjectFormDataUrl}&projectId=\${projectId}\`
    );
    
    if (response.data?.data?.forms) {
      const selectedForm = response.data.data.forms.find(
        form => form.selected
      );
      if (selectedForm) {
        setFormId(selectedForm.id);
      }
    }
  };
  
  fetchFormId();
}, [projectId]);

// Then use it
{formId && (
  <ProjectFormReadOnlyView 
    projectId={projectId} 
    formId={formId}
  />
)}`}
        </pre>
      </Box>
    </Container>
  );
};

export default ProjectFormReadOnlyExample;
