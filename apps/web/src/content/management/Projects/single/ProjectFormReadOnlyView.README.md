# ProjectFormReadOnlyView Component

A beautiful, modern React component for displaying project form data in read-only mode with Material Design principles.

## Features

### 🎨 Beautiful Design
- Modern Material-UI design with gradient backgrounds
- Smooth animations and transitions using Framer Motion
- Responsive layout that works on all screen sizes
- Custom styled components with hover effects

### 📋 Layout Support
- **Tab Layout**: Organizes fields into tabbed sections for better organization
- **Stack Layout**: Displays all fields in a vertical stack
- Automatic section grouping based on form configuration
- Field width customization (full width or half width)

### 🔧 Field Type Support
The component intelligently renders different field types:

- **Text Fields**: Simple text display with proper formatting
- **Numbers**: Numeric values with appropriate styling
- **Dates**: Formatted date chips with calendar icons
- **Date Ranges**: Start and end date display with visual separation
- **Boolean/Checkbox**: Yes/No chips with success coloring
- **Select/Radio**: Display selected values as chips
- **Rich Text**: Rendered HTML content with proper sanitization
- **URLs**: Clickable link chips that open in new tabs
- **Files**: File attachment indicators with icons

### 🎯 Smart Features
- Automatic form data fetching using SWR
- Loading states with circular progress indicators
- Error handling with user-friendly messages
- Field icons based on field type
- N/A display for empty values
- Section badges showing field counts
- **Field Group Descriptions**: Displays administrator-configured descriptions for each field group
- Responsive description styling with visual hierarchy

## Usage

### Basic Usage

```jsx
import ProjectFormReadOnlyView from "./ProjectFormReadOnlyView";

// In your component
<ProjectFormReadOnlyView 
  projectId="12345" 
  formId="form_001"
/>
```

### Integration with ProfileCover

The component is already integrated into the ProfileCover component and will automatically:
1. Check if a formId is available on the project object
2. If not, fetch the form data and find the selected form
3. Display the form in read-only mode below the Project Note section

### Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| projectId | string | null | The ID of the project to display |
| formId | string | null | The ID of the form configuration |

## Architecture

### Data Flow
1. Component receives `projectId` and `formId` as props
2. Uses SWR to fetch form data from the API
3. Processes the response and extracts form fields
4. Groups fields by section if layout is 'tab'
5. Renders fields based on their type and layout configuration

### Styling
- Uses Material-UI's `styled` components for custom styling
- Implements theme-aware colors that work in light and dark modes
- Gradient backgrounds and subtle borders for visual hierarchy
- Hover effects for interactive feedback

### Performance
- Memoized components to prevent unnecessary re-renders
- Efficient field rendering with minimal DOM updates
- SWR caching for optimal data fetching
- Lazy loading of heavy dependencies like DOMPurify

## Field Configuration

The component expects form data in this structure:

```javascript
{
  customForm: "124",
  formLayout: "tab" | "stack",
  formId: "1",
  formSelected: {
    id: "1",
    name: "Default Form"
  },
  formGroups: [
    {
      title: "Event Information",
      columns: "1",  // Number of columns for grid layout
      description: "Information about the event details and requirements"  // Optional description
    },
    {
      title: "General Contractor Information",
      columns: "2",  // Fields will display in 2 columns
      description: "Details about the general contractor and contact information"  // Optional description
    }
  ],
  formFields: [
    {
      name: "fieldName",
      label: "Field Label",
      type: "text" | "number" | "date" | etc.,
      value: "Field value",
      group: "Event Information", // Group name or null
      fieldHelp: "Helper text",
      validations: ["required", "date"],
      displayValue: "Display text" // Optional, for select fields
    }
  ]
}
```

### Layout Behavior

- **Tab Layout**: Each group becomes a tab with fields displayed inside
- **Stack Layout**: Groups are displayed vertically with section headers
- **Columns**: The `columns` property in `formGroups` determines field layout:
  - `"1"`: Fields take full width
  - `"2"`: Fields display in 2 columns (50% each)
  - `"3"`: Fields display in 3 columns (33.33% each)
  - etc.

## Field Group Descriptions

The component now supports displaying descriptions for field groups, providing users with helpful context about each section.

### Features
- **Automatic Display**: Descriptions are automatically shown when configured in `formGroups`
- **Visual Design**: Styled with subtle background, left border, and appropriate typography
- **Layout Support**: Works in both tab and stack layouts
- **Responsive**: Adapts to different screen sizes
- **Conditional**: Only displays when description is provided (empty descriptions are hidden)

### Configuration
Field group descriptions are configured in the `formGroups` array:

```javascript
formGroups: [
  {
    title: "Event Information",
    columns: "2",
    description: "Provide details about the event including dates, location, and special requirements."
  }
]
```

### Styling
Descriptions use the `GroupDescription` styled component with:
- Light blue background with transparency
- Left border accent in theme info color
- Proper spacing and typography
- Automatic hiding when empty

## Customization

### Adding New Field Types

To add support for a new field type:

1. Add the icon mapping in `getFieldIcon()`
2. Add the rendering logic in `renderFieldValue()`
3. Update the field type in the switch statement

### Styling Customization

The component uses styled components that can be customized:

```jsx
const StyledCard = styled(Card)(({ theme }) => ({
  // Your custom styles here
}));

const GroupDescription = styled(Typography)(({ theme }) => ({
  // Customize description styling here
}));
```

## Dependencies

- Material-UI v5
- Framer Motion for animations
- date-fns for date formatting
- DOMPurify for HTML sanitization
- html-react-parser for rendering HTML
- SWR for data fetching
- axios for API calls

## Future Enhancements

- Export to PDF functionality
- Print-friendly view
- Field-level permissions/visibility
- Custom field renderers
- Collapsible sections
- Search/filter within form fields 