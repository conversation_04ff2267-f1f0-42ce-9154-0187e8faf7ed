import React from 'react';
import {
  alpha,
  Avatar,
  Box,
  Button,
  Card,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  styled,
  Typography,
  useTheme
} from '@mui/material';

import { useTranslation } from 'react-i18next';
import Scrollbar from 'src/components/Scrollbar';
import ArrowForwardTwoToneIcon from '@mui/icons-material/ArrowForwardTwoTone';
import { useNavigate } from 'react-router-dom';
import { ChatBubbleTwoTone } from '@mui/icons-material';
import { format } from 'date-fns';

const IconButtonWrapper = styled(IconButton)(
  ({ theme }) => `
    border-radius: 100px;
    width: ${theme.spacing(6)};
    height: ${theme.spacing(6)};

    .MuiSvgIcon-root {
        transform-origin: center;
        transform: scale(1);
        transition: ${theme.transitions.create(['transform'])};
    }

    &:hover {
        .MuiSvgIcon-root {
            transform: scale(1.4);
        }
    }
  `
);

const LabelWrapper = styled(Box)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(10)};
    font-weight: bold;
    text-transform: uppercase;
    border-radius: ${theme.general.borderRadiusSm};
    padding: ${theme.spacing(0.5, 1)};
  `
);

function DiscussionNavigation({ cases }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();

  return (
    <Card
      key="cases"
      sx={{
        height: '100%'
      }}
    >
      <Box
        p={2}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box>
          <Typography gutterBottom variant="caption" fontWeight="bold">
            {t('Discussions')}
          </Typography>
          <Typography variant="body2">
            {t('View your discussions related to this project')}
          </Typography>
        </Box>
        <Button
          onClick={() => navigate('/discussions')}
          size="small"
          variant="contained"
          color="primary"
        >
          {t('Open all')}
        </Button>
      </Box>
      <Divider />
      <Box
        sx={{
          minHeight: 400,
          height: '85%'
        }}
      >
        {cases.length !== 0 ? (
          <Scrollbar>
            <List disablePadding>
              {cases.map((discussion) => (
                <React.Fragment key={discussion.id}>
                  <ListItem
                    key={`case-${discussion.id}`}
                    sx={{
                      p: 2.5
                    }}
                  >
                    <ListItemAvatar
                      sx={{
                        mr: 2,
                        display: 'flex',
                        alignItems: 'center',
                        minWidth: 0
                      }}
                    >
                      <Avatar
                        sx={{
                          background: 'transparent',
                          color: `${theme.colors.primary.main}`,
                          border: `${theme.colors.primary.main} solid 2px`,
                          width: 58,
                          height: 58
                        }}
                      >
                        <ChatBubbleTwoTone />
                      </Avatar>
                    </ListItemAvatar>
                    <ListItemText
                      primary={
                        <Typography
                          gutterBottom
                          variant="h4"
                          sx={{
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          {t(discussion.title)}
                          &nbsp;
                          <LabelWrapper
                            ml={1}
                            component="span"
                            sx={{
                              background: `${alpha(
                                theme.colors.info.light,
                                0.2
                              )}`,
                              color: `${theme.colors.info.main}`
                            }}
                          >
                            {t(discussion.status)}
                          </LabelWrapper>
                        </Typography>
                      }
                      secondary={
                        <>
                          <Typography
                            sx={{
                              fontSize: `${theme.typography.pxToRem(13)}`
                            }}
                            gutterBottom
                            noWrap
                            variant="subtitle2"
                          >
                            {t('Discussion')}&nbsp;
                            {discussion.casenumber}
                          </Typography>
                          <Typography
                            sx={{
                              fontSize: `${theme.typography.pxToRem(12)}`
                            }}
                            noWrap
                            variant="subtitle2"
                          >
                            {t('Created')}&nbsp;
                            {format(
                              new Date(discussion.createddate),
                              'MM/dd/yyyy'
                            )}
                          </Typography>
                        </>
                      }
                    />
                    <IconButtonWrapper
                      onClick={() => navigate(`/discussions/${discussion.id}`)}
                      color="primary"
                    >
                      <ArrowForwardTwoToneIcon fontSize="small" />
                    </IconButtonWrapper>
                  </ListItem>
                  <Divider />
                </React.Fragment>
              ))}
            </List>
          </Scrollbar>
        ) : (
          <Box
            pt={5}
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          >
            <Typography variant="h2">
              There are no discussions within this project
            </Typography>
          </Box>
        )}
      </Box>
      <Divider />
    </Card>
  );
}

export default DiscussionNavigation;
