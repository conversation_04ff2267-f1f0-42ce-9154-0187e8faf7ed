import { useEffect, useState } from 'react';

import {
  Avatar,
  Box,
  Button,
  Card,
  Divider,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  styled,
  ToggleButton,
  ToggleButtonGroup,
  Typography,
  useTheme
} from '@mui/material';

import Timeline from '@mui/lab/Timeline';
import TimelineItem from '@mui/lab/TimelineItem';
import TimelineSeparator from '@mui/lab/TimelineSeparator';
import TimelineConnector from '@mui/lab/TimelineConnector';
import TimelineContent from '@mui/lab/TimelineContent';
import TimelineDot from '@mui/lab/TimelineDot';
import TimelineOppositeContent from '@mui/lab/TimelineOppositeContent';

import { format } from 'date-fns';
import { useTranslation } from 'react-i18next';
import Scrollbar from 'src/components/Scrollbar';
import SellTwoToneIcon from '@mui/icons-material/SellTwoTone';
import LocalShippingTwoToneIcon from '@mui/icons-material/LocalShippingTwoTone';
import KeyboardReturnTwoToneIcon from '@mui/icons-material/KeyboardReturnTwoTone';
import WarehouseTwoToneIcon from '@mui/icons-material/WarehouseTwoTone';
import DirectionsRunTwoToneIcon from '@mui/icons-material/DirectionsRunTwoTone';
import CampaignTwoToneIcon from '@mui/icons-material/CampaignTwoTone';
import ExitToAppTwoToneIcon from '@mui/icons-material/ExitToAppTwoTone';
import HailTwoToneIcon from '@mui/icons-material/HailTwoTone';

// eslint-disable-next-line no-unused-vars
const LabelWrapper = styled(Box)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(10)};
    font-weight: bold;
    text-transform: uppercase;
    border-radius: ${theme.general.borderRadiusSm};
    padding: ${theme.spacing(0.5, 1)};
  `
);

function DateActivity({ project }) {
  const { t } = useTranslation();
  const theme = useTheme();

  const [tabs, setTab] = useState('activity');

  const handleViewOrientation = (_event, newValue) => {
    setTab(newValue);
  };

  const datelist = [
    {
      name: 'materialarrival',
      label: 'Material Arrival',
      date: new Date(project.materialarrival),
      description: 'Material expected arrival',
      icon: <WarehouseTwoToneIcon />
    },
    {
      name: 'materialpickup',
      label: 'Material Pickup',
      date: new Date(project.materialpickup),
      description: 'Material expected pickup',
      icon: <HailTwoToneIcon />
    },
    {
      name: 'returndate',
      label: 'Return Date',
      date: new Date(project.returndate),
      description: 'Return date for shipment',
      icon: <KeyboardReturnTwoToneIcon />
    },
    {
      name: 'shipdate',
      label: 'Shipment',
      date: new Date(project.shipdate),
      description: 'Expected shipment date',
      icon: <LocalShippingTwoToneIcon />
    },
    {
      name: 'moveindate',
      label: 'Move In',
      date: new Date(project.moveindate),
      description: '',
      icon: <DirectionsRunTwoToneIcon />
    },
    {
      name: 'moveoutdate',
      label: 'Move Out',
      date: new Date(project.moveoutdate),
      description: '',
      icon: (
        <DirectionsRunTwoToneIcon style={{ transform: 'rotateY(180deg)' }} />
      )
    },
    {
      name: 'eventstartdate',
      label: 'Event Start',
      date: new Date(project.eventstartdate),
      description: "Start of the event for current project'",
      icon: <CampaignTwoToneIcon />
    },
    {
      name: 'eventenddate',
      label: 'Event End',
      date: new Date(project.eventenddate),
      description: 'End date of event for project',
      icon: <ExitToAppTwoToneIcon />
    },
    {
      name: 'discountexpirationdate',
      label: 'Show Services Discount Expiry',
      date: new Date(project.discountexpirationdate),
      description: 'The expiration date for contractor ordering',
      icon: <SellTwoToneIcon />
    }
  ];

  const sortedDates = datelist.sort((a, b) => b.date - a.date);
  const validDates = sortedDates.filter(
    (d) => d.date.toString() !== 'Invalid Date'
  );

  useEffect(() => {
    console.log('Sorted Dates', sortedDates);
    console.log('Valid Dates', validDates);
  }, []);

  const renderTimelineItems = (timeItem) => {
    let renderItem = null;
    switch (timeItem.name) {
      case 'discountexpirationdate':
        renderItem = (
          <TimelineItem
            key={timeItem.name}
            sx={{
              p: 0
            }}
          >
            <TimelineOppositeContent
              sx={{
                width: '85px',
                flex: 'none'
              }}
              color="text.secondary"
            >
              {format(timeItem.date, 'MMMM dd yyyy')}
            </TimelineOppositeContent>
            <TimelineSeparator
              sx={{
                position: 'relative'
              }}
            >
              <TimelineDot
                sx={{
                  marginTop: 0,
                  top: theme.spacing(1.2)
                }}
                variant="outlined"
                color="primary"
              />
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent
              sx={{
                pb: 4
              }}
            >
              <Typography variant="h5" gutterBottom>
                {timeItem.label}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {timeItem.description}
              </Typography>
              {/*   <Box display="flex" mt={1} alignItems="flex-start">
              <Button size="small" variant="contained" color="warning">
                {t('Order Items')}
              </Button>
            </Box> */}
            </TimelineContent>
          </TimelineItem>
        );
        break;
      default:
        renderItem = (
          <TimelineItem
            key={timeItem.name}
            sx={{
              p: 0
            }}
          >
            <TimelineOppositeContent
              sx={{
                width: '85px',
                flex: 'none'
              }}
              color="text.secondary"
            >
              {format(timeItem.date, 'MMMM dd yyyy')}
            </TimelineOppositeContent>
            <TimelineSeparator
              sx={{
                position: 'relative'
              }}
            >
              <TimelineDot
                sx={{
                  marginTop: 0,
                  top: theme.spacing(1.2)
                }}
                variant="outlined"
                color="primary"
              />
              <TimelineConnector />
            </TimelineSeparator>
            <TimelineContent
              sx={{
                pb: 4
              }}
            >
              <Typography variant="h5" gutterBottom>
                {timeItem.label}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {timeItem.description}
              </Typography>
              <Box display="flex" mt={1} alignItems="flex-start">
                {/* <AvatarGroup max={4}>
                  <Tooltip arrow title="Remy Sharp">
                    <Avatar
                      component={RouterLink}
                      sx={{
                        width: 32,
                        height: 32
                      }}
                      to="#"
                      alt="Remy Sharp"
                      src="/static/images/avatars/1.jpg"
                    />
                  </Tooltip>
                  <Tooltip arrow title="Travis Howard">
                    <Avatar
                      component={RouterLink}
                      sx={{
                        width: 32,
                        height: 32
                      }}
                      to="#"
                      alt="Travis Howard"
                      src="/static/images/avatars/2.jpg"
                    />
                  </Tooltip>
                  <Tooltip arrow title="Cindy Baker">
                    <Avatar
                      component={RouterLink}
                      sx={{
                        width: 32,
                        height: 32
                      }}
                      to="#"
                      alt="Cindy Baker"
                      src="/static/images/avatars/4.jpg"
                    />
                  </Tooltip>
                  <Tooltip arrow title="Agnes Walker">
                    <Avatar
                      component={RouterLink}
                      sx={{
                        width: 32,
                        height: 32
                      }}
                      to="#"
                      alt="Agnes Walker"
                      src="/static/images/avatars/5.jpg"
                    />
                  </Tooltip>
                </AvatarGroup> */}
              </Box>
            </TimelineContent>
          </TimelineItem>
        );
        break;
    }
    return renderItem;
  };

  return (
    <Card>
      <Box
        p={2.5}
        display="flex"
        alignItems="center"
        justifyContent="space-between"
      >
        <Box>
          <Typography gutterBottom variant="h4">
            {t('Date Activity')}
          </Typography>
        </Box>
        <ToggleButtonGroup
          size="small"
          value={tabs}
          exclusive
          onChange={handleViewOrientation}
        >
          <ToggleButton
            sx={{
              px: 2,
              py: 0.5,
              lineHeight: 1.5,
              fontSize: `${theme.typography.pxToRem(12)}`
            }}
            disableRipple
            value="activity"
          >
            {t('Timeline')}
          </ToggleButton>
          <ToggleButton
            sx={{
              px: 2,
              py: 0.5,
              lineHeight: 1.5,
              fontSize: `${theme.typography.pxToRem(12)}`
            }}
            disableRipple
            value="list"
          >
            {t('List')}
          </ToggleButton>
        </ToggleButtonGroup>
      </Box>
      <Divider />

      {tabs === 'activity' && (
        <>
          <Box
            sx={{
              height: 560
            }}
          >
            <Scrollbar>
              <Timeline>
                {validDates.map((date) => {
                  return renderTimelineItems(date);
                })}
              </Timeline>
              <Box
                px={2}
                py={3}
                sx={{
                  textAlign: 'center'
                }}
              >
                <Typography variant="subtitle2">
                  {t("You've reached the end of the project list")}!
                </Typography>
              </Box>
            </Scrollbar>
          </Box>
          <Divider />
        </>
      )}
      {tabs === 'list' && (
        <>
          <Box
            sx={{
              height: 560
            }}
          >
            <Scrollbar>
              <List disablePadding>
                {validDates.map((item, i) => (
                  <>
                    <ListItem
                      key={i}
                      sx={{
                        p: 2
                      }}
                    >
                      <ListItemAvatar
                        sx={{
                          mr: 2,
                          display: 'flex',
                          alignItems: 'center',
                          minWidth: 0
                        }}
                      >
                        <Avatar
                          variant="rounded"
                          sx={{
                            background: `${theme.colors.alpha.black[10]}`,
                            color: `${theme.colors.primary.main}`,
                            width: 64,
                            height: 64
                          }}
                        >
                          {item.icon}
                        </Avatar>
                      </ListItemAvatar>
                      <ListItemText
                        primary={
                          <Typography gutterBottom variant="h4">
                            {t(item.label)}
                          </Typography>
                        }
                        secondary={
                          <Typography variant="subtitle2">
                            {format(new Date(item.date), 'MMMM dd yyyy')}
                          </Typography>
                        }
                      />
                    </ListItem>
                    <Divider />
                  </>
                ))}
              </List>
            </Scrollbar>
          </Box>
          <Divider />
        </>
      )}
      {!tabs && (
        <Box
          p={3}
          display="flex"
          alignItems="center"
          justifyContent="center"
          sx={{
            height: 422,
            textAlign: 'center'
          }}
        >
          <Box>
            <Typography
              align="center"
              variant="h2"
              fontWeight="normal"
              color="text.secondary"
              sx={{
                mt: 3
              }}
              gutterBottom
            >
              {t('Select one of the tabs to continue')}
            </Typography>
            <Button
              sx={{
                mt: 4
              }}
            >
              Maybe, a button?
            </Button>
          </Box>
        </Box>
      )}
    </Card>
  );
}

export default DateActivity;
