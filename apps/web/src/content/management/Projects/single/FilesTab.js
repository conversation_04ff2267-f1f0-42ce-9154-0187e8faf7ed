import { Fragment, useState } from 'react';

import {
  Alert,
  alpha,
  Avatar,
  Box,
  Button,
  Card,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Divider,
  Grid,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  styled,
  Typography,
  useMediaQuery,
  useTheme,
  Zoom
} from '@mui/material';

import { useTranslation } from 'react-i18next';
import GifBoxTwoToneIcon from '@mui/icons-material/GifBoxTwoTone';
import InsertDriveFileTwoToneIcon from '@mui/icons-material/InsertDriveFileTwoTone';
import PhotoTwoToneIcon from '@mui/icons-material/PhotoTwoTone';
import ArticleTwoToneIcon from '@mui/icons-material/ArticleTwoTone';
import PictureAsPdfTwoToneIcon from '@mui/icons-material/PictureAsPdfTwoTone';

import Scrollbar from 'src/components/Scrollbar';
import { FileUploadTwoTone, ViewInArTwoTone } from '@mui/icons-material';
import { Formik } from 'formik';
import * as Yup from 'yup';
import CheckTwoToneIcon from '@mui/icons-material/CheckTwoTone';
import CloseTwoToneIcon from '@mui/icons-material/CloseTwoTone';
import CloudUploadTwoToneIcon from '@mui/icons-material/CloudUploadTwoTone';
import { useDropzone } from 'react-dropzone';
import axios from 'axios';
import { useSnackbar } from 'notistack';
import ThreeDimensionalModelViewer from '../../../../components/modals/ThreeDimensionalModelViewer';
import FileOptionsMenu from '../../../../components/Containers/FileOptionsMenu';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';

const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.general.borderRadius};
    padding: ${theme.spacing(3)};
    background: ${theme.colors.alpha.black[5]};
    border: 1px dashed ${theme.colors.alpha.black[30]};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(['border', 'background'])};

    &:hover {
      background: ${theme.colors.alpha.white[100]};
      border-color: ${theme.colors.primary.main};
    }
`
);

// eslint-disable-next-line no-unused-vars
const EditorWrapper = styled(Box)(
  ({ theme }) => `

    .ql-editor {
      min-height: 100px;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: ${theme.general.borderRadius};
      border-top-right-radius: ${theme.general.borderRadius};
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border-color: ${theme.colors.alpha.black[30]};
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: ${theme.general.borderRadius};
      border-bottom-right-radius: ${theme.general.borderRadius};
    }

    &:hover {
      .ql-toolbar.ql-snow,
      .ql-container.ql-snow {
        border-color: ${theme.colors.alpha.black[50]};
      }
    }
`
);

const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.primary.lighter};
    color: ${theme.colors.primary.main};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.success.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const AvatarDanger = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.error.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const DividerWrapper = styled(Divider)(
  ({ theme }) => `
    background: ${theme.colors.alpha.trueWhite[10]};
  `
);

const ListItemWrapper = styled(ListItem)(
  ({ theme }) => `
    padding: ${theme.spacing(1.5, 2)};
    background: ${theme.colors.alpha.trueWhite[10]};
    margin-bottom: ${theme.spacing(2)};
    border-radius: ${theme.general.borderRadiusSm};
    color: ${theme.colors.alpha.trueWhite[100]};
    box-shadow: 
        0 0.56875rem 3.3rem rgba(0,0,0, .05),
        0 0.9975rem 2.4rem rgba(0,0,0, .07),
        0 0.35rem 1rem rgba(0,0,0, .1),
        0 0.225rem 0.8rem rgba(0,0,0, .15);

    &:last-of-type {
        margin-bottom: 0;
    }
  `
);

function FilesTab({
  files,
  project,
  mutateProj,
  isUpdating,
  setUpdating,
  tabHeight
}) {
  const { t } = useTranslation();
  const [openFileUploadModal, setOpenFileUploadModal] = useState(false);
  const { enqueueSnackbar } = useSnackbar();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const [itemViewing, setItemViewing] = useState({});
  const [modelViewOpen, setModelViewOpen] = useState(false);
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const renderFileThumbnailIcon = (item) => {
    let imagesCheck = ['jpeg', 'jpg', 'pjpeg', 'x-png', 'png'];
    let docsCheck = ['doc', 'docx', 'txt'];
    let threedCheck = ['glb', 'gltf'];
    if (item.extension === 'gif') {
      return <GifBoxTwoToneIcon fontSize="large" />;
    }
    if (item.extension === 'pdf') {
      return <PictureAsPdfTwoToneIcon fontSize="large" />;
    }
    if (imagesCheck.includes(item.extension)) {
      return <PhotoTwoToneIcon fontSize="large" />;
    }
    if (docsCheck.includes(item.extension)) {
      return <ArticleTwoToneIcon fontSize="large" />;
    }
    if (threedCheck.includes(item.extension)) {
      return <ViewInArTwoTone fontSize="large" />;
    }
    return <InsertDriveFileTwoToneIcon fontSize="large" />;
  };

  const toggleThreeDModal = (visible, item) => {
    setItemViewing(item);
    setModelViewOpen(!visible);
  };

  function formatBytes(bytes, decimals = 2) {
    let kilobytes = bytes * 1000;
    if (bytes === 0) return '0 KB';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(kilobytes) / Math.log(k));

    // eslint-disable-next-line no-restricted-properties
    return `${parseFloat((kilobytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  function formatUploadBytes(bytes, decimals = 2) {
    let kilobytes = bytes;
    if (bytes === 0) return '0 KB';

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(kilobytes) / Math.log(k));

    // eslint-disable-next-line no-restricted-properties
    return `${parseFloat((kilobytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps
  } = useDropzone({
    accept:
      'image/jpeg, image/png, text/plain, application/pdf, application/docx, application/json, .gltf, .glb'
  });

  const uploadedFiles = acceptedFiles.map((file, index) => (
    <ListItem disableGutters component="div" key={index}>
      <ListItemText primary={file.name} />
      <b>{formatUploadBytes(file.size)}</b>
      <Divider />
    </ListItem>
  ));

  const handleUpdateProjectOpen = () => {
    setOpenFileUploadModal(true);
  };

  const handleUpdateProjectClose = (values) => {
    console.log('Values:', values);
    setOpenFileUploadModal(false);
  };

  const handleUpdateProjectFiles = async (
    values,
    setStatus,
    setSubmitting,
    resetForm
  ) => {
    console.log('Values:', values);
    // eslint-disable-next-line camelcase
    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    console.log('Files Added?', acceptedFiles);

    const formData = new FormData();
    let allFileNames = acceptedFiles.map((fil, i) => `file${i}`);
    formData.append('fileObjNames', JSON.stringify(allFileNames));
    if (acceptedFiles && acceptedFiles.length !== 0) {
      // eslint-disable-next-line array-callback-return
      acceptedFiles.map((fl, i) => {
        formData.append(`file${i}`, fl);
      });
    }

    if (Object.keys(values).length !== 0) {
      let formInputValues = Object.keys(values);
      formInputValues.forEach((key) => {
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else {
          formData.append(key, values[key]);
        }
      });
    }

    let requestOptions = {
      method: 'PUT',
      redirect: 'follow',
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    return new Promise(() => {
      setUpdating(true);
      // eslint-disable-next-line camelcase
      axios
        .post(
          // eslint-disable-next-line camelcase
          `${get_record_function_url}&type=UPDATEPROJ`,
          formData,
          requestOptions
        )
        .then((res) => res.data)
        .then((resJSON) => {
          if (resJSON?.message && resJSON?.message.includes('updated')) {
            resetForm();
            setStatus({ success: true });
            setSubmitting(false);
            // eslint-disable-next-line camelcase
            const getProjectContentsUrl =
              process.env.NODE_ENV === 'development'
                ? getDeploymentUrl.get_project_details_external
                : getDeploymentUrl.get_project_details_internal;
            mutateProj(`${getProjectContentsUrl}&projId=${project.id}`);
            console.log('Update success!');
            enqueueSnackbar(
              t('Project files have been uploaded successfully'),
              {
                variant: 'success',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'right'
                },
                TransitionComponent: Zoom
              }
            );
          } else {
            setSubmitting(false);
            enqueueSnackbar(t('Failed to attach project files'), {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          }
          console.log(resJSON);
          return resJSON;
        })
        .catch((err) => {
          setStatus({ success: false });
          setSubmitting(false);
          console.error(err);
          enqueueSnackbar(t('Project update failed'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
          Promise.reject(err);
        })
        .finally(() => {
          setOpenFileUploadModal(false);
          setTimeout(() => setUpdating(false), 400);
        });
    });
  };

  return (
    <Card
      className="!shadow-lg"
      sx={{
        color: `${theme.colors.alpha.trueWhite[100]}`,
        background: `linear-gradient(90deg, rgba(64,69,74,1) 0%, rgba(114,175,186,1) 61%, rgba(154,207,231,1) 95%, rgba(212,226,228,1) 100%)`,
        height: '100%'
      }}
    >
      <Box
        display="flex"
        alignItems="center"
        justifyContent="space-between"
        sx={{
          background: `${theme.colors.alpha.white[5]}`
        }}
        p={2}
      >
        <Box>
          <Typography
            variant="caption"
            fontWeight="bold"
            sx={{
              color: `${theme.colors.alpha.trueWhite[70]}`,
              fontSize: `${theme.typography.pxToRem(12)}`
            }}
          >
            {t('Storage')}
          </Typography>
          <Typography variant="h4">{t('Recent Files')}</Typography>
        </Box>
        <Button
          onClick={() => handleUpdateProjectOpen()}
          size="small"
          sx={{
            alignSelf: 'center',
            fontWeight: 'normal',
            backgroundColor: `${theme.colors.alpha.black[10]}`,
            color: `${theme.colors.alpha.black[70]}`,
            transition: `${theme.transitions.create(['all'])}`,

            '&:hover': {
              backgroundColor: `${alpha(theme.colors.alpha.black[100], 0.2)}`,
              color: `${theme.colors.alpha.trueWhite[100]}`
            }
          }}
          variant="outlined"
        >
          {t('Upload Files')}
          &nbsp;
          <FileUploadTwoTone />
        </Button>
      </Box>
      <DividerWrapper />
      <Box
        sx={{
          height: `calc(${tabHeight}px + 60vh);`
        }}
      >
        {files.length !== 0 ? (
          <Scrollbar>
            <List
              sx={{
                p: 2
              }}
            >
              {files.map((item, i) => (
                <Fragment key={item.internalid}>
                  <ListItemWrapper
                    secondaryAction={
                      <FileOptionsMenu
                        key={i}
                        item={item}
                        setModelViewOpen={() =>
                          toggleThreeDModal(modelViewOpen, item)
                        }
                      />
                    }
                  >
                    <ListItemAvatar
                      sx={{
                        color: `${theme.colors.alpha.trueWhite[50]}`,
                        minWidth: 0,
                        mr: 2
                      }}
                    >
                      {renderFileThumbnailIcon(item)}
                    </ListItemAvatar>
                    <ListItemText
                      sx={{
                        flexGrow: 0,
                        maxWidth: '50%',
                        flexBasis: '50%'
                      }}
                      disableTypography
                      primary={
                        <Typography
                          noWrap
                          gutterBottom
                          sx={{
                            color: `${theme.colors.alpha.trueWhite[100]}`
                          }}
                          variant="h4"
                        >
                          {item.name}
                        </Typography>
                      }
                      secondary={
                        <>
                          <Typography
                            sx={{
                              color: `${theme.colors.alpha.trueWhite[70]}`
                            }}
                            variant="body1"
                          >
                            {formatBytes(parseFloat(item.documentsize))}
                          </Typography>
                        </>
                      }
                    />
                  </ListItemWrapper>
                </Fragment>
              ))}
            </List>
          </Scrollbar>
        ) : (
          <Box
            pt={5}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Typography variant="h2">No files found</Typography>
          </Box>
        )}
      </Box>
      <ThreeDimensionalModelViewer
        item={itemViewing}
        onClose={() => setModelViewOpen(!modelViewOpen)}
        open={modelViewOpen}
      />
      <Dialog
        fullWidth
        maxWidth="md"
        open={openFileUploadModal}
        onClose={handleUpdateProjectClose}
      >
        <DialogTitle
          sx={{
            p: 3
          }}
        >
          <Typography variant="h4" gutterBottom>
            {t('Upload Project Files')}
          </Typography>
          <Typography variant="subtitle2">
            {t(
              'Attach new files to the project by clicking the box or simply drag and drop files to attach to the project'
            )}
            .
          </Typography>
        </DialogTitle>
        <Formik
          initialValues={{
            projname: project.name,
            projectId: project.id,
            submit: null
          }}
          validationSchema={Yup.object().shape({
            projname: Yup.string()
              .max(255)
              .required(t('The title field is required'))
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              setSubmitting(true);
              await handleUpdateProjectFiles(
                _values,
                setStatus,
                setSubmitting,
                resetForm
              );
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({ errors, handleSubmit, isSubmitting }) => (
            <form onSubmit={handleSubmit}>
              <DialogContent
                dividers
                sx={{
                  p: 3
                }}
              >
                <Grid container spacing={1}>
                  <Grid
                    sx={{
                      mb: `${theme.spacing(3)}`
                    }}
                    item
                    xs={12}
                  >
                    <BoxUploadWrapper {...getRootProps()}>
                      <input {...getInputProps()} />
                      {isDragAccept && (
                        <>
                          <AvatarSuccess variant="rounded">
                            <CheckTwoToneIcon />
                          </AvatarSuccess>
                          <Typography
                            sx={{
                              mt: 2
                            }}
                          >
                            {t('Drop the files to start uploading')}
                          </Typography>
                        </>
                      )}
                      {isDragReject && (
                        <>
                          <AvatarDanger variant="rounded">
                            <CloseTwoToneIcon />
                          </AvatarDanger>
                          <Typography
                            sx={{
                              mt: 2
                            }}
                          >
                            {t('You cannot upload these file types')}
                          </Typography>
                        </>
                      )}
                      {!isDragActive && (
                        <>
                          <AvatarWrapper variant="rounded">
                            <CloudUploadTwoToneIcon />
                          </AvatarWrapper>
                          <Typography
                            sx={{
                              mt: 2
                            }}
                          >
                            {t('Drag & drop files here to attach to project')}
                          </Typography>
                        </>
                      )}
                    </BoxUploadWrapper>
                    {uploadedFiles.length > 0 && (
                      <>
                        <Alert
                          sx={{
                            py: 0,
                            mt: 2
                          }}
                          severity="success"
                        >
                          {t('You have uploaded')} <b>{uploadedFiles.length}</b>{' '}
                          {t('files')}!
                        </Alert>
                        <Divider
                          sx={{
                            mt: 2
                          }}
                        />
                        <List disablePadding component="div">
                          {uploadedFiles}
                        </List>
                      </>
                    )}
                  </Grid>
                </Grid>
                <Box
                  sx={{
                    display: { xs: 'block', sm: 'flex' },
                    alignItems: 'center',
                    justifyContent: 'flex-end',
                    p: 3
                  }}
                >
                  <Box>
                    <Button
                      fullWidth={mobile}
                      sx={{
                        mr: 2
                      }}
                      color="secondary"
                      size="large"
                      variant="outlined"
                      onClick={handleUpdateProjectClose}
                    >
                      {t('Cancel')}
                    </Button>
                    <Button
                      fullWidth={mobile}
                      type="submit"
                      startIcon={
                        isSubmitting ? <CircularProgress size="1rem" /> : null
                      }
                      disabled={
                        Boolean(errors.submit) || isSubmitting || isUpdating
                      }
                      variant="contained"
                      size="large"
                    >
                      {t('Update Project')}
                    </Button>
                  </Box>
                </Box>
              </DialogContent>
            </form>
          )}
        </Formik>
      </Dialog>
    </Card>
  );
}

export default FilesTab;
