import React from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Card,
  Divider,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useSnackbar } from 'notistack';
import Cookies from 'js-cookie';
import { motion } from 'framer-motion';

import Label from 'src/components/Label';
import ChatBubbleTwoToneIcon from '@mui/icons-material/ChatBubbleTwoTone';
import useDeploymentUrls from 'src/hooks/useHoistedUrls';

// Function to get status label
const getStatusLabel = (taskStatus) => {
  const map = {
    'Not Started': {
      text: 'Not Started',
      color: 'error'
    },
    Completed: {
      text: 'Completed',
      color: 'success'
    },
    'In Progress': {
      text: 'In Progress',
      color: 'warning'
    }
  };

  const { text, color } = map[taskStatus] || {
    text: 'Unknown',
    color: 'default'
  };

  return <Label color={color}>{text}</Label>;
};

// Helper to extract Project ID from company string
const getProjectId = (companyString) => {
  if (!companyString) return '';
  const parts = companyString.split(':');
  // Trim whitespace and return the part after ':' if it exists
  return parts.length > 1 ? parts[1].trim() : companyString.trim();
};

const ProjectTaskTable = ({ tasks = [], className, ...rest }) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const { getDeploymentUrl } = useDeploymentUrls();

  // Date formatting function
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    } catch (error) {
      console.error('Error formatting date:', error);
      return 'Invalid Date';
    }
  };

  // Chat button onClick handler (adapted from Results.js)
  const handleOpenChat = (task) => {
    if (task.externalChat) {
      const isDevelopment =
        !process.env.NODE_ENV || process.env.NODE_ENV === 'development';
      // Use correct internal URL for dev, external for prod
      const baseUrl = isDevelopment
        ? 'http://localhost:5173/' // Assuming default dev chat URL
        : getDeploymentUrl?.get_cs_chat_url;

      if (baseUrl) {
        const chatUrl = `${baseUrl}?taskId=${task.internalid}&mode=view`;
        Cookies.set('ept-taskChatId', task.internalid, { expires: 7 });
        Cookies.set('ept-internalChat', 'F', { expires: 7 }); // Set internalChat flag
        navigate('/view-chat', {
          state: {
            chatUrl,
            taskName: task.name
          }
        });
      } else {
        console.error('Chat base URL is not configured.');
        enqueueSnackbar(
          t('Chat URL is not configured. Please contact support.'),
          {
            variant: 'error'
          }
        );
      }
    }
  };

  return (
    <Card className={className} {...rest}>
      <Typography sx={{ p: 2 }} variant="h4">
        {t('Project Tasks')}
      </Typography>
      <Divider />
      <TableContainer>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>{t('NAME')}</TableCell>
              <TableCell align="center">{t('STATUS')}</TableCell>
              <TableCell align="center">{t('START DATE')}</TableCell>
              <TableCell align="center">{t('END DATE')}</TableCell>
              <TableCell align="center">{t('CHAT')}</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {tasks.map((task) => {
              const projectId = getProjectId(task.company);
              return (
                <TableRow hover key={task.internalid || task.id}>
                  {/* Name Cell */}
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <Box>
                        <Typography
                          variant="body1"
                          fontWeight="bold"
                          color="text.primary"
                          gutterBottom
                          noWrap
                        >
                          {task.name}
                        </Typography>
                      </Box>
                    </Box>
                  </TableCell>
                  {/* Status Cell */}
                  <TableCell align="center">
                    {getStatusLabel(task.status)}
                  </TableCell>
                  {/* Start Date Cell */}
                  <TableCell align="center">
                    <Typography variant="body1" color="text.secondary" noWrap>
                      {formatDate(task.startdate)}
                    </Typography>
                  </TableCell>
                  {/* End Date Cell */}
                  <TableCell align="center">
                    <Typography variant="body1" color="text.secondary" noWrap>
                      {formatDate(task.enddate)}
                    </Typography>
                  </TableCell>
                  {/* Chat Cell - Added onClick, disabled state, and animation */}
                  <TableCell align="center">
                    <Tooltip
                      title={
                        !task.externalChat
                          ? t('Chat is not enabled externally for this task')
                          : t('Open Task Chat')
                      }
                      arrow
                    >
                      {/* Span needed for tooltip on disabled button */}
                      <span>
                        <IconButton
                          color="primary"
                          size="small"
                          disabled={!task.externalChat}
                          onClick={() => handleOpenChat(task)}
                          sx={{ position: 'relative' }} // Needed for positioning animation
                        >
                          <ChatBubbleTwoToneIcon fontSize="small" />
                          {task.externalChat && (
                            <>
                              {/* Animated ring pulse */}
                              <motion.span
                                initial={{ scale: 0.7, opacity: 0.5 }}
                                animate={{
                                  scale: [0.7, 1.6],
                                  opacity: [0.5, 0]
                                }}
                                transition={{
                                  duration: 1.3,
                                  repeat: Infinity,
                                  repeatType: 'loop',
                                  ease: 'easeInOut'
                                }}
                                style={{
                                  position: 'absolute',
                                  top: 0,
                                  right: 0,
                                  width: 18,
                                  height: 18,
                                  borderRadius: '50%',
                                  background: 'rgba(76, 175, 80, 0.25)', // Green pulse
                                  zIndex: 1,
                                  filter: 'blur(2px)'
                                }}
                              />
                              {/* Static dot */}
                              <span
                                style={{
                                  position: 'absolute',
                                  top: 4,
                                  right: 4,
                                  width: 8,
                                  height: 8,
                                  borderRadius: '50%',
                                  background: '#4caf50', // Green dot
                                  zIndex: 2
                                }}
                              />
                            </>
                          )}
                        </IconButton>
                      </span>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              );
            })}
            {tasks.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} align="center">
                  <Typography
                    variant="body1"
                    color="text.secondary"
                    sx={{ p: 2 }}
                  >
                    {t('No tasks found for this project.')}
                  </Typography>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    </Card>
  );
};

ProjectTaskTable.propTypes = {
  tasks: PropTypes.array.isRequired,
  className: PropTypes.string
};

export default ProjectTaskTable;
