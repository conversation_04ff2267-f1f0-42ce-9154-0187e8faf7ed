import React, { memo, useContext, useEffect, useRef, useState } from 'react';
/* eslint-disable jsx-a11y/label-has-for */
import PropTypes from 'prop-types';

import {
  alpha,
  Box,
  Button,
  Card,
  CardContent,
  CircularProgress,
  createFilterOptions,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Divider,
  Fade,
  Grid,
  IconButton,
  Menu,
  MenuItem,
  styled,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  Zoom
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import {
  CheckTwoTone,
  Edit,
  Inventory2TwoTone,
  Print,
  WarehouseTwoTone
} from '@mui/icons-material';
import ArrowBackTwoToneIcon from '@mui/icons-material/ArrowBackTwoTone';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import DOMPurify from 'dompurify';
import { useSnackbar } from 'notistack';
import parse from 'html-react-parser';
import { useTheme } from '@mui/material/styles';

import { SettingsContext } from '../../../../contexts/SettingsContext';
import useAuth from '../../../../hooks/useAuth';
import KeyboardArrowRightTwoToneIcon from '@mui/icons-material/KeyboardArrowRightTwoTone';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import ReactQuill from 'react-quill';
import { addDays, isPast, subDays } from 'date-fns';
import axios from 'axios';
import { useDropzone } from 'react-dropzone';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';
import ProjectFormReadOnlyView from './ProjectFormReadOnlyView';

// eslint-disable-next-line no-unused-vars
const AvatarWrapper = styled(Card)(
  ({ theme }) => `

    position: relative;
    overflow: visible;
    display: inline-block;
    margin-top: -${theme.spacing(9)};
    margin-left: ${theme.spacing(2)};

    .MuiAvatar-root {
      width: ${theme.spacing(16)};
      height: ${theme.spacing(16)};
    }
`
);

// eslint-disable-next-line no-unused-vars
const ButtonUploadWrapper = styled(Box)(
  ({ theme }) => `
    position: absolute;
    width: ${theme.spacing(4)};
    height: ${theme.spacing(4)};
    bottom: -${theme.spacing(1)};
    right: -${theme.spacing(1)};

    .MuiIconButton-root {
      border-radius: 100%;
      background: ${theme.colors.primary.main};
      color: ${theme.palette.primary.contrastText};
      box-shadow: ${theme.colors.shadows.primary};
      width: ${theme.spacing(4)};
      height: ${theme.spacing(4)};
      padding: 0;
  
      &:hover {
        background: ${theme.colors.primary.dark};
      }
    }
`
);

// eslint-disable-next-line no-unused-vars
const CardCover = styled(Card)(
  ({ theme }) => `
    position: relative;

    .MuiCardMedia-root {
      height: ${theme.spacing(26)};
    }
`
);

// eslint-disable-next-line no-unused-vars
const CardCoverAction = styled(Box)(
  ({ theme }) => `
    position: absolute;
    right: ${theme.spacing(2)};
    bottom: ${theme.spacing(2)};
`
);

const EditorWrapper = styled(Box)(
  ({ theme }) => `

    .ql-editor {
      min-height: 100px;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: ${theme.general.borderRadius};
      border-top-right-radius: ${theme.general.borderRadius};
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border-color: ${theme.colors.alpha.black[30]};
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: ${theme.general.borderRadius};
      border-bottom-right-radius: ${theme.general.borderRadius};
    }

    &:hover {
      .ql-toolbar.ql-snow,
      .ql-container.ql-snow {
        border-color: ${theme.colors.alpha.black[50]};
      }
    }
`
);

const StyledMenu = styled((props) => (
  <Menu
    elevation={0}
    anchorOrigin={{
      vertical: 'center',
      horizontal: 'right'
    }}
    transformOrigin={{
      vertical: 'center',
      horizontal: 'left'
    }}
    {...props}
  />
))(({ theme }) => ({
  '& .MuiPaper-root': {
    borderRadius: 6,
    marginTop: theme.spacing(1),
    minWidth: 180,
    color:
      theme.palette.mode === 'light'
        ? 'rgb(55, 65, 81)'
        : theme.palette.grey[300],
    boxShadow:
      'rgb(255, 255, 255) 0px 0px 0px 0px, rgba(0, 0, 0, 0.05) 0px 0px 0px 1px, rgba(0, 0, 0, 0.1) 0px 10px 15px -3px, rgba(0, 0, 0, 0.05) 0px 4px 6px -2px',
    '& .MuiMenu-list': {
      padding: '4px 0'
    },
    '& .MuiMenuItem-root': {
      '& .MuiSvgIcon-root': {
        fontSize: 18,
        color: theme.palette.text.secondary,
        marginRight: theme.spacing(1.5)
      },
      '&:active': {
        backgroundColor: alpha(
          theme.palette.primary.main,
          theme.palette.action.selectedOpacity
        )
      }
    }
  }
}));

const ProfileCover = ({ project, mutateProj }) => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { enqueueSnackbar } = useSnackbar();
  const [settings] = useContext(SettingsContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const { currentProject, setProject, checkoutConfig } = useAuth();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const orderButtonRef = useRef(null);
  const [orderAnchorEl, setOrderAnchorEl] = useState(null);
  const menuOpen = Boolean(orderAnchorEl);

  const [open, setOpen] = useState(false);
  const [shipCheck, setShipCheck] = useState({
    after: 0,
    prior: 0
  });

  const [editProjectNote, setEditProjectNote] = useState(false);
  const [projectNoteIsSubmitting, setProjectNoteIsSubmitting] = useState(false);
  const [projectNote, setProjectNote] = useState(
    project.projectContentBlurb || ''
  );
  const [projectFormId, setProjectFormId] = useState(project.formId || null);
  const [isLoadingProjectForm, setIsLoadingProjectForm] = useState(
    !project.formId && !!project?.id
  );

  const handleProjectNoteSubmit = () => {
    setProjectNoteIsSubmitting(true);

    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    let data = {
      project: project.id,
      portalBlurb: projectNote
    };

    axios
      .put(`${get_record_function_url}&type=UPDATEPORTALBLURB`, data)
      .then((res) => res.data)
      .then((resJson) => {
        console.log('resJson', resJson);

        if (resJson.message.includes('success')) {
          const getProjectContentsUrl =
            process.env.NODE_ENV === 'development'
              ? getDeploymentUrl.get_project_details_external
              : getDeploymentUrl.get_project_details_internal;
          project.projectContentBlurb = resJson.blurb;

          mutateProj({
            ...project,
            project: (project.projectContentBlurb = resJson.blurb)
          });
          setEditProjectNote(false);
          setProjectNote(resJson.blurb);
          setProjectNoteIsSubmitting(false);

          enqueueSnackbar(t('Project notes have been successfully updated'), {
            variant: 'success',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
        } else {
          setEditProjectNote(false);
          setProjectNote(project.projectContentBlurb);
          setProjectNoteIsSubmitting(false);

          enqueueSnackbar(t('Project Notes failed to update'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
        }
      });
  };

  const filter = createFilterOptions();

  const [exhibitSpaceValue, setExhibitSpaceValue] = useState(null);

  const handleExhibitSpaceChange = (event) => {
    setExhibitSpaceValue(event.target.value);
  };

  const [eventNameValue, seteventNameValue] = useState(null);

  const handleEventNameChange = (event) => {
    seteventNameValue(event.target.value);
  };

  const [showServicesOrderedBy, setShowServicesOrderedBy] = useState(null);

  const handleShowServicesOrderedByChange = (event) => {
    setShowServicesOrderedBy(event.target.value);
  };

  const [cardForShowServices, setCardForShowServices] = useState(null);

  const handleCardForShowServicesChange = (event) => {
    setCardForShowServices(event.target.value);
  };

  const [idLaborBy, setIdLaborBy] = useState(null);

  const handleIdLaborByChange = (event) => {
    setIdLaborBy(event.target.value);
  };

  const [unionLabor, setUnionLabor] = useState(null);

  const handleUnionLaborChange = (event) => {
    setUnionLabor(event.target.value);
  };

  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps
  } = useDropzone({
    accept:
      'image/jpeg, image/png, text/plain, application/pdf, application/docx, application/json, .gltf, .glb'
  });

  const getImageFileAttachment = (url) => {
    if (url) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  const handleClick = (event) => {
    setOrderAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setOrderAnchorEl(null);
  };

  const handleCreateProjectOpen = () => {
    setOpen(true);
  };

  const handleCreateProjectClose = (values) => {
    console.log('Values:', values);
    setOpen(false);
  };

  useEffect(() => {
    console.log('Settings', settings);
  }, []);

  // Fetch form ID if not available
  useEffect(() => {
    const fetchProjectFormId = async () => {
      if (!projectFormId && project?.id) {
        setIsLoadingProjectForm(true);
        try {
          const getProjectFormDataUrl =
            process.env.NODE_ENV === 'development'
              ? getDeploymentUrl.get_project_form_data_external
              : getDeploymentUrl.get_project_form_data_internal;

          const response = await axios.get(`${getProjectFormDataUrl}`, {
            params: {
              projectId: project.id
            }
          });

          if (response.data?.status === 'success' && response.data?.data) {
            // Handle the forms array structure
            const forms = Array.isArray(response.data.data)
              ? response.data.data
              : response.data.data?.forms || [];
            const selectedForm = forms.find((form) => form.selected);
            if (selectedForm) {
              setProjectFormId(selectedForm.id);
            }
          }
        } catch (error) {
          console.error('Error fetching project form ID:', error);
        } finally {
          setIsLoadingProjectForm(false);
        }
      } else if (projectFormId) {
        // If we already have a projectFormId, ensure loading state is false
        setIsLoadingProjectForm(false);
      }
    };

    fetchProjectFormId();
  }, [project?.id, projectFormId, getDeploymentUrl]);

  const handleBack = () => {
    return navigate(-1);
  };

  // useLayoutEffect(() => {
  //   if (user && settings) {
  //     if (user?.profile?.record) {
  //       let shipPrior =
  //         customerRec?.fields?.custentity_ng_eh_default_days_to_ship_pr;
  //       let shipAfter =
  //         customerRec?.fields?.custentity_ng_eh_default_days_to_ship_af;
  //
  //       if (shipPrior || shipAfter) {
  //         setShipCheck({
  //           prior: Number(shipPrior) || 0,
  //           after: Number(shipAfter) || 0
  //         });
  //       }
  //     }
  //   }
  // }, [settings, user]);

  const withinDateThreshold = (date, position) => {
    let currentDate = new Date(date);
    let priorThreshold = subDays(currentDate, shipCheck.prior);
    let afterThreshold = addDays(currentDate, shipCheck.after);
    let disabled = false;

    if (position === 'start') {
      disabled = isPast(priorThreshold);
    } else {
      disabled = isPast(priorThreshold);
    }

    return disabled;
  };

  const handleProjectSetForCart = (project) => {
    project.project = project.name;
    setProject(project);
    enqueueSnackbar(t(`Project ${project.name} has been set for checkout`), {
      variant: 'info',
      anchorOrigin: {
        vertical: 'top',
        horizontal: 'center'
      },
      TransitionComponent: Zoom
    });
  };

  const htmlFrom = (htmlString) => {
    const options = {
      replace: (domNode) => {
        /* if (domNode.type === 'tag' && domNode.name === 'img') {
          return null;
        } */
        return domNode;
      }
    };
    const cleanHtmlString = DOMPurify.sanitize(htmlString, {
      USE_PROFILES: { html: true }
    });
    const html = parse(cleanHtmlString, options);
    return html;
  };

  const handleCreateProjectSuccess = async (
    values,
    setStatus,
    setSubmitting,
    resetForm
  ) => {
    console.log('Values:', values);
    // eslint-disable-next-line camelcase
    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    console.log('Files Added?', acceptedFiles);

    const formData = new FormData();
    let allFileNames = acceptedFiles.map((fil, i) => `file${i}`);
    formData.append('fileObjNames', JSON.stringify(allFileNames));
    formData.append('projectId', project.id);
    if (acceptedFiles && acceptedFiles.length !== 0) {
      // eslint-disable-next-line array-callback-return
      acceptedFiles.map((fl, i) => {
        formData.append(`file${i}`, fl);
      });
    }

    if (Object.keys(values).length !== 0) {
      let formInputValues = Object.keys(values);
      formInputValues.forEach((key) => {
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else {
          formData.append(key, values[key]);
        }
      });
    }

    let requestOptions = {
      method: 'POST',
      redirect: 'follow',
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    return new Promise(() => {
      // eslint-disable-next-line camelcase
      axios
        .post(
          // eslint-disable-next-line camelcase
          `${get_record_function_url}&type=COPYPROJ`,
          formData,
          requestOptions
        )
        .then((res) => res.data)
        .then((resJSON) => {
          if (resJSON.message.includes('created')) {
            resetForm();
            setStatus({ success: true });
            setSubmitting(false);
            // eslint-disable-next-line camelcase
            const user_suitelet =
              process.env.NODE_ENV === 'development'
                ? process.env.REACT_APP_GET_USER_SUITELET
                : window.env.REACT_APP_GET_USER_SUITELET;
            // userMutate(user_suitelet);
            console.log('Created success!');
            enqueueSnackbar(t('A new project has been created successfully'), {
              variant: 'success',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          } else if (resJSON.saved) {
            setSubmitting(false);
            enqueueSnackbar(
              t('New project failed to add files but created successfully'),
              {
                variant: 'warning',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'right'
                },
                TransitionComponent: Zoom
              }
            );
          } else {
            setSubmitting(false);
            enqueueSnackbar(t('New project failed to save'), {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          }
          console.log(resJSON);
          return resJSON;
        })
        .catch((err) => {
          setStatus({ success: false });
          setSubmitting(false);
          console.error(err);
          enqueueSnackbar(t('New project failed to create'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
          Promise.reject(err);
        })
        .finally(() => {
          setOpen(false);
        });
    });
  };

  return (
    <>
      <Box
        display="flex"
        mb={3}
        sx={{
          padding: 2,
          backgroundColor: `${
            theme.palette.mode === 'dark'
              ? theme.colors.alpha.trueWhite[10]
              : theme.colors.alpha.white[70]
          }`,
          borderRadius: 1
        }}
      >
        <Tooltip arrow placement="top" title={t('Go back')}>
          <IconButton
            onClick={handleBack}
            color="primary"
            sx={{
              p: 2,
              mr: 2
            }}
          >
            <ArrowBackTwoToneIcon />
          </IconButton>
        </Tooltip>
        <Box alignItems="center">
          <Typography variant="h3" component="h3">
            Project Details
          </Typography>
          <Typography variant="subtitle2">{t(project?.name)}</Typography>
        </Box>
      </Box>
      {/* <CardCover>
        <CardMedia image={project?.bannerImage ? project.bannerImage : settings.custrecord_ng_eh_fallback_cover_img_url} style={{ backgroundPosition: 'bottom' }} />
        <CardCoverAction>
          <Input accept="image/*" id="change-cover" multiple type="file" />
          <label htmlFor="change-cover">
            <Button
              startIcon={<UploadTwoToneIcon />}
              variant="contained"
              component="span"
            >
              {t('Change cover')}
            </Button>
          </label>
        </CardCoverAction>
      </CardCover> */}
      {/* <AvatarWrapper>
        <Avatar sx={{ objectFit: 'contain'}} variant="rounded" alt={project.name} src={project.logo || settings.custrecord_ng_eh_react_logo_image_url} />
         <ButtonUploadWrapper>
          <Input
            accept="image/*"
            id="icon-button-file"
            name="icon-button-file"
            type="file"
          />
          <label htmlFor="icon-button-file">
            <IconButton component="span" color="primary">
              <UploadTwoToneIcon />
            </IconButton>
          </label>
        </ButtonUploadWrapper>
      </AvatarWrapper> */}
      <Box
        display={{ xs: 'block', md: 'flex' }}
        alignItems="center"
        justifyContent="space-between"
      >
        <Box py={1}>
          {project.id === currentProject?.id ? (
            <Button
              disabled
              startIcon={<CheckTwoTone />}
              color="success"
              variant="outlined"
              sx={{ mr: 2 }}
            >
              {t('Project set')}
            </Button>
          ) : (
            <Button
              variant="outlined"
              onClick={() => handleProjectSetForCart(project)}
              sx={{ mr: 2 }}
            >
              {t('Set Cart Order')}
            </Button>
          )}

          <Button
            id="order-customized-button"
            aria-owns={menuOpen ? 'order-customized-menu' : undefined}
            aria-haspopup="true"
            aria-expanded={menuOpen ? 'true' : undefined}
            variant="contained"
            disableElevation
            onMouseOver={handleClick}
            endIcon={<KeyboardArrowRightTwoToneIcon />}
            ref={orderButtonRef}
          >
            Order
          </Button>

          {settings.custrecord_ng_eh_print_proj_summary === 'T' ? (
            <Tooltip arrow placement="top" title={t('Print Project Summary')}>
              <IconButton
                aria-owns={menuOpen ? 'order-customized-menu' : undefined}
                aria-haspopup="true"
                aria-expanded={menuOpen ? 'true' : undefined}
                variant="contained"
                sx={{ ml: 2 }}
                onClick={() =>
                  window.open(
                    getImageFileAttachment(project.projectSummaryPrintUrl)
                  )
                }
              >
                <Print />
              </IconButton>
            </Tooltip>
          ) : null}

          <StyledMenu
            variant="menu"
            id="order-customized-menu"
            MenuListProps={{
              'aria-labelledby': 'order-customized-button',
              onMouseLeave: handleClose
            }}
            anchorEl={orderAnchorEl}
            open={menuOpen}
            TransitionComponent={Fade}
            onClose={handleClose}
          >
            {checkoutConfig.inventory && (
              <MenuItem
                component={RouterLink}
                to="/inventory/nonrentals"
                onClick={handleClose}
                disableRipple
              >
                <Inventory2TwoTone />
                {t('Inventory')}
              </MenuItem>
            )}
            {checkoutConfig.rentals && (
              <MenuItem
                component={RouterLink}
                to="/inventory/rentals"
                onClick={handleClose}
                disableRipple
              >
                <WarehouseTwoTone />
                {t('Rentals')}
              </MenuItem>
            )}
          </StyledMenu>
          {/* <IconButton */}
          {/*  color="primary" */}
          {/*  sx={{ */}
          {/*    p: 0.5 */}
          {/*  }} */}
          {/* > */}
          {/*  <MoreHorizTwoToneIcon /> */}
          {/* </IconButton> */}
        </Box>

        <Box mt={{ xs: 3, md: 0 }}>
          <Button
            sx={{
              mt: { xs: 2, sm: 0 },
              mr: 2
            }}
            onClick={() =>
              navigate(`/management/projects/edit/${project.id}`, {
                state: { mode: 'edit', projectName: project.name }
              })
            }
            variant="outlined"
            disabled={isLoadingProjectForm}
            startIcon={
              isLoadingProjectForm ? (
                <CircularProgress
                  size={16}
                  thickness={5}
                  sx={{
                    color: theme.palette.primary.main,
                    opacity: 0.8
                  }}
                />
              ) : (
                <Edit fontSize="small" />
              )
            }
          >
            {isLoadingProjectForm ? t('Loading...') : t('Edit Project')}
          </Button>

          {settings.custrecord_ng_eh_ext_prj_copy_options === 'T' ? (
            <Button
              sx={{
                mt: { xs: 2, sm: 0 }
              }}
              onClick={() =>
                navigate(`/management/projects/copy/${project.id}`, {
                  state: { projectName: project.name }
                })
              }
              variant="contained"
              disabled={isLoadingProjectForm}
              startIcon={
                isLoadingProjectForm ? (
                  <CircularProgress
                    size={16}
                    thickness={5}
                    sx={{
                      color: theme.palette.primary.contrastText,
                      opacity: 0.9
                    }}
                  />
                ) : (
                  <AddTwoToneIcon fontSize="small" />
                )
              }
            >
              {isLoadingProjectForm ? t('Loading...') : t('Copy project')}
            </Button>
          ) : null}
        </Box>
      </Box>
      {/* Project Form Data - Read Only View */}
      {projectFormId && (
        <Box
          mb={1}
          sx={{
            py: !mobile && 2,
            pb: 1,
            pl: !mobile && 2,
            mr: !mobile && 2
          }}
        >
          <ProjectFormReadOnlyView
            projectId={project.id}
            formId={projectFormId}
          />
        </Box>
      )}
    </>
  );
};

ProfileCover.propTypes = {
  project: PropTypes.object.isRequired
};

export default ProfileCover;
