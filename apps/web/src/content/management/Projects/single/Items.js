import React, { useContext } from 'react';
import {
  alpha,
  Box,
  Card,
  CardHeader,
  Divider,
  List,
  styled,
  Typography,
  useTheme
} from '@mui/material';

import { DataGridPro, GridToolbar } from '@mui/x-data-grid-pro';
import { useTranslation } from 'react-i18next';
import Scrollbar from 'src/components/Scrollbar';
import { SettingsContext } from '../../../../contexts/SettingsContext';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';

const CardContentWrapper = styled(Box)(
  ({ theme }) => `
    background: ${theme.colors.alpha.white[100]};
    border-radius: ${theme.general.borderRadius};
  `
);

const ListWrapper = styled(List)(
  () => `
    .MuiDivider-root:last-of-type {
        display: none;
    }
  `
);

function Items({ project }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useContext(SettingsContext);

  console.log('THE ITEMS', project);

  const getImageFileAttachment = (url) => {
    if (url) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  const rows = project.salesOrderItems.map((itm, i) => {
    return {
      id: i,
      itemImg: getImageFileAttachment(itm?.imageUrl),
      item: itm.item,
      quantity: itm.quantity,
      rentalLocation: itm.rentalLocation,
      startDate: itm.rentalStartDate,
      endDate: itm.rentalEndDate,
      soNum: itm.soNum
    };
  });

  const itemCol = [
    {
      field: 'itemImg',
      headerName: 'Image',
      width: 200,
      renderCell: (params) => <img src={params.value} />,
      disableExport: true
    },
    { field: 'item', headerName: 'Item', width: 475 },
    { field: 'quantity', headerName: 'Quantity', width: 75 },
    { field: 'rentalLocation', headerName: 'Location', width: 200 },
    { field: 'startDate', headerName: 'Start Date', width: 150 },
    { field: 'endDate', headerName: 'End Date', width: 150 },
    { field: 'soNum', headerName: 'Order Number', width: 150 }
  ];

  return (
    <Card
      variant="outlined"
      sx={{
        background:
          theme.palette.mode === 'dark'
            ? `${theme.colors.alpha.white[70]}`
            : `${alpha(theme.colors.alpha.trueWhite[100], 0.7)}`,
        height: '100%'
      }}
    >
      <CardHeader
        sx={{
          p: 2
        }}
        disableTypography
        title={
          <Box>
            <Typography fontWeight="bold" variant="caption">
              {t('Items Ordered')}
            </Typography>
            <Typography variant="body2">
              {t('View the items below ordered for the current project')}
            </Typography>
          </Box>
        }
      />
      <CardContentWrapper
        sx={{
          mx: 3,
          mb: 3,
          minHeight: 400,
          height: '85%'
        }}
      >
        {project.salesOrderItems.length !== 0 ? (
          <Scrollbar>
            <ListWrapper disablePadding>
              <Divider />
              <DataGridPro
                loading={!rows}
                rows={rows}
                columns={itemCol}
                initialState={{
                  pagination: {
                    paginationModel: {
                      pageSize: 5
                    }
                  },
                  columns: {
                    columnVisibilityModel: {
                      id: false
                    }
                  }
                }}
                autoHeight
                pageSizeOptions={[5]}
                rowHeight={150}
                slots={{ toolbar: GridToolbar }}
                checkboxSelection={false}
                disableRowSelectionOnClick
              />
              <Divider />
            </ListWrapper>
          </Scrollbar>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center'
            }}
          >
            <Typography variant="h1" sx={{ textAlign: 'center', pt: 10 }}>
              No Items Ordered On Project
            </Typography>
          </Box>
        )}
      </CardContentWrapper>
    </Card>
  );
}

export default Items;
