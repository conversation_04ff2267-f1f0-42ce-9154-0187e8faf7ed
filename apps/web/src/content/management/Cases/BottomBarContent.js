/* eslint-disable jsx-a11y/accessible-emoji */
/* eslint-disable jsx-a11y/label-has-for */
import {
  Alert,
  Avatar,
  Box,
  Button,
  Divider,
  Grid,
  IconButton,
  InputBase,
  List,
  ListItem,
  ListItemText,
  styled,
  Tooltip,
  Typography,
  useMediaQuery,
  Zoom
} from '@mui/material';

import { useTheme } from '@mui/material/styles';
import axios from 'axios';
import * as Yup from 'yup';
import { Formik } from 'formik';
import useAuth from 'src/hooks/useAuth';
import { useTranslation } from 'react-i18next';
import AttachFileTwoToneIcon from '@mui/icons-material/AttachFileTwoTone';
import SendTwoToneIcon from '@mui/icons-material/SendTwoTone';
import CheckTwoToneIcon from '@mui/icons-material/CheckTwoTone';
import CloseTwoToneIcon from '@mui/icons-material/CloseTwoTone';
import CloudUploadTwoToneIcon from '@mui/icons-material/CloudUploadTwoTone';
import { useContext, useEffect, useRef } from 'react';
import { useDropzone } from 'react-dropzone';
import { useSnackbar } from 'notistack';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

const MessageInputWrapper = styled(InputBase)(
  ({ theme }) => `
    font-size: ${theme.typography.pxToRem(18)};
    padding: ${theme.spacing(1)};
    width: 100%;
`
);

const BoxUploadWrapper = styled(Box)(
  ({ theme }) => `
    border-radius: ${theme.general.borderRadius};
    padding: ${theme.spacing(3)};
    background: ${theme.colors.alpha.black[5]};
    border: 1px dashed ${theme.colors.alpha.black[30]};
    outline: none;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: ${theme.transitions.create(['border', 'background'])};

    &:hover {
      background: ${theme.colors.alpha.white[100]};
      border-color: ${theme.colors.primary.main};
    }
`
);

const AvatarWrapper = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.primary.lighter};
    color: ${theme.colors.primary.main};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const AvatarSuccess = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.success.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const AvatarDanger = styled(Avatar)(
  ({ theme }) => `
    background: ${theme.colors.error.light};
    width: ${theme.spacing(7)};
    height: ${theme.spacing(7)};
`
);

const Input = styled('input')({
  display: 'none'
});

function BottomBarContent({
  caseDetails,
  discussionId,
  caseMutate,
  caseDetailsMutate,
  getLastMessageSentRef,
  userLogo
}) {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { getDeploymentUrl } = useHoistedUrls();
  const theme = useTheme();
  const { enqueueSnackbar } = useSnackbar();
  const chatboxRef = useRef();
  const mobile = useMediaQuery(theme.breakpoints.down('md'));

  const {
    acceptedFiles,
    isDragActive,
    isDragAccept,
    isDragReject,
    getRootProps,
    getInputProps
  } = useDropzone({
    accept:
      'image/jpeg, image/png, text/plain, application/pdf, application/docx, .gltf, .glb'
  });

  useEffect(() => {
    console.log('Ref changed:', chatboxRef);
  }, [chatboxRef]);

  const files = acceptedFiles.map((file, index) => (
    <ListItem disableGutters component="div" key={index}>
      <ListItemText primary={file.name} />
      <b>{file.size} bytes</b>
      <Divider />
    </ListItem>
  ));

  const handleCreateMessageSuccess = async (
    values,
    setStatus,
    setSubmitting,
    resetForm
  ) => {
    console.log('Values:', values);
    // eslint-disable-next-line camelcase
    let get_record_function_url =
      process.env.NODE_ENV === 'development'
        ? getDeploymentUrl.record_operation_external
        : getDeploymentUrl.record_operation_internal;

    console.log('Files Added?', acceptedFiles);

    const formData = new FormData();
    let allFileNames = acceptedFiles.map((fil, i) => `file${i}`);
    formData.append('fileObjNames', JSON.stringify(allFileNames));
    if (acceptedFiles && acceptedFiles.length !== 0) {
      // eslint-disable-next-line array-callback-return
      acceptedFiles.map((fl, i) => {
        formData.append(`file${i}`, fl);
      });
    }

    if (Object.keys(values).length !== 0) {
      let formInputValues = Object.keys(values);
      formInputValues.forEach((key) => {
        if (Array.isArray(values[key])) {
          formData.append(key, JSON.stringify(values[key]));
        } else {
          formData.append(key, values[key]);
        }
      });
    }

    let requestOptions = {
      method: 'POST',
      redirect: 'follow',
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    };

    return new Promise(() => {
      // eslint-disable-next-line camelcase
      axios
        .post(
          // eslint-disable-next-line camelcase
          `${get_record_function_url}&type=MESSAGECREATE`,
          formData,
          requestOptions
        )
        .then((res) => res.data)
        .then((resJSON) => {
          if (resJSON.message.includes('created')) {
            resetForm();
            setStatus({ success: true });
            setSubmitting(false);
            acceptedFiles.length = 0;
            const getCasesUrl =
              process.env.NODE_ENV === 'development'
                ? getDeploymentUrl.get_customer_cases_external
                : getDeploymentUrl.get_customer_cases_internal;
            const getCaseDetailsUrl =
              process.env.NODE_ENV === 'development'
                ? getDeploymentUrl.get_customer_case_external
                : getDeploymentUrl.get_customer_case_internal;

            caseMutate(`${getCasesUrl}`);
            caseDetailsMutate(`${getCaseDetailsUrl}`, {
              ...caseDetails,
              messages: resJSON.supportCase
            });
            if (getLastMessageSentRef?.current) {
              getLastMessageSentRef.current.scrollIntoView();
            }
            // eslint-disable-next-line camelcase
            console.log('Created success!');
            enqueueSnackbar(t('A new message has been posted successfully'), {
              variant: 'success',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          } else if (resJSON.saved) {
            setSubmitting(false);
            enqueueSnackbar(
              t('New message failed to add files but created successfully'),
              {
                variant: 'warning',
                anchorOrigin: {
                  vertical: 'top',
                  horizontal: 'right'
                },
                TransitionComponent: Zoom
              }
            );
          } else {
            setSubmitting(false);
            enqueueSnackbar(t('New message failed to post'), {
              variant: 'error',
              anchorOrigin: {
                vertical: 'top',
                horizontal: 'right'
              },
              TransitionComponent: Zoom
            });
          }
          console.log(resJSON);
          return resJSON;
        })
        .catch((err) => {
          setStatus({ success: false });
          setSubmitting(false);
          console.error(err);
          enqueueSnackbar(t('New message failed to post'), {
            variant: 'error',
            anchorOrigin: {
              vertical: 'top',
              horizontal: 'right'
            },
            TransitionComponent: Zoom
          });
          Promise.reject(err);
        });
    });
  };

  return (
    <>
      {files.length > 0 && (
        <Grid
          sx={{
            mb: `${theme.spacing(3)}`
          }}
          item
          xs={12}
        >
          <>
            <Alert
              sx={{
                py: 0,
                mt: 2
              }}
              severity="success"
            >
              {t('You have uploaded')} <b>{files.length}</b> {t('files')}!
            </Alert>
            <Divider
              sx={{
                mt: 2
              }}
            />
            <List component="div">{files}</List>
          </>
          <hr />
        </Grid>
      )}
      <Box
        sx={{
          background: theme.colors.alpha.white[50],
          display: 'flex',
          alignItems: 'center',
          p: 2,
          paddingBottom: mobile ? 4 : 0
        }}
        onDragEnter={() => {
          chatboxRef.current = { dropping: true };
        }}
        onDragLeave={() => {
          chatboxRef.current = { dropping: false };
        }}
        onDrop={() => {
          chatboxRef.current = { dropping: false };
        }}
      >
        <Avatar
          sx={{ display: { xs: 'none', sm: 'flex' }, mr: 1 }}
          alt={user.name}
          src={userLogo}
        />
        <Formik
          enableReinitialize={discussionId !== caseDetails?.details?.id}
          style={{
            width: '100%'
          }}
          initialValues={{
            caseId: discussionId,
            caseTitle: caseDetails?.details?.title,
            message: '',
            email: user.email,
            submit: false
          }}
          validationSchema={Yup.object().shape({
            message: Yup.string().required(
              t('Please enter a message to add it to the discussion')
            )
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              setSubmitting(true);
              await handleCreateMessageSuccess(
                _values,
                setStatus,
                setSubmitting,
                resetForm
              );
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values
          }) => (
            <Box
              display="flex"
              alignItems="center"
              sx={{
                width: '100%'
              }}
            >
              {chatboxRef.current?.dropping ? (
                <BoxUploadWrapper {...getRootProps()}>
                  <input {...getInputProps()} />
                  {isDragAccept && (
                    <>
                      <AvatarSuccess variant="rounded">
                        <CheckTwoToneIcon />
                      </AvatarSuccess>
                      <Typography
                        sx={{
                          mt: 2
                        }}
                      >
                        {t('Drop the files to start uploading')}
                      </Typography>
                    </>
                  )}
                  {isDragReject && (
                    <>
                      <AvatarDanger variant="rounded">
                        <CloseTwoToneIcon />
                      </AvatarDanger>
                      <Typography
                        sx={{
                          mt: 2
                        }}
                      >
                        {t('You cannot upload these file types')}
                      </Typography>
                    </>
                  )}
                  {!isDragActive && (
                    <>
                      <AvatarWrapper variant="rounded">
                        <CloudUploadTwoToneIcon />
                      </AvatarWrapper>
                      <Typography
                        sx={{
                          mt: 2
                        }}
                      >
                        {t('Drag & drop files here to attach to project')}
                      </Typography>
                    </>
                  )}
                </BoxUploadWrapper>
              ) : null}
              <form
                onSubmit={handleSubmit}
                style={{
                  flexGrow: 1,
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <MessageInputWrapper
                  autoFocus
                  placeholder={t('Write your message here...')}
                  fullWidth
                  name="message"
                  value={values.message}
                  onChange={handleChange}
                  error={Boolean(touched.message && errors.message)}
                />
                <Box display="flex">
                  {/* <Tooltip arrow placement="top" title={t('Choose an emoji')}>
                        <IconButton
                            sx={{ fontSize: theme.typography.pxToRem(16) }}
                            color="primary"
                        >
                          😀
                        </IconButton>
                      </Tooltip> */}
                  <Input
                    accept="image/jpeg, image/png, text/plain, application/pdf, application/docx, .gltf, .glb"
                    id="messenger-upload-file"
                    type="file"
                    onChange={(e) => acceptedFiles.push(e.target.files[0])}
                  />
                  <Tooltip arrow placement="top" title={t('Attach a file')}>
                    <label htmlFor="messenger-upload-file">
                      <IconButton
                        sx={{ mx: 1 }}
                        color="primary"
                        component="span"
                      >
                        <AttachFileTwoToneIcon fontSize="small" />
                      </IconButton>
                    </label>
                  </Tooltip>
                  {mobile ? (
                    <IconButton
                      disabled={isSubmitting}
                      variant="contained"
                      type="submit"
                    >
                      <SendTwoToneIcon />
                    </IconButton>
                  ) : (
                    <Button
                      disabled={isSubmitting}
                      startIcon={<SendTwoToneIcon />}
                      variant="contained"
                      type="submit"
                    >
                      {t('Send')}
                    </Button>
                  )}
                </Box>
              </form>
            </Box>
          )}
        </Formik>
      </Box>
    </>
  );
}

export default BottomBarContent;
