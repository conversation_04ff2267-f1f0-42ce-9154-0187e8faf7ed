import { Fragment, useEffect, useRef } from 'react';
import {
  Avatar,
  Box,
  Card,
  CardActionArea,
  CardActions,
  CardContent,
  CardMedia,
  Chip,
  Divider,
  Grid,
  styled,
  Tooltip,
  Typography,
  useMediaQuery
} from '@mui/material';
import useAuth from 'src/hooks/useAuth';
import { format } from 'date-fns';
import ScheduleTwoToneIcon from '@mui/icons-material/ScheduleTwoTone';
import DOMPurify from 'dompurify';
import parse from 'html-react-parser';
import GifBoxTwoToneIcon from '@mui/icons-material/GifBoxTwoTone';
import PictureAsPdfTwoToneIcon from '@mui/icons-material/PictureAsPdfTwoTone';
import PhotoTwoToneIcon from '@mui/icons-material/PhotoTwoTone';
import ArticleTwoToneIcon from '@mui/icons-material/ArticleTwoTone';
import { ViewInArTwoTone } from '@mui/icons-material';
import InsertDriveFileTwoToneIcon from '@mui/icons-material/InsertDriveFileTwoTone';
import { useTheme } from '@mui/material/styles';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

// import { Player } from '@lottiefiles/react-lottie-player';

const DividerWrapper = styled(Divider)(
  ({ theme }) => `
      .MuiDivider-wrapper {
        border-radius: ${theme.general.borderRadiusSm};
        text-transform: none;
        background: ${theme.palette.background.default};
        font-size: ${theme.typography.pxToRem(13)};
        color: ${theme.colors.alpha.black[50]};
      }
`
);

const CardWrapperPrimary = styled(Card)(
  ({ theme }) => `
      background: ${theme.colors.primary.main};
      color: ${theme.palette.primary.contrastText};
      padding: ${theme.spacing(2)};
      border-radius: ${theme.general.borderRadiusXl};
      border-top-right-radius: ${theme.general.borderRadius};
      max-width: 380px;
      display: inline-flex;
`
);

const CardWrapperSecondary = styled(Card)(
  ({ theme }) => `
      background: ${theme.colors.alpha.black[10]};
      color: ${theme.colors.alpha.black[100]};
      padding: ${theme.spacing(2)};
      border-radius: ${theme.general.borderRadiusXl};
      border-top-left-radius: ${theme.general.borderRadius};
      max-width: 380px;
      display: inline-flex;
`
);

function ChatContent({
  caseDetails,
  setItemViewing,
  toggleThreeDimensionalModal,
  setLastMessageRef,
  getLastMessageRef
}) {
  const { user } = useAuth();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const lastMessageSentBoxRef = useRef();
  const mobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    setLastMessageRef(lastMessageSentBoxRef);
    console.log('New ref set:', getLastMessageRef);
  }, [caseDetails]);

  const htmlFrom = (htmlString) => {
    const options = {
      /*  replace: (domNode) => {
                if (domNode.type === 'tag' && domNode.name === 'img') {
                    return null
                }
                return domNode
            } */
    };
    const cleanHtmlString = DOMPurify.sanitize(htmlString, {
      USE_PROFILES: { html: true }
    });
    const html = parse(cleanHtmlString, options);
    return html;
  };

  const renderFileThumbnailIcon = (item) => {
    let imagesCheck = ['jpeg', 'jpg', 'pjpeg', 'x-png', 'png'];
    let docsCheck = ['doc', 'docx', 'txt'];
    let threedCheck = ['glb', 'gltf'];
    if (item.extension === 'gif') {
      return <GifBoxTwoToneIcon fontSize="large" />;
    }
    if (item.extension === 'pdf') {
      return <PictureAsPdfTwoToneIcon fontSize="large" />;
    }
    if (imagesCheck.includes(item.extension)) {
      return <PhotoTwoToneIcon fontSize="large" />;
    }
    if (docsCheck.includes(item.extension)) {
      return <ArticleTwoToneIcon fontSize="large" />;
    }
    if (threedCheck.includes(item.extension)) {
      return <ViewInArTwoTone fontSize="large" />;
    }
    return <InsertDriveFileTwoToneIcon fontSize="large" />;
  };

  function formatBytes(bytes, decimals = 2) {
    let kilobytes = bytes * 1000;
    if (bytes === 0) return '0 KB';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    // eslint-disable-next-line no-restricted-properties
    return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  const viewFileAttachment = (fileAtt) => {
    window.open(
      `${getDeploymentUrl?.core_account_url}${fileAtt.url}`,
      '_blank'
    );
  };

  const toggleThreeDModal = (item, toggle) => {
    setItemViewing(item);
    toggleThreeDimensionalModal(toggle);
  };

  return (
    <Box p={3}>
      {caseDetails?.messages &&
        caseDetails?.messages.map((msg, i) => {
          let dateLabel = null;
          let messageFromMe =
            process.env.NODE_ENV === 'development'
              ? msg.authoremail === '<EMAIL>'
              : msg.authoremail === user.email;
          let { messages } = caseDetails;
          let messageDate = format(new Date(msg.messagedate), 'MMMM dd yyyy');
          let prevMessageDate =
            i === 0
              ? null
              : format(new Date(messages[i - 1].messagedate), 'MMMM dd yyyy');
          let lastMessage = messages.length === i + 1;
          if (i === 0) {
            dateLabel = (
              <DividerWrapper>
                {format(new Date(msg.messagedate), 'MMMM dd yyyy')}
              </DividerWrapper>
            );
          } else if (prevMessageDate && messageDate !== prevMessageDate) {
            dateLabel = (
              <DividerWrapper>
                {format(new Date(msg.messagedate), 'MMMM dd yyyy')}
              </DividerWrapper>
            );
          }

          let fromOther = (
            <Box
              display="flex"
              alignItems="flex-start"
              justifyContent="flex-start"
              py={3}
              ref={lastMessage ? lastMessageSentBoxRef : null}
            >
              <Tooltip title={msg.authoremail} placement="bottom">
                <Avatar
                  variant="rounded"
                  sx={{
                    width: 50,
                    height: 50
                  }}
                >
                  {msg.authoremail && msg.authoremail[0].toUpperCase()}
                </Avatar>
              </Tooltip>
              <Box
                display="flex"
                alignItems="flex-start"
                flexDirection="column"
                justifyContent="flex-start"
                ml={2}
              >
                <CardWrapperSecondary>
                  {htmlFrom(atob(msg.message))}
                </CardWrapperSecondary>
                {msg.files && msg.files.length !== 0 ? (
                  <Box flexDirection="row" display="flex">
                    <CardActions disableSpacing>
                      <Box
                        flexDirection="row"
                        display="flex"
                        mt={2}
                        alignItems="flex-start"
                      >
                        {msg.files.map((item) => {
                          let renderAttachment = <></>;
                          switch (item.extension) {
                            case 'png':
                            case 'jpg':
                            case 'jpeg':
                              renderAttachment = (
                                <Card
                                  sx={{
                                    mr: 2
                                  }}
                                >
                                  <Tooltip title={item.name}>
                                    <CardActionArea
                                      onClick={() => viewFileAttachment(item)}
                                    >
                                      <CardMedia
                                        component="img"
                                        height="64"
                                        image={`${getDeploymentUrl?.core_account_url}${item.url}`}
                                        alt={item.name}
                                      />
                                    </CardActionArea>
                                  </Tooltip>
                                </Card>
                              );
                              break;
                            case 'glb':
                            case 'gltf':
                              renderAttachment = (
                                <Card
                                  sx={{ mr: 2, height: 64 }}
                                  key={item.name}
                                >
                                  <Tooltip title={item.name}>
                                    <CardActionArea
                                      onClick={() =>
                                        toggleThreeDModal(item, true)
                                      }
                                    >
                                      <Box
                                        display="flex"
                                        alignItems="center"
                                        alignContent="center"
                                      >
                                        <Grid container spacing={1}>
                                          <Grid item xs={4}>
                                            <Avatar
                                              color="primary"
                                              variant="square"
                                              sx={{ width: '100%', height: 64 }}
                                            >
                                              {renderFileThumbnailIcon(item)}
                                            </Avatar>
                                          </Grid>
                                          <Grid item xs={8}>
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                flexDirection: 'row'
                                              }}
                                            >
                                              <CardContent
                                                sx={{ flex: '1 0 auto' }}
                                              >
                                                <Typography
                                                  variant="subtitle1"
                                                  color="text.secondary"
                                                  component="div"
                                                >
                                                  {formatBytes(item.size)}
                                                </Typography>
                                              </CardContent>
                                            </Box>
                                          </Grid>
                                        </Grid>
                                      </Box>
                                    </CardActionArea>
                                  </Tooltip>
                                </Card>
                              );
                              break;
                            default:
                              renderAttachment = (
                                <Card
                                  sx={{ mr: 2, height: 64 }}
                                  key={item.name}
                                >
                                  <Tooltip title={item.name}>
                                    <CardActionArea
                                      onClick={() => viewFileAttachment(item)}
                                    >
                                      <Box
                                        display="flex"
                                        alignItems="center"
                                        alignContent="center"
                                      >
                                        <Grid container spacing={1}>
                                          <Grid item xs={4}>
                                            <Avatar
                                              color="primary"
                                              variant="square"
                                              sx={{ width: '100%', height: 64 }}
                                            >
                                              {renderFileThumbnailIcon(item)}
                                            </Avatar>
                                          </Grid>
                                          <Grid item xs={8}>
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                flexDirection: 'row'
                                              }}
                                            >
                                              <CardContent
                                                sx={{ flex: '1 0 auto' }}
                                              >
                                                <Typography
                                                  variant="subtitle1"
                                                  color="text.secondary"
                                                  component="div"
                                                >
                                                  {formatBytes(item.size)}
                                                </Typography>
                                              </CardContent>
                                            </Box>
                                          </Grid>
                                        </Grid>
                                      </Box>
                                    </CardActionArea>
                                  </Tooltip>
                                </Card>
                              );
                          }

                          return renderAttachment;
                        })}
                      </Box>
                    </CardActions>
                  </Box>
                ) : null}

                <Tooltip arrow title="" placement={mobile ? 'bottom' : 'right'}>
                  <Box sx={{ pt: 1, mr: 0.5 }}>
                    <Chip
                      size="small"
                      variant="outlined"
                      label={
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontSize: '8pt',
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          <ScheduleTwoToneIcon
                            sx={{
                              mr: 0.5
                            }}
                            fontSize="small"
                          />
                          {msg.messagedate}
                          {/*  {formatDistanceToNow(new Date(msg.messagedate), {
                                          addSuffix: true
                                      })} */}
                        </Typography>
                      }
                    />
                  </Box>
                </Tooltip>
              </Box>
            </Box>
          );

          let fromMe = (
            <Box
              display="flex"
              alignItems="flex-start"
              justifyContent="flex-end"
              py={3}
              ref={lastMessage ? lastMessageSentBoxRef : null}
            >
              <Box
                display="flex"
                alignItems="flex-end"
                flexDirection="column"
                justifyContent="flex-end"
                mr={2}
              >
                <CardWrapperPrimary>
                  {htmlFrom(atob(msg.message))}
                </CardWrapperPrimary>
                {msg.files && msg.files.length !== 0 ? (
                  <Box flexDirection="row" display="flex">
                    <CardActions disableSpacing>
                      <Box
                        flexDirection="row"
                        display="flex"
                        mt={2}
                        alignItems="flex-start"
                      >
                        {msg.files.map((item) => {
                          let renderAttachment = <></>;
                          switch (item.extension) {
                            case 'png':
                            case 'jpg':
                            case 'jpeg':
                              renderAttachment = (
                                <Card
                                  sx={{
                                    mr: 2
                                  }}
                                >
                                  <Tooltip title={item.name}>
                                    <CardActionArea
                                      onClick={() => viewFileAttachment(item)}
                                    >
                                      <CardMedia
                                        component="img"
                                        height="64"
                                        image={`${getDeploymentUrl?.core_account_url}${item.url}`}
                                        alt={item.name}
                                      />
                                    </CardActionArea>
                                  </Tooltip>
                                </Card>
                              );
                              break;
                            default:
                              renderAttachment = (
                                <Card
                                  sx={{ mr: 2, height: 64 }}
                                  key={item.name}
                                >
                                  <Tooltip title={item.name}>
                                    <CardActionArea
                                      onClick={() => viewFileAttachment(item)}
                                    >
                                      <Box
                                        display="flex"
                                        alignItems="center"
                                        alignContent="center"
                                      >
                                        <Grid container spacing={1}>
                                          <Grid item xs={4}>
                                            <Avatar
                                              color="primary"
                                              variant="square"
                                              sx={{ width: '100%', height: 64 }}
                                            >
                                              {renderFileThumbnailIcon(item)}
                                            </Avatar>
                                          </Grid>
                                          <Grid item xs={8}>
                                            <Box
                                              sx={{
                                                display: 'flex',
                                                flexDirection: 'row'
                                              }}
                                            >
                                              <CardContent
                                                sx={{ flex: '1 0 auto' }}
                                              >
                                                <Typography
                                                  variant="subtitle1"
                                                  color="text.secondary"
                                                  component="div"
                                                >
                                                  {formatBytes(item.size)}
                                                </Typography>
                                              </CardContent>
                                            </Box>
                                          </Grid>
                                        </Grid>
                                      </Box>
                                    </CardActionArea>
                                  </Tooltip>
                                </Card>
                              );
                          }

                          return renderAttachment;
                        })}
                      </Box>
                    </CardActions>
                  </Box>
                ) : null}
                <Tooltip arrow title="" placement={mobile ? 'bottom' : 'right'}>
                  <Box sx={{ pt: 1, mr: 0.5 }}>
                    <Chip
                      size="small"
                      variant="outlined"
                      label={
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontSize: '8pt',
                            display: 'flex',
                            alignItems: 'center'
                          }}
                        >
                          <ScheduleTwoToneIcon
                            sx={{
                              mr: 0.5
                            }}
                            fontSize="small"
                          />
                          {msg.messagedate}
                          {/*  {formatDistanceToNow(new Date(msg.messagedate), {
                                          addSuffix: true
                                      })} */}
                        </Typography>
                      }
                    />
                  </Box>
                </Tooltip>
              </Box>
              <Tooltip title={user?.name} placement="bottom">
                <Avatar
                  color="primary"
                  variant="rounded"
                  sx={{
                    width: 50,
                    height: 50
                  }}
                >
                  {user?.name && user?.name[0].toUpperCase()}
                </Avatar>
              </Tooltip>
            </Box>
          );

          return (
            <Fragment key={`msg-${i}`}>
              {dateLabel}
              {messageFromMe ? fromMe : fromOther}
            </Fragment>
          );
        })}
    </Box>
  );
}

export default ChatContent;
