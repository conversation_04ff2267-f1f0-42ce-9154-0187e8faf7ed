import { useEffect, useState } from 'react';
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  <PERSON>,
  <PERSON><PERSON>,
  Di<PERSON>r,
  Drawer,
  IconButton,
  lighten,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  styled,
  Tooltip,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

import { useTranslation } from 'react-i18next';
import InfoTwoToneIcon from '@mui/icons-material/InfoTwoTone';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import GifBoxTwoToneIcon from '@mui/icons-material/GifBoxTwoTone';
import PictureAsPdfTwoToneIcon from '@mui/icons-material/PictureAsPdfTwoTone';
import PhotoTwoToneIcon from '@mui/icons-material/PhotoTwoTone';
import ArticleTwoToneIcon from '@mui/icons-material/ArticleTwoTone';
import {
  ArrowDownwardTwoTone,
  InboxTwoTone,
  ViewInArTwoTone
} from '@mui/icons-material';
import InsertDriveFileTwoToneIcon from '@mui/icons-material/InsertDriveFileTwoTone';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

const RootWrapper = styled(Box)(
  ({ theme }) => `
        @media (min-width: ${theme.breakpoints.values.md}px) {
          display: flex;
          align-items: center;
          justify-content: space-between;
      }
`
);

const ListItemIconWrapper = styled(ListItemIcon)(
  ({ theme }) => `
        min-width: 36px;
        color: ${theme.colors.primary.light};
`
);

const AccordionSummaryWrapper = styled(AccordionSummary)(
  ({ theme }) => `
        &.Mui-expanded {
          min-height: 48px;
        }

        .MuiAccordionSummary-content.Mui-expanded {
          margin: 12px 0;
        }

        .MuiSvgIcon-root {
          transition: ${theme.transitions.create(['color'])};
        }

        &.MuiButtonBase-root {

          margin-bottom: ${theme.spacing(0.5)};

          &:last-child {
            margin-bottom: 0;
          }

          &.Mui-expanded,
          &:hover {
            background: ${theme.colors.alpha.black[10]};

            .MuiSvgIcon-root {
              color: ${theme.colors.primary.main};
            }
          }
        }
`
);

function TopBarContent({ discussionId, caseDetails, getLastMessageSentRef }) {
  const { t } = useTranslation();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const [mobileOpen, setMobileOpen] = useState(false);
  const [filesShared, setFilesShared] = useState([]);
  const [caseFiles, setCaseFiles] = useState([]);
  const mobile = useMediaQuery(theme.breakpoints.down('md'));

  const handleDrawerToggle = () => {
    setMobileOpen(!mobileOpen);
  };

  const [expanded, setExpanded] = useState('section3');

  const handleChange = (section) => (_event, isExpanded) => {
    setExpanded(isExpanded ? section : false);
  };

  useEffect(() => {
    let active = true;

    if (active) {
      console.log(
        `%cDisscussion: ${discussionId} loaded`,
        'color: lime; padding: 3px; font-size: 18pt;'
      );
    }

    return () => {
      active = false;
    };
  }, []);

  // Set shared files from messages on mount
  useEffect(() => {
    console.log('Case detail effect:', caseDetails);
    if (caseDetails?.messages && caseDetails?.messages.length !== 0) {
      let files = [];
      let messagesOnlyWithFiles = caseDetails.messages.filter(
        (msg) => msg?.files && msg.files.length !== 0
      );
      console.log('Messages only with files:', messagesOnlyWithFiles);
      messagesOnlyWithFiles.forEach((msg) => {
        let msgFiles = msg.files;
        msgFiles.forEach((msgFile) => {
          files.push(msgFile);
        });
      });
      setFilesShared(files);
      console.log('Files shared:', files);
    } else {
      setFilesShared([]);
    }
  }, [caseDetails]);

  // Set files attached to case on mount
  useEffect(() => {
    console.log('Case detail effect for case files:', caseDetails);
    if (caseDetails?.files && caseDetails?.files.length !== 0) {
      setCaseFiles(caseDetails.files);
    } else {
      setCaseFiles([]);
    }
  }, [caseDetails]);

  const renderFileThumbnailIcon = (item) => {
    let imagesCheck = ['jpeg', 'jpg', 'pjpeg', 'x-png', 'png'];
    let docsCheck = ['doc', 'docx', 'txt'];
    let threedCheck = ['glb', 'gltf'];
    if (item.extension === 'gif') {
      return <GifBoxTwoToneIcon />;
    }
    if (item.extension === 'pdf') {
      return <PictureAsPdfTwoToneIcon />;
    }
    if (imagesCheck.includes(item.extension)) {
      return <PhotoTwoToneIcon />;
    }
    if (docsCheck.includes(item.extension)) {
      return <ArticleTwoToneIcon />;
    }
    if (threedCheck.includes(item.extension)) {
      return <ViewInArTwoTone />;
    }
    return <InsertDriveFileTwoToneIcon />;
  };

  const viewFileAttachment = (fileAtt) => {
    window.open(
      `${getDeploymentUrl?.core_account_url}${fileAtt.url}`,
      '_blank'
    );
  };

  const scrollToLastMessage = () => {
    if (getLastMessageSentRef?.current) {
      getLastMessageSentRef.current.scrollIntoView();
    }
  };

  function formatBytes(bytes, decimals = 2) {
    let kilobytes = bytes * 1000;
    if (bytes === 0) return '0 KB';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    // eslint-disable-next-line no-restricted-properties
    return `${parseFloat((bytes / k ** i).toFixed(dm))} ${sizes[i]}`;
  }

  return (
    <>
      <RootWrapper>
        {caseDetails && (
          <>
            <Box
              sx={{
                display: { sm: 'flex' }
              }}
              alignItems="center"
            >
              {/* <Avatar */}
              {/*  variant="rounded" */}
              {/*  sx={{ */}
              {/*    width: 50, */}
              {/*    height: 50 */}
              {/*  }} */}
              {/*  alt={caseDetails?.details?.title} */}
              {/* > */}
              {/*  {caseDetails?.details?.title */}
              {/*    .split(' ') */}
              {/*    .map((l) => l[0]) */}
              {/*    .join('')} */}
              {/* </Avatar> */}
              <Box
                sx={{
                  pl: { sm: 1.5 },
                  pt: { xs: 1.5, sm: 0 }
                }}
              >
                <Typography variant="h4" gutterBottom>
                  {caseDetails?.details?.projectName}
                </Typography>
                <Typography variant="h4" gutterBottom>
                  {caseDetails?.details?.title}
                </Typography>
                {caseDetails?.details?.lastmessagedate ? (
                  <Typography variant="subtitle2">
                    Last message received:{' '}
                    {caseDetails?.details.lastmessagedate}
                  </Typography>
                ) : (
                  <Typography variant="subtitle2">
                    No messages received
                  </Typography>
                )}
              </Box>
            </Box>
            <Box
              sx={{
                mt: { xs: 3, md: 0 },
                float: { xs: 'right' }
              }}
            >
              <Stack
                direction="row"
                divider={<Divider orientation="vertical" flexItem />}
                spacing={1}
              >
                <Tooltip
                  arrow
                  placement="bottom"
                  title={t('Scroll to last message')}
                >
                  <IconButton color="primary" onClick={scrollToLastMessage}>
                    <ArrowDownwardTwoTone />
                  </IconButton>
                </Tooltip>
                <Tooltip
                  enterDelay={500}
                  leaveDelay={200}
                  arrow
                  placement="bottom"
                  title={
                    <>
                      <Box>
                        <Typography variant="h5" sx={{ textAlign: 'center' }}>
                          {t('Conversation information')}
                        </Typography>
                        <Divider
                          sx={{
                            backgroundColor: lighten(
                              theme.colors.primary.main,
                              0.2
                            )
                          }}
                        />
                        <Typography variant="body2" fontSize="small">
                          See attachments to all messages and current
                          discussion.
                        </Typography>
                      </Box>
                    </>
                  }
                >
                  {mobile ? (
                    <IconButton
                      color="primary"
                      variant="outlined"
                      onClick={handleDrawerToggle}
                    >
                      <InfoTwoToneIcon />
                    </IconButton>
                  ) : (
                    <Button
                      color="primary"
                      variant="outlined"
                      onClick={handleDrawerToggle}
                      endIcon={<InfoTwoToneIcon />}
                    >
                      View Attachments
                    </Button>
                  )}
                </Tooltip>
              </Stack>
            </Box>
          </>
        )}
      </RootWrapper>
      {caseDetails && (
        <Drawer
          variant="temporary"
          anchor={theme.direction === 'rtl' ? 'left' : 'right'}
          open={mobileOpen}
          onClose={handleDrawerToggle}
          elevation={9}
        >
          <Box
            sx={{
              minWidth: 360
            }}
            p={2}
          >
            <Box
              sx={{
                textAlign: 'center'
              }}
            >
              {/* <Avatar */}
              {/*  sx={{ */}
              {/*    mx: 'auto', */}
              {/*    my: 2, */}
              {/*    width: theme.spacing(12), */}
              {/*    height: theme.spacing(12) */}
              {/*  }} */}
              {/*  variant="rounded" */}
              {/*  alt={caseDetails?.details?.title} */}
              {/* > */}
              {/*  {caseDetails?.details?.title */}
              {/*    .split(' ') */}
              {/*    .map((l) => l[0]) */}
              {/*    .join('')} */}
              {/* </Avatar> */}
              <Typography variant="h4">
                {caseDetails?.details?.projectName}
              </Typography>
              <Typography variant="h4">
                {caseDetails?.details?.title}
              </Typography>
              <Typography variant="subtitle2">
                {t('Last message received')} {/* {formatDistanceToNow( */}
                {/*  new Date(caseDetails?.details?.lastmessagedate), */}
                {/*  { */}
                {/*    addSuffix: true */}
                {/*  } */}
                {/* )} */}
              </Typography>
              <Typography variant="subtitle2">
                {caseDetails?.details?.lastmessagedate}
              </Typography>
            </Box>
            <Divider
              sx={{
                my: 3
              }}
            />
            {/*  <Accordion
            expanded={expanded === 'section1'}
            onChange={handleChange('section1')}
          >
            <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h5">{t('Customize Chat')}</Typography>
            </AccordionSummaryWrapper>
            <AccordionDetails
              sx={{
                p: 0
              }}
            >
              <List component="nav">
                <ListItem button>
                  <ListItemIconWrapper>
                    <SearchTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Search in Conversation')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
                <ListItem button>
                  <ListItemIconWrapper>
                    <ColorLensTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Change Theme Styling')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
                <ListItem button>
                  <ListItemIconWrapper>
                    <EmojiEmotionsTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Choose Default Emoji')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion>
          <Accordion
            expanded={expanded === 'section2'}
            onChange={handleChange('section2')}
          >
            <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
              <Typography variant="h5">{t('Privacy & Support')}</Typography>
            </AccordionSummaryWrapper>
            <AccordionDetails
              sx={{
                p: 0
              }}
            >
              <List component="nav">
                <ListItem button>
                  <ListItemIconWrapper>
                    <NotificationsOffTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Turn off notifications')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
                <ListItem button>
                  <ListItemIconWrapper>
                    <CancelTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Ignore all messages')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
                <ListItem button>
                  <ListItemIconWrapper>
                    <BlockTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t('Block user')}
                    primaryTypographyProps={{ variant: 'h5' }}
                  />
                </ListItem>
                <ListItem button>
                  <ListItemIconWrapper>
                    <WarningTwoToneIcon />
                  </ListItemIconWrapper>
                  <ListItemText
                    primary={t("Something's Wrong")}
                    primaryTypographyProps={{ variant: 'h5' }}
                    secondary={t(
                      'Report the conversation and provide feedback'
                    )}
                    secondaryTypographyProps={{ variant: 'subtitle1' }}
                  />
                </ListItem>
              </List>
            </AccordionDetails>
          </Accordion> */}
            <Accordion
              expanded={expanded === 'section3'}
              onChange={handleChange('section3')}
            >
              <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h5">{t('Discussion Files')}</Typography>
              </AccordionSummaryWrapper>
              <AccordionDetails
                sx={{
                  p: 0
                }}
              >
                <List component="nav">
                  {caseFiles && caseFiles.length !== 0 ? (
                    caseFiles.map((item) => {
                      return (
                        <ListItemButton
                          key={item.name}
                          onClick={() => viewFileAttachment(item)}
                        >
                          <ListItemIconWrapper>
                            {renderFileThumbnailIcon(item)}
                          </ListItemIconWrapper>
                          <ListItemText
                            primary={t(item.name)}
                            primaryTypographyProps={{ variant: 'h5' }}
                            secondary={t(formatBytes(item.documentsize))}
                            secondaryTypographyProps={{ variant: 'subtitle1' }}
                          />
                        </ListItemButton>
                      );
                    })
                  ) : (
                    <ListItemButton>
                      <ListItemIconWrapper>
                        <InboxTwoTone />
                      </ListItemIconWrapper>
                      <ListItemText
                        primary={t('No files here')}
                        primaryTypographyProps={{ variant: 'h4' }}
                        secondary={t('Try uploading some files')}
                        secondaryTypographyProps={{ variant: 'subtitle1' }}
                      />
                    </ListItemButton>
                  )}
                </List>
              </AccordionDetails>
            </Accordion>
            <Accordion
              expanded={expanded === 'section4'}
              onChange={handleChange('section4')}
            >
              <AccordionSummaryWrapper expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h5">{t('Message Files')}</Typography>
              </AccordionSummaryWrapper>
              <AccordionDetails
                sx={{
                  p: 0
                }}
              >
                <List component="nav">
                  {filesShared && filesShared.length !== 0 ? (
                    filesShared.map((item) => {
                      return (
                        <ListItemButton
                          key={item.name}
                          onClick={() => viewFileAttachment(item)}
                        >
                          <ListItemIconWrapper>
                            {renderFileThumbnailIcon(item)}
                          </ListItemIconWrapper>
                          <ListItemText
                            primary={t(item.name)}
                            primaryTypographyProps={{ variant: 'h5' }}
                            secondary={t(formatBytes(item.size))}
                            secondaryTypographyProps={{ variant: 'subtitle1' }}
                          />
                        </ListItemButton>
                      );
                    })
                  ) : (
                    <ListItemButton>
                      <ListItemIconWrapper>
                        <InboxTwoTone />
                      </ListItemIconWrapper>
                      <ListItemText
                        primary={t('No files here')}
                        primaryTypographyProps={{ variant: 'h4' }}
                        secondary={t('Try uploading some files')}
                        secondaryTypographyProps={{ variant: 'subtitle1' }}
                      />
                    </ListItemButton>
                  )}
                </List>
              </AccordionDetails>
            </Accordion>
          </Box>
        </Drawer>
      )}
    </>
  );
}

export default TopBarContent;
