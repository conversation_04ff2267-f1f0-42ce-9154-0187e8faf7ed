import React, { useContext } from 'react';
import {
  alpha,
  Box,
  Card,
  CardContent,
  CardHeader,
  Divider,
  Grid,
  List,
  styled,
  Typography,
  useTheme
} from '@mui/material';

import { DataGridPro, GridToolbar } from '@mui/x-data-grid-pro';
import { useTranslation } from 'react-i18next';
import { SettingsContext } from '../../../contexts/SettingsContext';
import useHoistedUrls from '../../../hooks/useHoistedUrls';
import useSWR from 'swr';
import { Player } from '@lottiefiles/react-lottie-player';
import { LoadingTruck } from '../../../utils/lottieAnimate';
import { motion } from 'framer-motion';

const CardContentWrapper = styled(Box)(
  ({ theme }) => `
    background: ${theme.colors.alpha.white[100]};
    border-radius: ${theme.general.borderRadius};
  `
);

const ListWrapper = styled(List)(
  () => `
    .MuiDivider-root:last-of-type {
        display: none;
    }
  `
);

function ItemsReport() {
  const { t } = useTranslation();
  const theme = useTheme();
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useContext(SettingsContext);

  const ForwardTypography = React.forwardRef((props, ref) => (
    <Typography ref={ref} {...props}>
      {props.children}
    </Typography>
  ));

  const MotionTypography = motion(ForwardTypography);

  const getProjectContentsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_reports_external
      : getDeploymentUrl.get_reports_internal;

  const { data, error, mutate } = useSWR(
    `${getProjectContentsUrl}&type=GETALLITEMS`
  );

  const getImageFileAttachment = (url) => {
    if (url) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  const rows = data?.items?.map((itm, i) => {
    return {
      id: i,
      itemImg: getImageFileAttachment(itm?.imageUrl),
      item: itm.item,
      quantity: itm.quantity,
      projectName: itm.projectName,
      tranDate: itm.tranDate,
      soNum: itm.tranName,
      rentalLocation: itm.rentalLocation,
      rentalStartDate: itm.rentalStartDate,
      rentalEndDate: itm.rentalEndDate
    };
  });

  const itemCol = [
    {
      field: 'itemImg',
      headerName: 'Image',
      width: 200,
      renderCell: (params) => <img src={params.value} />,
      disableExport: true
    },
    { field: 'item', headerName: 'Item', width: 300 },
    { field: 'quantity', headerName: 'Quantity', width: 75 },
    { field: 'projectName', headerName: 'Project', width: 250 },
    { field: 'tranDate', headerName: 'Transaction Date', width: 140 },
    { field: 'soNum', headerName: 'Order Number', width: 150 },
    { field: 'rentalLocation', headerName: 'Location', width: 200 },
    { field: 'rentalStartDate', headerName: 'Start Date', width: 150 },
    { field: 'rentalEndDate', headerName: 'End Date', width: 150 }
  ];

  const getRowSpacing = React.useCallback((params) => {
    return {
      top: params.isFirstVisible ? 0 : 5,
      bottom: params.isLastVisible ? 0 : 5
    };
  }, []);

  if (error) {
    return (
      <Grid container spacing={3} alignItems="center" sx={{ flexGrow: 1 }}>
        <Grid item xs={12}>
          <Grid
            contianer
            justifyContent="center"
            alignItems="center"
            spacing={3}
          >
            <Grid item xs={12} alignSelf="center">
              <motion.div styles={containerStyles}>
                <motion.div
                  onPointerMove={onMove}
                  style={{
                    rotateY,
                    rotateX,
                    textAlign: 'center',
                    fontFamily: 'Montserrat'
                  }}
                  initial={{
                    y: 1000,
                    opacity: 0
                  }}
                  animate={{
                    y: 0,
                    scale: 1.5,
                    opacity: 1,
                    alignItems: 'center'
                  }}
                  transition={{
                    type: 'spring',
                    stiffness: 260,
                    damping: 20
                  }}
                >
                  <Box
                    p={5}
                    pt={20}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}
                  >
                    <Card>
                      <CardContent>
                        <Box p={3}>
                          <Player
                            src={EmptyFiles()}
                            autoplay
                            loop
                            controls={false}
                            style={{
                              height: '80%',
                              width: '80%',
                              borderRadius: '1rem'
                            }}
                          />
                        </Box>
                        <MotionTypography
                          sx={{ fontFamily: 'Montserrat' }}
                          component={ForwardTypography}
                        >
                          Error Occurred Loading Reports!
                        </MotionTypography>
                        <MotionTypography
                          gutterBottom
                          variant="subtitle2"
                          color="secondary"
                          sx={{ fontFamily: 'Montserrat' }}
                          fontSize="small"
                        >
                          Check permissions or contact administrator for details
                        </MotionTypography>
                        <Box p={3}>
                          <MotionMuiButton
                            variant="outlined"
                            fullWidth
                            transition={{
                              type: 'fade'
                            }}
                            whileHover={{
                              backdropFilter: 'blur(2px)'
                            }}
                            whileTap={{
                              scale: 1.05
                            }}
                            onClick={() => navigate('/management/projects')}
                          >
                            Go Back
                          </MotionMuiButton>
                        </Box>
                      </CardContent>
                    </Card>
                  </Box>
                </motion.div>
              </motion.div>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  if (!data) {
    return (
      <Grid container spacing={3} justifyContent="center" alignItems="center">
        <Grid container spacing={2} alignItems="center" justifyContent="center">
          <Grid item xs={6}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              p={5}
              pt={20}
            >
              <Card>
                <CardContent>
                  <Player
                    src={LoadingTruck()}
                    autoplay
                    loop
                    controls={false}
                    style={{
                      height: '50%',
                      width: '50%',
                      borderRadius: '1rem'
                    }}
                  />
                </CardContent>
              </Card>
            </Box>
          </Grid>
          <Grid item xs={12}>
            <MotionTypography
              variant="h2"
              initial={{
                opacity: 0,
                y: -15
              }}
              animate={{
                opacity: 1,
                y: 0
              }}
              transition={{
                ease: [0.17, 0.67, 0.83, 0.67],
                y: { type: 'spring', stiffness: 100 },
                default: {
                  duration: 1,
                  repeat: Infinity,
                  repeatDelay: 1,
                  repeatType: 'reverse'
                }
              }}
              style={{
                textAlign: 'center'
              }}
            >
              Loading Reports...
            </MotionTypography>
          </Grid>
        </Grid>
      </Grid>
    );
  }

  return (
    <Card
      variant="outlined"
      sx={{
        background:
          theme.palette.mode === 'dark'
            ? `${theme.colors.alpha.white[70]}`
            : `${alpha(theme.colors.alpha.trueWhite[100], 0.7)}`,
        height: '100%'
      }}
    >
      <CardHeader
        sx={{
          p: 2
        }}
        disableTypography
        title={
          <Box>
            <Typography fontWeight="bold" variant="caption">
              {t('Rental History')}
            </Typography>
            <Typography variant="body2">
              {t('View the rental history below')}
            </Typography>
          </Box>
        }
      />
      <CardContentWrapper
        sx={{
          mx: 3,
          mb: 3,
          minHeight: 900,
          height: '85%'
        }}
      >
        {data?.items && data?.items.length !== 0 ? (
          <ListWrapper disablePadding>
            <Divider />
            <DataGridPro
              loading={!rows}
              rows={rows}
              columns={itemCol}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 5
                  }
                },
                columns: {
                  columnVisibilityModel: {
                    id: false
                  }
                }
              }}
              autoHeight
              pageSizeOptions={[5]}
              checkboxSelection={false}
              disableRowSelectionOnClick
              getRowSpacing={getRowSpacing}
              rowHeight={150}
              slots={{ toolbar: GridToolbar }}
              pageSize={20}
            />
            <Divider />
          </ListWrapper>
        ) : (
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              alignContent: 'center'
            }}
          >
            <Typography variant="h1" sx={{ textAlign: 'center', pt: 10 }}>
              No Items Ordered
            </Typography>
          </Box>
        )}
      </CardContentWrapper>
    </Card>
  );
}
export default ItemsReport;
