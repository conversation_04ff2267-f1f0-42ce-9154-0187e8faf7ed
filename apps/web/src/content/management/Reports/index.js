import React, { useContext, useEffect, useRef, useState } from 'react';

import { Helmet } from 'react-helmet-async';
import useSWR from 'swr';

import {
  Badge,
  Box,
  Button,
  Card,
  CardContent,
  Divider,
  Grid,
  styled,
  Tab,
  Tabs,
  Typography,
  useMediaQuery
} from '@mui/material';
import Footer from 'src/components/Footer';
import useRefMounted from 'src/hooks/useRefMounted';
import useAuth from '../../../hooks/useAuth';
import AssignmentTwoToneIcon from '@mui/icons-material/AssignmentTwoTone';
import InventoryIcon from '@mui/icons-material/Inventory';
import WorkHistoryIcon from '@mui/icons-material/WorkHistory';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';
import ItemsReport from './ItemsReport';
import InventoryList from './InventoryList';
import RecentInvoices from './RecentInvoices';
import RecentOrders from '../../../content/pages/User/RecentOrders';
import { motion, useMotionValue, useTransform } from 'framer-motion';
import { Player } from '@lottiefiles/react-lottie-player';
import { EmptyFiles, LoadingTruck } from '../../../utils/lottieAnimate';
import { useNavigate } from 'react-router-dom';
import useHoistedUrls from '../../../hooks/useHoistedUrls';
import { SettingsContext } from '../../../contexts/SettingsContext';

function ProjectReports() {
  const isMountedRef = useRefMounted();
  const { getDeploymentUrl } = useHoistedUrls();
  const { t } = useTranslation();
  const [projects, setProjects] = useState([]);
  const [currentTab, setCurrentTab] = useState('items');
  const navigate = useNavigate();
  const profileCoverEl = useRef(null);
  const [tabContainerHeight, setTabContainerHeight] = useState(0);

  const [settings] = useContext(SettingsContext);

  useEffect(() => {
    setTabContainerHeight(profileCoverEl.current?.clientHeight);
  });

  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { user, username, userMutate } = useAuth();

  const TabsWrapper = styled(Tabs)(
    () => `
    .MuiTabs-scrollableX {
      overflow-x: auto !important;

      .MuiTabs-indicator {
        box-shadow: none;
      }
    }
`
  );

  useEffect(() => {
    if (isMountedRef && user && user?.profile) {
      setProjects(user.profile.projects.list);
    }
  }, [user]);

  useEffect(() => {}, [projects]);

  const getProjectContentsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_reports_external
      : getDeploymentUrl.get_reports_internal;

  let data = {};

  // const {
  //   data,
  //   error,
  //   mutate
  // } = useSWR(`${getProjectContentsUrl}&type=GETALLITEMS`);

  const handleTabsChange = (_event, value) => {
    setCurrentTab(value);
  };

  const tabs = [
    {
      value: 'items',
      label: t('Rental History'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          // variant="dot"
          // badgeContent={data && data?.items && data?.items.length}
        >
          <AssignmentTwoToneIcon />
        </Badge>
      )
    },
    {
      value: 'inventoryList',
      label: t('Inventory List'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          // variant="dot"
          // badgeContent={data && data?.items && data?.items.length}
        >
          <InventoryIcon />
        </Badge>
      )
    }
  ];

  if (settings.custrecord_ng_eh_disable_recent_orders !== 'T') {
    tabs.push({
      value: 'recentOrders',
      label: t('Recent Orders'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          // variant="dot"
          // badgeContent={data && data?.items && data?.items.length}
        >
          <WorkHistoryIcon />
        </Badge>
      )
    });
  }
  if (settings.custrecord_ng_eh_disable_recent_invoices !== 'T') {
    tabs.push({
      value: 'recentInvoices',
      label: t('Recent Invoices'),
      icon: (
        <Badge
          sx={{ zIndex: 1000 }}
          color="primary"
          // variant="dot"
          // badgeContent={data && data?.items && data?.items.length}
        >
          <WorkHistoryIcon />
        </Badge>
      )
    });
  }

  const ForwardTypography = React.forwardRef((props, ref) => (
    <Typography ref={ref} {...props}>
      {props.children}
    </Typography>
  ));

  const MotionTypography = motion(ForwardTypography);

  const ForwardButton = React.forwardRef((props, ref) => (
    <Button ref={ref} {...props}>
      {props.children}
    </Button>
  ));

  const MotionMuiButton = motion(ForwardButton);

  const containerStyles = `
     perspective: 500;
     height: 400px;
     /* center card item */
     display: flex;
     justify-content: center;
     align-items: center;
  `;

  const [angle] = React.useState(8);

  // we replace the useState with two motion values. One for each axis.
  // Since we want the card to start out flat we set the initial
  // values to x=0.5 y=0.5 which equals to no transformation
  const y = useMotionValue(0.5);
  const x = useMotionValue(0.5);

  const rotateY = useTransform(x, [0, 1], [-angle, angle], {
    clamp: true
  });
  const rotateX = useTransform(y, [0, 1], [angle, -angle], {
    clamp: true
  });

  const onMove = (e) => {
    // get position information for the card
    const bounds = e.currentTarget.getBoundingClientRect();

    // set x,y local coordinates
    const xValue = (e.clientX - bounds.x) / e.currentTarget.clientWidth;
    const yValue = (e.clientY - bounds.y) / e.currentTarget.clientHeight;

    // update MotionValues
    x.set(xValue, true);
    y.set(yValue, true);
  };

  return (
    <>
      <Helmet>
        <title>Reports</title>
      </Helmet>
      {/*<Grid display="flex" alignItems="center" item xs={12} px={4} pb={4}>*/}
      {/*  <Box flex={1} mt={3}>*/}
      {/*     <PageHeader />*/}
      {/*  </Box>*/}
      {/*</Grid>*/}
      <Box
        sx={{
          mt: 3,
          minHeight: '95vh'
        }}
      >
        <Box
          display="flex"
          alignItems={{ xs: 'stretch', md: 'center' }}
          flexDirection={{ xs: 'column', md: 'row' }}
          justifyContent="space-between"
          sx={{
            padding: 3,
            backgroundColor: `${
              theme.palette.mode === 'dark'
                ? theme.colors.alpha.trueWhite[10]
                : theme.colors.alpha.white[70]
            }`,
            borderRadius: 1
          }}
        >
          <Box display="flex" alignItems="center">
            <Box>
              <Typography variant="h3" component="h3" gutterBottom>
                {t('Reports Center')}
              </Typography>
            </Box>
          </Box>
        </Box>
        <Divider />
        <Box
          mb={1}
          sx={{
            py: !mobile && 2,
            pb: 1,
            pl: !mobile && 2,
            mr: !mobile && 2
          }}
        ></Box>
        <Grid
          sx={{
            px: 4
          }}
          container
          direction="row"
          justifyContent="center"
          alignItems="stretch"
          spacing={4}
        >
          <Grid item xs={12}>
            <TabsWrapper
              onChange={handleTabsChange}
              value={currentTab}
              variant={mobile ? 'fullWidth' : 'scrollable'}
              scrollButtons="auto"
              textColor="secondary"
              indicatorColor="primary"
              centered={mobile}
            >
              {tabs.map((tab) =>
                mobile ? (
                  <Tab
                    key={tab.value}
                    aria-label={tab.label}
                    value={tab.value}
                    iconPosition="bottom"
                    icon={tab.icon}
                  />
                ) : (
                  <Tab
                    key={tab.value}
                    label={tab.label}
                    value={tab.value}
                    iconPosition="end"
                    icon={tab.icon}
                  />
                )
              )}
            </TabsWrapper>
          </Grid>
          <Grid
            item
            xs={12}
            sx={{ height: `calc(${tabContainerHeight}px + 70vh)` }}
          >
            {currentTab === 'items' && <ItemsReport />}
            {currentTab === 'inventoryList' && <InventoryList />}
            {currentTab === 'recentOrders' && (
              <RecentOrders user={user} username={username} />
            )}
            {currentTab === 'recentInvoices' && (
              <RecentInvoices user={user} username={username} />
            )}
          </Grid>
        </Grid>
      </Box>
      <Footer />
    </>
  );
}

export default ProjectReports;
