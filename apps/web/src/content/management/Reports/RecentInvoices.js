import { <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>H<PERSON>er, Chip, Divider } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';
import {
  CheckCircleTwoTone,
  InfoTwoTone,
  LocalShippingOutlined,
  PrintTwoTone,
  UpdateTwoTone
} from '@mui/icons-material';
import { DataGridPro } from '@mui/x-data-grid-pro';
import useSettings from '../../../hooks/useSettings';

function RecentInvoices({ user }) {
  const [settings] = useSettings();
  const { t } = useTranslation();
  // Recent Orders Table
  const [rows, setRows] = useState([]);
  const [columns, setColumns] = useState([]);

  useEffect(() => {
    if (user && settings) {
      if (user.profile?.recentInvoices) {
        parseFields(user.profile.recentInvoices);
      }
    }
  }, [user]);

  const createTableCol = (field, headerName, width, type, sortable) => {
    let column = {
      field,
      headerName,
      // label: headerName,
      width,
      flex: 1
    };

    if (type) {
      if (typeof type === 'string') {
        // eslint-disable-next-line no-unused-expressions
        column = { type, ...column };
      }
    }
    if (sortable) {
      if (typeof sortable === 'boolean') {
        // eslint-disable-next-line no-unused-expressions
        column = { sort: sortable, ...column };
      }
    }
    // log.debug({ title: 'Column Created', details: column })
    return column;
  };

  const parseFields = (orders) => {
    console.log('Parsing fields', orders);
    let currRows = [];
    let currCols = [];
    currCols.push(createTableCol('tranid', 'Order Number', 200));
    currCols.push(createTableCol('trandate', 'Order Date', 130));
    currCols.push(createTableCol('project', 'Project', 130));
    currCols.push(createTableCol('amount', 'Amount', 125));
    let printCol = createTableCol('print', 'Print', 125);

    printCol.renderCell = (params) => {
      let cellRender = null;
      if (params.value.includes('https')) {
        cellRender = (
          <Button
            component="a"
            href={`${params.value}`}
            target="_blank"
            endIcon={<PrintTwoTone />}
          >
            Print
          </Button>
        );
      }

      return cellRender;
    };

    let statusCol = createTableCol('statusref', 'Status', 235);
    statusCol.renderCell = (params) => {
      // console.log('params col status', params)
      let cellRender = null;
      if (params.value.toLowerCase() === 'pending fulfillment') {
        cellRender = (
          <Chip
            icon={<LocalShippingOutlined />}
            label={params.value}
            color="info"
          />
        );
      } else if (params.value.toLowerCase() === 'pending billing') {
        cellRender = (
          <Chip icon={<UpdateTwoTone />} label={params.value} color="warning" />
        );
      } else if (params.value.toLowerCase() === 'billed') {
        cellRender = (
          <Chip
            icon={<CheckCircleTwoTone />}
            label={params.value}
            color="success"
          />
        );
      } else if (params.value.toLowerCase() === 'pending approval') {
        cellRender = (
          <Chip
            icon={<UpdateTwoTone />}
            label={params.value}
            color="secondary"
          />
        );
      } else {
        cellRender = (
          <Chip icon={<InfoTwoTone />} label={params.value} color="primary" />
        );
      }
      return cellRender;
    };

    // currCols.push(statusCol);
    currCols.push(printCol);
    setColumns(currCols);
    orders.forEach((order) => {
      // Order ForEach
      if (!order.disableInPortal) {
        currRows.push(order);
      }
    });
    setRows(currRows);
  };

  return (
    <Card>
      <CardHeader title={t('Recent Invoices')} />
      <Divider />
      <Box
        px={2}
        py={4}
        display="flex"
        alignItems="center"
        justifyContent="center"
        sx={{ width: '100%', height: 500 }}
      >
        <DataGridPro
          rows={rows}
          columns={columns}
          loading={rows.length === 0}
          rowHeight={50}
          autoPageSize
          disableRowSelectionOnClick
        />
      </Box>
    </Card>
  );
}

export default RecentInvoices;
