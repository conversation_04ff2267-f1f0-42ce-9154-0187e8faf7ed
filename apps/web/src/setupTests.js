// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

// Import Jest-based axios mock setup for API mocking
import { setupDefaultMocks, resetMocks } from './__tests__/setup/axios-mock';

// Setup default mocks
setupDefaultMocks();

// Reset mocks before each test
beforeEach(() => {
  resetMocks();
});

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.sessionStorage = sessionStorageMock;

// Mock window.env for NetSuite environment variables
global.window.env = {
  REACT_APP_GET_USER_SUITELET: 'https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1&compid=TSTDRV2149044&h=abc123',
  REACT_APP_SUITELET_SETTINGS: 'https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=903&deploy=1&compid=TSTDRV2149044&h=def456',
  REACT_APP_RESTLET_SETTINGS: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/restlet.nl?script=904&deploy=1&compid=TSTDRV2149044&h=ghi789'
};

// Mock react-i18next for tests
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key,
    i18n: {
      changeLanguage: () => new Promise(() => {}),
    },
  }),
  initReactI18next: {
    type: '3rdParty',
    init: () => {},
  },
  Trans: ({ children }) => children,
  Translation: ({ children }) => children,
}));

// Mock i18next core module
jest.mock('i18next', () => ({
  use: jest.fn().mockReturnThis(),
  init: jest.fn().mockReturnThis(),
  t: (key) => key,
  changeLanguage: () => new Promise(() => {}),
  language: 'en',
  languages: ['en'],
}));

// Mock axios-mock-adapter to prevent import issues
jest.mock('axios-mock-adapter', () => {
  return jest.fn().mockImplementation(() => ({
    onGet: jest.fn().mockReturnThis(),
    onPost: jest.fn().mockReturnThis(),
    onPut: jest.fn().mockReturnThis(),
    onDelete: jest.fn().mockReturnThis(),
    onAny: jest.fn().mockReturnThis(),
    reply: jest.fn().mockReturnThis(),
    restore: jest.fn(),
    reset: jest.fn(),
  }));
});

// Mock axios to prevent interceptors issues
jest.mock('axios', () => ({
  create: jest.fn(() => ({
    interceptors: {
      request: { use: jest.fn(), eject: jest.fn() },
      response: { use: jest.fn(), eject: jest.fn() }
    },
    get: jest.fn(),
    post: jest.fn(),
    put: jest.fn(),
    patch: jest.fn(),
    delete: jest.fn(),
    defaults: {}
  })),
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  patch: jest.fn(),
  delete: jest.fn(),
  defaults: {
    headers: {
      common: {},
      get: {},
      post: {},
      put: {},
      patch: {},
      delete: {}
    }
  },
  interceptors: {
    request: { use: jest.fn(), eject: jest.fn() },
    response: { use: jest.fn(), eject: jest.fn() }
  }
}));



// Mock MUI system useMediaQuery specifically
jest.mock('@mui/system/useMediaQuery', () => ({
  __esModule: true,
  default: jest.fn(() => false)
}));

// Mock js-cookie for theme and components
jest.mock('js-cookie', () => ({
  get: jest.fn(() => undefined),
  set: jest.fn(),
  remove: jest.fn()
}));

// Mock useMediaQuery specifically to handle the matches property issue
global.window.matchMedia = jest.fn(() => ({
  matches: false,
  addListener: jest.fn(),
  removeListener: jest.fn(),
  addEventListener: jest.fn(),
  removeEventListener: jest.fn(),
  dispatchEvent: jest.fn(),
}));

// Mock Lottie Player to prevent canvas issues
jest.mock('@lottiefiles/react-lottie-player', () => ({
  Player: jest.fn(({ children, ...props }) => <div data-testid="lottie-player" {...props}>{children}</div>)
}));

// Mock missing MUI RtlProvider
jest.mock('@mui/system/RtlProvider', () => ({
  __esModule: true,
  default: ({ children }) => children
}));

// Mock @google/model-viewer to prevent DOM token issues
jest.mock('@google/model-viewer', () => ({}));

// Mock framer-motion to prevent addListener issues
jest.mock('framer-motion', () => ({
  motion: {
    div: 'div',
    span: 'span',
    button: 'button',
    img: 'img',
    svg: 'svg',
    path: 'path'
  },
  AnimatePresence: ({ children }) => children,
  useAnimation: () => ({
    start: jest.fn(),
    stop: jest.fn(),
    set: jest.fn()
  })
}));

// Mock URL.createObjectURL
global.URL.createObjectURL = jest.fn(() => 'mocked-url');
global.URL.revokeObjectURL = jest.fn();

// Mock fetch
global.fetch = jest.fn();

// Suppress console warnings in tests
const originalError = console.error;
beforeAll(() => {
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      args[0].includes('Warning: ReactDOM.render is deprecated')
    ) {
      return;
    }
    originalError.call(console, ...args);
  };
});

afterAll(() => {
  console.error = originalError;
});

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
  localStorage.clear();
  sessionStorage.clear();
});
