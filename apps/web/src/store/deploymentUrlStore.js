import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import useSWR from 'swr';
import axios from 'axios';
import { useEffect } from 'react';

const fetcher = (url) =>
  axios
    .get(url, {
      headers: { 'Content-Type': 'application/json' },
      ...(process.env.NODE_ENV !== 'development' && {
        auth: {
          consumer_key: process.env.REACT_APP_NS_CONSUMER_KEY,
          consumer_secret: process.env.REACT_APP_NS_CONSUMER_SECRET,
          token: process.env.REACT_APP_NS_TOKEN_ID,
          token_secret: process.env.REACT_APP_NS_TOKEN_SECRET,
          signature_method: 'HMAC-SHA256',
          realm: process.env.REACT_APP_NS_ACCOUNT_ID,
          version: '1.0'
        }
      })
    })
    .then((res) => res.data);

const useDeploymentStore = create(
  persist(
    // eslint-disable-next-line no-unused-vars
    (set, get) => ({
      getDeploymentUrl: null,
      setDeploymentUrls: (urls) => set({ getDeploymentUrl: urls })
    }),
    {
      name: 'eh.deployments',
      partialize: (state) => ({ user: state.user }),
      storage: createJSONStorage(() => localStorage) // (optional) by default, 'localStorage' is used
    }
  )
);

const getHoistedUrls =
  process.env.NODE_ENV === 'development'
    ? process.env.REACT_APP_NS_HOISTED_URL_SUITELET
    : '/app/site/hosting/scriptlet.nl?script=customscript_ng_cs_sl_hoisted_app_urls&deploy=customdeploy_ng_cs_sl_hoisted_app_urls';

export const useDeploymentUrls = () => {
  const { getDeploymentUrl, setDeploymentUrls } = useDeploymentStore();
  const { data, error, mutate } = useSWR(getHoistedUrls, fetcher, {
    revalidateOnFocus: false,
    revalidateOnMount: true
  });

  useEffect(() => {
    if (data) {
      setDeploymentUrls(data);
    }
  }, [data]);

  return {
    getDeploymentUrl
  };
};
