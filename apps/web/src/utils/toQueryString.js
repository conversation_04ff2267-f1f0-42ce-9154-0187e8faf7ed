export const toQueryString = (baseUrl, paramsObject) => {
  const queryString = Object.entries(paramsObject)
    .map(
      ([key, value]) =>
        `${encodeURIComponent(key)}=${encodeURIComponent(value)}`
    )
    .join('&');

  // if the baseUrl already has params we just want to append new ones
  const appender = baseUrl.includes('?') ? '&' : '?';
  return `${baseUrl}${appender}${queryString}`;
};
