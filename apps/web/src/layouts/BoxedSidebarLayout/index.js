import { useContext, useLayoutEffect, useState } from 'react';
import { Box, Card, CardMedia, useTheme } from '@mui/material';
import { Outlet } from 'react-router-dom';
import { AnimatePresence, motion } from 'framer-motion';
import ThemeSettings from 'src/components/ThemeSettings';
import Sidebar from './Sidebar';
import Header from './Header';
import { SettingsContext } from '../../contexts/SettingsContext';
import useHoistedUrls from '../../hooks/useHoistedUrls';
import useAuth from '../../hooks/useAuth';

const BoxedSidebarLayout = () => {
  const theme = useTheme();
  const [settings] = useContext(SettingsContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const { user } = useAuth();
  const [coverLogoUrl, setCoverLogoUrl] = useState('');

  useLayoutEffect(() => {
    if (user && settings) {
      console.log('Cover photo setting...');
      if (user?.profile?.record?.fields?.custentity_ng_eh_customer_cover_url) {
        console.log(
          'Cover photo setting as...',
          user.profile.record.fields.custentity_ng_eh_customer_cover_url
        );
        setCoverLogoUrl(
          user.profile.record.fields.custentity_ng_eh_customer_cover_url
        );
      } else if (settings?.custrecord_ng_eh_fallback_cover_img_url) {
        console.log(
          'Cover photo setting as (settings)...',
          settings.custrecord_ng_eh_fallback_cover_img_url
        );
        setCoverLogoUrl(settings.custrecord_ng_eh_fallback_cover_img_url);
      } else {
        setCoverLogoUrl('');
      }
    }
  }, [settings, user]);

  return (
    <>
      <AnimatePresence>
        <Sidebar key="sidebar" />
        {coverLogoUrl && (
          <motion.div
            key={0}
            initial={{
              opacity: 0
            }}
            animate={{
              opacity: 1
            }}
            transition={{
              type: 'blur',
              stiffness: 260,
              damping: 20,
              duration: 3
            }}
            style={{
              position: 'fixed',
              width: '100%'
            }}
          >
            <div key={1}>
              <Card
                key={2}
                sx={{
                  borderRadius: 0,
                  height: '50vh',
                  WebkitMaskImage:
                    'linear-gradient(to bottom,black 3%,transparent 90%)',
                  maskImage:
                    'linear-gradient(to bottom,black 3%,transparent 90%)'
                }}
              >
                {coverLogoUrl && (
                  <CardMedia
                    key={3}
                    component="img"
                    height="100%"
                    sx={{
                      backdropFilter: 'blur(100px)'
                    }}
                    image={`${getDeploymentUrl.core_account_url}${coverLogoUrl}`}
                  />
                )}
              </Card>
            </div>
          </motion.div>
        )}
        <Box
          key="layout-container"
          sx={{
            position: 'relative',
            zIndex: 5,
            flex: 1,
            display: 'flex',

            '.MuiDrawer-pw': {
              '& .MuiDrawer-paper': {
                width: `calc(400px + ${theme.spacing(3)})`,
                background: 'none',
                border: 0,
                pl: 0
              }
            },

            '.MuiDrawer-hd': {
              '& .MuiDrawer-paper': {
                background: 'none',
                border: 0,
                width: `calc(360px + ${theme.spacing(4)})`,
                pl: 0
              }
            },

            '.MuiDrawer-fm': {
              '& .MuiDrawer-paper': {
                borderRadius: theme.general.borderRadius,
                width: `calc(400px - ${theme.spacing(3)})`,
                height: `calc(100% - 80px - ${theme.spacing(6)})`,
                m: 3
              }
            },

            '.Mui-FixedWrapper': {
              height: `calc(100vh - ${theme.spacing(17)})`,
              minHeight: `calc(100vh - ${theme.spacing(17)})`,
              margin: theme.spacing(4),
              background: theme.colors.alpha.white[100],
              borderRadius: theme.general.borderRadius,
              overflow: 'hidden',
              border: `${theme.colors.alpha.black[30]} solid 1px`,

              '.Mui-FixedWrapperContent': {
                overflow: 'auto',
                height: `calc(100vh - ${theme.spacing(17.5)})`
              },

              '.MuiDrawer-root.MuiDrawer-docked': {
                position: 'relative',

                '.MuiPaper-root': {
                  height: `calc(100vh - ${theme.spacing(17)})`,
                  minHeight: `calc(100vh - ${theme.spacing(17)})`,
                  position: 'absolute',
                  top: 0,
                  left: 0
                }
              }
            },

            '.footer-wrapper': {
              margin: 0,
              background: 'transparent',
              boxShadow: 'none'
            },

            '.MuiPageTitle-wrapper': {
              pt: theme.spacing(3),
              pb: theme.spacing(6)
            }
          }}
        >
          <Box
            key="layout-container-sub"
            sx={{
              display: 'flex',
              flex: 1,
              flexDirection: 'column',
              width: `calc(100% - ${theme.sidebar.width} - ${theme.spacing(
                4
              )})`,
              [theme.breakpoints.up('lg')]: {
                ml: `calc(${theme.sidebar.width} + ${theme.spacing(4)})`
              }
            }}
          >
            <Box flexGrow={1} key="layout-container-sub-0">
              <Box>
                <Header />
                <Outlet />
              </Box>
            </Box>
            <ThemeSettings key="layout-container-sub-1" />
          </Box>
        </Box>
      </AnimatePresence>
    </>
  );
};

export default BoxedSidebarLayout;
