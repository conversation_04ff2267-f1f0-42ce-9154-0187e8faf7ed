import AnalyticsTwoToneIcon from '@mui/icons-material/AnalyticsTwoTone';
import AccountTreeTwoToneIcon from '@mui/icons-material/AccountTreeTwoTone';
import StorefrontTwoToneIcon from '@mui/icons-material/StorefrontTwoTone';
import ChatTwoToneIcon from '@mui/icons-material/ChatTwoTone';
import { useEffect, useState } from 'react';
import useAuth from '../../../../hooks/useAuth';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';

const useProdMenuItems = () => {
  let [menuItems, setMenuItems] = useState([
    {
      heading: '',
      items: [
        {
          name: 'Dashboard',
          icon: AnalyticsTwoToneIcon,
          link: 'dashboards/exhibit-tasks'
        },
        {
          name: 'Projects',
          icon: AccountTreeTwoToneIcon,
          link: 'management/projects/list'
        },
        {
          name: 'Discussions',
          icon: ChatTwoToneIcon,
          link: 'discussions'
        },
        {
          name: 'Calendar',
          icon: CalendarMonthIcon,
          link: 'events/calendar'
        },
        {
          name: 'Inventory',
          icon: StorefrontTwoToneIcon,
          link: 'inventory',
          items: [
            {
              name: 'Customer Owned Inventory',
              link: 'inventory/nonrentals'
            },
            {
              name: 'Rentals',
              link: 'inventory/rentals'
            }
          ]
        }
      ]
    },
    {
      heading: '',
      items: []
    }
  ]);
  const { user, checkoutConfig } = useAuth();

  useEffect(() => {
    if (user && checkoutConfig) {
      let conditionalRendering = [
        {
          name: 'Customer Owned Inventory',
          link: 'inventory/nonrentals'
        },
        {
          name: 'Rentals',
          link: 'inventory/rentals'
        }
      ];

      let menuOptions = [
        {
          heading: '',
          items: [
            {
              name: 'Dashboard',
              icon: AnalyticsTwoToneIcon,
              link: 'dashboards/exhibit-tasks'
            },
            {
              name: 'Projects',
              icon: AccountTreeTwoToneIcon,
              link: 'management/projects/list'
            },
            {
              name: 'Calendar',
              icon: CalendarMonthIcon,
              link: 'events/calendar'
            },
            {
              name: 'Discussions',
              icon: ChatTwoToneIcon,
              link: 'discussions'
            },
            {
              name: 'Inventory',
              icon: StorefrontTwoToneIcon,
              link: 'inventory',
              items: conditionalRendering
            }
          ]
        },
        {
          heading: '',
          items: []
        }
      ];

      let rentalsOption = conditionalRendering.findIndex(
        (op) => op.name === 'Rentals'
      );
      let invOption = conditionalRendering.findIndex(
        (op) => op.name === 'Customer Owned Inventory'
      );

      if (!checkoutConfig.rentals) {
        conditionalRendering.splice(rentalsOption, 1);
      }

      if (!checkoutConfig.inventory) {
        conditionalRendering.splice(invOption, 1);
      }

      if (conditionalRendering.length === 0) {
        let inventoryIndex = menuOptions[0].items.findIndex(
          (item) => item.name === 'Inventory'
        );
        // Remove the accordion from the list if both options are disabled
        menuOptions[0].items.splice(inventoryIndex, 1);
      }

      setMenuItems(menuOptions);
    }
  }, [checkoutConfig]);

  return menuItems;
};

export default useProdMenuItems;
