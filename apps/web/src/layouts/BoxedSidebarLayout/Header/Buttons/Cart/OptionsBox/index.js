import { useContext, useRef, useState } from 'react';
import useAuth from 'src/hooks/useAuth';
import { NavLink, useLocation, useNavigate } from 'react-router-dom';

import {
  Box,
  Button,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Popover,
  styled,
  Typography
} from '@mui/material';
import { useTranslation } from 'react-i18next';
import LockOpenTwoToneIcon from '@mui/icons-material/LockOpenTwoTone';
import AccountTreeTwoToneIcon from '@mui/icons-material/AccountTreeTwoTone';
import MoreVertTwoToneIcon from '@mui/icons-material/MoreVertTwoTone';
import {
  CloseTwoTone,
  EjectTwoTone,
  RemoveShoppingCartTwoTone
} from '@mui/icons-material';
import { CartContext } from '../../../../../../contexts/CartContext';

function HeaderCartOptionsBox({ toggleCartView }) {
  const { t } = useTranslation();
  const location = useLocation();
  const navigate = useNavigate();

  // eslint-disable-next-line no-unused-vars
  const { user, initialUserData, logout, setProject } = useAuth();
  const { setCart } = useContext(CartContext);
  const ref = useRef(null);
  const [isOpen, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleLogout = async () => {
    try {
      handleClose();
      await logout();
      navigate('/');
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <>
      {/* Only show developer testing tools in local or tstdrv accounts */}
      <Box sx={{ display: 'inline-flex' }} flexDirection="row">
        <Box item xs={6}>
          {process.env.NODE_ENV === 'development' ? (
            <IconButton
              ref={ref}
              onClick={handleOpen}
              size="small"
              color="secondary"
            >
              <MoreVertTwoToneIcon />
            </IconButton>
          ) : (
            window.location.origin.includes('tstdrv') && (
              <IconButton
                ref={ref}
                onClick={handleOpen}
                size="small"
                color="secondary"
              >
                <MoreVertTwoToneIcon />
              </IconButton>
            )
          )}
        </Box>
        <Box>
          <IconButton
            ref={ref}
            onClick={() => toggleCartView(false)}
            size="small"
            color="secondary"
          >
            <CloseTwoTone />
          </IconButton>
        </Box>
      </Box>
      <Popover
        disableScrollLock
        anchorEl={ref.current}
        onClose={handleClose}
        open={isOpen}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <Divider
          sx={{
            mb: 0
          }}
        />
        <List
          sx={{
            p: 1
          }}
          component="nav"
        >
          <ListItem
            onClick={() => {
              handleClose();
              setCart([]);
            }}
            button
          >
            <RemoveShoppingCartTwoTone fontSize="small" />
            <ListItemText primary={t('Clear Cart')} />
          </ListItem>
          <ListItem
            onClick={() => {
              handleClose();
              setProject(null);
            }}
            button
          >
            <EjectTwoTone fontSize="small" />
            <ListItemText primary={t('Eject Project')} />
          </ListItem>
          <ListItem
            onClick={() => {
              handleClose();
            }}
            button
            to="/management/projects/list"
            component={NavLink}
          >
            <AccountTreeTwoToneIcon fontSize="small" />
            <ListItemText primary={t('Projects')} />
          </ListItem>
        </List>
        <Divider />
        <Box m={1}>
          <Button color="primary" fullWidth onClick={handleLogout}>
            <LockOpenTwoToneIcon
              sx={{
                mr: 1
              }}
            />
            {t('Sign out')}
          </Button>
        </Box>
      </Popover>
    </>
  );
}

export default HeaderCartOptionsBox;
