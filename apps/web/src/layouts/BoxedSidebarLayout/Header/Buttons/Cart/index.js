import {
  alpha,
  Ava<PERSON>,
  Badge,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Card<PERSON><PERSON>er,
  darken,
  Divider,
  IconButton,
  lighten,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Popover,
  styled,
  Tooltip,
  Typography,
  useTheme
} from '@mui/material';
import { Fragment, useContext, useEffect, useRef, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import Link from '@mui/material/Link';
import { useTranslation } from 'react-i18next';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import ClearTwoToneIcon from '@mui/icons-material/ClearTwoTone';
import {
  ErrorTwoTone,
  RemoveTwoTone,
  ShoppingCartTwoTone
} from '@mui/icons-material';
import Scrollbar from '../../../../../components/Scrollbar';
import Label from '../../../../../components/Label';
import { CartContext } from '../../../../../contexts/CartContext';
import useHoistedUrls from '../../../../../hooks/useHoistedUrls';
import HeaderCartOptionsBox from './OptionsBox';
import useAuth from '../../../../../hooks/useAuth';
import { SettingsContext } from '../../../../../contexts/SettingsContext';

const AnimatedBadge = styled(Badge)(
  ({ theme }) => `
    
    .MuiBadge-badge {
        box-shadow: 0 0 0 2px ${theme.palette.background.paper};
        background-color: #44b700;
        color: #44b700;
        
        &::after {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            animation: ripple 1.2s infinite ease-in-out;
            border: 1px solid currentColor;
            content: "";
        }
    }
`
);

const AvatarWarning = styled(Avatar)(
  ({ theme }) => `
          background-color: ${theme.colors.warning.lighter};
          color: ${theme.colors.warning.main};
          width: ${theme.spacing(8)};
          height: ${theme.spacing(8)};
          margin-left: auto;
          margin-right: auto;
    `
);

const CartBadge = styled(Badge)(
  ({ theme }) => `
    
    .MuiBadge-badge {
        background-color: ${alpha(theme.palette.primary.main, 0.4)};
        color: ${
          theme.palette.mode === 'dark'
            ? lighten(theme.palette.primary.main, 0.7)
            : darken(theme.palette.primary.main, 0.7)
        };
        min-width: 16px; 
        height: 16px;
        padding: 0;

        &::after {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: 0 0 0 1px ${alpha(theme.palette.primary.main, 0.7)};
            content: "";
        }
    }
`
);

const CardWrapper = styled(Card)(
  ({ theme }) => `
      background: ${alpha(theme.colors.alpha.black[10], 0.05)};
      border-radius: 0;
  `
);

const LinkHover = styled('a')(
  ({ theme }) => `
    transition: ${theme.transitions.create([
      'transform',
      'opacity',
      'box-shadow'
    ])};
    transform: translateY(0px);
    display: block;
    opacity: 1;

    &:hover {
        opacity: .9;
        transform: translateY(-4px);
    }
  `
);

const IconButtonWrapper = styled(IconButton)(
  ({ theme }) => `
    transition: ${theme.transitions.create(['transform', 'background'])};
    transform: scale(1);
    transform-origin: center;

    &:hover {
        transform: scale(1.1);
    }
  `
);

const ListWrapper = styled(List)(
  () => `
      .MuiListItem-root:last-of-type + .MuiDivider-root {
          display: none;
      }
  `
);

const IconButtonTrayWrapper = styled(IconButton)(
  ({ theme }) => `
        width: ${theme.spacing(6)};
        height: ${theme.spacing(6)};
        background-color: ${
          theme.palette.mode === 'dark'
            ? alpha(theme.palette.background.default, 0.7)
            : theme.colors.alpha.white[70]
        };
      :hover {
            background-color: ${
              theme.palette.mode === 'dark'
                ? alpha(theme.palette.background.default, 0.5)
                : theme.colors.alpha.white[50]
            };
      }
`
);

function HeaderCart() {
  const ref = useRef(null);
  const { currentProject } = useAuth();
  const cartOptions = useRef(null);
  const [isOpen, setOpen] = useState(false);
  const { cart, removeCartItem, setItemQuantity } = useContext(CartContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useContext(SettingsContext);
  const [totalQuantity, setTotalQuantity] = useState(0);
  const theme = useTheme();

  useEffect(() => {
    if (cart.length !== 0) {
      let sum = 0;
      cart.forEach((item) => {
        sum += item.quantity;
      });
      setTotalQuantity(sum);
    } else {
      setTotalQuantity(0);
    }
  }, [cart]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const { t } = useTranslation();

  const items = [
    {
      id: 1,
      title: 'Amazon Dot Echo 3',
      image: '/static/images/placeholders/products/1.png',
      price: '79'
    },
    {
      id: 2,
      title: 'Autodesk 3D Printer PRO',
      image: '/static/images/placeholders/products/2.png',
      price: '399'
    },
    {
      id: 3,
      title: 'Apple iPhone 12 PRO',
      image: '/static/images/placeholders/products/3.png',
      price: '749'
    },
    {
      id: 4,
      title: 'GoPro Action Camera 3',
      image: '/static/images/placeholders/products/4.png',
      price: '289'
    },
    {
      id: 5,
      title: 'Apple Watch 42mm Gen. 4',
      image: '/static/images/placeholders/products/5.png',
      price: '199'
    }
  ];

  const getImageFileAttachment = (url) => {
    if (url) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  return (
    <>
      <Tooltip arrow title={t('My Cart')}>
        <IconButtonTrayWrapper color="secondary" ref={ref} onClick={handleOpen}>
          <CartBadge
            badgeContent={cart.length}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right'
            }}
          >
            <ShoppingCartTwoTone />
          </CartBadge>
        </IconButtonTrayWrapper>
      </Tooltip>
      <Popover
        disableScrollLock
        anchorEl={ref.current}
        onClose={handleClose}
        open={isOpen}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <Card
          sx={{
            minWidth: 486
          }}
        >
          <CardHeader
            sx={{
              p: 2
            }}
            disableTypography
            action={<HeaderCartOptionsBox toggleCartView={(e) => setOpen(e)} />}
            title={
              <>
                <Typography
                  sx={{
                    fontSize: `${theme.typography.pxToRem(17)}`
                  }}
                  gutterBottom
                  variant="h3"
                  textAlign="center"
                >
                  {t('Shopping Cart')}
                </Typography>
              </>
            }
            subheader={
              <>
                {currentProject ? (
                  <Typography variant="subtitle2" textAlign="center">
                    {t('Checkout is almost done')}
                  </Typography>
                ) : (
                  <Typography variant="subtitle2" textAlign="center">
                    {t('No project has been set')}
                  </Typography>
                )}
              </>
            }
          />
          <Divider />
          <Box
            sx={{
              height: 303
            }}
          >
            <Scrollbar>
              <ListWrapper disablePadding>
                {cart.map((item) => (
                  <Fragment key={item.id}>
                    <ListItem
                      sx={{
                        display: { md: 'flex' },
                        py: 1.5,
                        px: 2
                      }}
                    >
                      <ListItemAvatar
                        sx={{
                          mr: 2,
                          mb: { xs: 1, md: 0 }
                        }}
                      >
                        <LinkHover href="#">
                          <Avatar
                            variant="rounded"
                            sx={{
                              width: 100,
                              height: 'auto'
                            }}
                            alt={item.displayname}
                            src={getImageFileAttachment(
                              item.custitem_ng_eh_item_primary_image
                            )}
                          />
                        </LinkHover>
                      </ListItemAvatar>
                      <ListItemText
                        disableTypography
                        primary={
                          <Link
                            sx={{
                              display: 'block',
                              mb: 1
                            }}
                            noWrap
                            color="text.primary"
                            variant="h4"
                            href="#"
                          >
                            {item.displayname}
                          </Link>
                        }
                        secondary={
                          <>
                            <Label color="primary">
                              Quantity: {item.quantity}
                            </Label>
                            <Tooltip title="Remove Item">
                              <IconButtonWrapper
                                sx={{
                                  ml: 1,
                                  backgroundColor: `${theme.colors.error.lighter}`,
                                  color: `${theme.colors.error.main}`,
                                  transition: `${theme.transitions.create([
                                    'all'
                                  ])}`,

                                  '&:hover': {
                                    backgroundColor: `${theme.colors.error.main}`,
                                    color: `${theme.palette.getContrastText(
                                      theme.colors.error.main
                                    )}`
                                  }
                                }}
                                size="small"
                                onClick={() => removeCartItem(item.id)}
                              >
                                <ClearTwoToneIcon fontSize="small" />
                              </IconButtonWrapper>
                            </Tooltip>
                          </>
                        }
                      />
                      <Box
                        component="span"
                        sx={{
                          display: { xs: 'flex', md: 'inline-block' }
                        }}
                      >
                        <Box ml={3} textAlign="right">
                          <IconButtonWrapper
                            sx={{
                              backgroundColor: `${theme.colors.primary.main}`,
                              color: `${theme.palette.getContrastText(
                                theme.colors.primary.main
                              )}`,
                              transition: `${theme.transitions.create([
                                'all'
                              ])}`,

                              '&:hover': {
                                backgroundColor: `${theme.colors.primary.main}`,
                                color: `${theme.palette.getContrastText(
                                  theme.colors.primary.main
                                )}`
                              }
                            }}
                            size="small"
                            onClick={() =>
                              setItemQuantity(item, item.quantity + 1)
                            }
                          >
                            <AddTwoToneIcon fontSize="small" />
                          </IconButtonWrapper>
                          <IconButtonWrapper
                            sx={{
                              backgroundColor: `${theme.colors.primary.main}`,
                              color: `${theme.palette.getContrastText(
                                theme.colors.primary.main
                              )}`,
                              transition: `${theme.transitions.create([
                                'all'
                              ])}`,
                              '&:hover': {
                                backgroundColor: `${theme.colors.primary.main}`,
                                color: `${theme.palette.getContrastText(
                                  theme.colors.primary.main
                                )}`
                              }
                            }}
                            size="small"
                            onClick={() =>
                              item.quantity <= 1
                                ? removeCartItem(item.id)
                                : setItemQuantity(item, item.quantity - 1)
                            }
                          >
                            <RemoveTwoTone fontSize="small" />
                          </IconButtonWrapper>
                        </Box>
                      </Box>
                    </ListItem>
                    <Divider />
                  </Fragment>
                ))}
                {!currentProject && (
                  <Box pb={3}>
                    <Divider
                      sx={{
                        mb: 3
                      }}
                    />
                    <AvatarWarning>
                      <ErrorTwoTone />
                    </AvatarWarning>
                    <Typography
                      sx={{
                        mt: 2,
                        textAlign: 'center'
                      }}
                      variant="subtitle2"
                    >
                      {t('Select a project to begin shopping!')}
                    </Typography>
                  </Box>
                )}
              </ListWrapper>
            </Scrollbar>
          </Box>
          <Divider />
          <CardWrapper
            elevation={0}
            sx={{
              textAlign: 'right',
              p: 2
            }}
          >
            <Typography variant="caption" fontWeight="bold">
              {t('Total Items')}
              <Typography
                sx={{
                  pl: 1
                }}
                component="span"
                variant="h4"
                color="text.primary"
              >
                <b>{totalQuantity}</b>
              </Typography>
            </Typography>
          </CardWrapper>
          <Divider />
          <Box
            p={2}
            sx={{
              textAlign: 'center'
            }}
          >
            <Button
              component={RouterLink}
              onClick={
                !currentProject || cart.length === 0 ? null : handleClose
              }
              to="/checkout"
              disabled={!currentProject || cart.length === 0}
              variant="contained"
              fullWidth
              sx={{
                py: 2,
                fontSize: `${theme.typography.pxToRem(12)}`,
                textTransform: 'uppercase'
              }}
            >
              {t('Proceed to checkout')}
            </Button>
          </Box>
        </Card>
      </Popover>
    </>
  );
}

export default HeaderCart;
