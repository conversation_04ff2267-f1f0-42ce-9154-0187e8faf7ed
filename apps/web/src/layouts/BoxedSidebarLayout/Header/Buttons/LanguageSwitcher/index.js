import { useRef, useState } from 'react';

import {
  Box,
  Divider,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Popover,
  styled,
  Tooltip,
  Typography
} from '@mui/material';
import Text from 'src/components/Text';

import WarningTwoToneIcon from '@mui/icons-material/WarningTwoTone';
import internationalization from 'src/i18n/i18n';
import { useTranslation } from 'react-i18next';

const deFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 85.333 512 341.333">
    <path fill="#D80027" d="M0 85.331h512v341.337H0z" />
    <path d="M0 85.331h512v113.775H0z" />
    <path fill="#FFDA44" d="M0 312.882h512v113.775H0z" />
  </svg>
);
const usFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 513 342">
    <path fill="#FFF" d="M0 0h513v342H0z" />
    <g fill="#D80027">
      <path d="M0 0h513v26.3H0zM0 52.6h513v26.3H0zM0 105.2h513v26.3H0zM0 157.8h513v26.3H0zM0 210.5h513v26.3H0zM0 263.1h513v26.3H0zM0 315.7h513V342H0z" />
    </g>
    <path fill="#2E52B2" d="M0 0h256.5v184.1H0z" />
    <g fill="#FFF">
      <path d="m47.8 138.9-4-12.8-4.4 12.8H26.2l10.7 7.7-4 12.8 10.9-7.9 10.6 7.9-4.1-12.8 10.9-7.7zM104.1 138.9l-4.1-12.8-4.2 12.8H82.6l10.7 7.7-4 12.8 10.7-7.9 10.8 7.9-4-12.8 10.7-7.7zM160.6 138.9l-4.3-12.8-4 12.8h-13.5l11 7.7-4.2 12.8 10.7-7.9 11 7.9-4.2-12.8 10.7-7.7zM216.8 138.9l-4-12.8-4.2 12.8h-13.3l10.8 7.7-4 12.8 10.7-7.9 10.8 7.9-4.3-12.8 11-7.7zM100 75.3l-4.2 12.8H82.6L93.3 96l-4 12.6 10.7-7.8 10.8 7.8-4-12.6 10.7-7.9h-13.4zM43.8 75.3l-4.4 12.8H26.2L36.9 96l-4 12.6 10.9-7.8 10.6 7.8L50.3 96l10.9-7.9H47.8zM156.3 75.3l-4 12.8h-13.5l11 7.9-4.2 12.6 10.7-7.8 11 7.8-4.2-12.6 10.7-7.9h-13.2zM212.8 75.3l-4.2 12.8h-13.3l10.8 7.9-4 12.6 10.7-7.8 10.8 7.8-4.3-12.6 11-7.9h-13.5zM43.8 24.7l-4.4 12.6H26.2l10.7 7.9-4 12.7L43.8 50l10.6 7.9-4.1-12.7 10.9-7.9H47.8zM100 24.7l-4.2 12.6H82.6l10.7 7.9-4 12.7L100 50l10.8 7.9-4-12.7 10.7-7.9h-13.4zM156.3 24.7l-4 12.6h-13.5l11 7.9-4.2 12.7 10.7-7.9 11 7.9-4.2-12.7 10.7-7.9h-13.2zM212.8 24.7l-4.2 12.6h-13.3l10.8 7.9-4 12.7 10.7-7.9 10.8 7.9-4.3-12.7 11-7.9h-13.5z" />
    </g>
  </svg>
);
const esFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 85.333 512 341.333">
    <path fill="#FFDA44" d="M0 85.331h512v341.337H0z" />
    <g fill="#D80027">
      <path d="M0 85.331h512v113.775H0zM0 312.882h512v113.775H0z" />
    </g>
  </svg>
);
const frFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 85.333 512 341.333">
    <path fill="#FFF" d="M0 85.331h512v341.337H0z" />
    <path fill="#0052B4" d="M0 85.331h170.663v341.337H0z" />
    <path fill="#D80027" d="M341.337 85.331H512v341.337H341.337z" />
  </svg>
);
const cnFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 513 342">
    <path fill="#D80027" d="M0 0h513v342H0z" />
    <g fill="#FFDA44">
      <path d="m226.8 239.2-9.7-15.6-17.9 4.4 11.9-14.1-9.7-15.6 17.1 6.9 11.8-14.1-1.3 18.4 17.1 6.9-17.9 4.4zM290.6 82l-10.1 15.4 11.6 14.3-17.7-4.8-10.1 15.5-1-18.4-17.7-4.8 17.2-6.6-1-18.4 11.6 14.3zM236.2 25.4l-2 18.3 16.8 7.6-18 3.8-2 18.3-9.2-16-17.9 3.8 12.3-13.7-9.2-15.9 16.8 7.5zM292.8 161.8l-14.9 10.9 5.8 17.5-14.9-10.8-14.9 11 5.6-17.6-14.9-10.7 18.4-.1 5.6-17.6 5.8 17.5zM115 46.3l17.3 53.5h56.2l-45.4 32.9 17.3 53.5-45.4-33-45.5 33 17.4-53.5-45.5-32.9h56.3z" />
    </g>
  </svg>
);
const aeFlag = (
  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 513 342">
    <path fill="#FFF" d="M0 0h513v342H0z" />
    <path fill="#009e49" d="M0 0h513v114H0z" />
    <path d="M0 228h513v114H0z" />
    <path fill="#ce1126" d="M0 0h171v342H0z" />
  </svg>
);

const SectionHeading = styled(Typography)(
  ({ theme }) => `
        font-weight: ${theme.typography.fontWeightBold};
        color: ${theme.palette.secondary.main};
        display: block;
        padding: ${theme.spacing(2, 2, 0)};
`
);
const ImageWrapper = styled('div')(
  () => `
        width: 30px;
        margin: 3px;
`
);

const IconButtonWrapper = styled(IconButton)(
  ({ theme }) => `
        width: ${theme.spacing(6)};
        height: ${theme.spacing(6)};
`
);

function LanguageSwitcher() {
  const { i18n } = useTranslation();
  const { t } = useTranslation();
  const getLanguage = i18n.language;

  const switchLanguage = ({ lng }) => {
    internationalization.changeLanguage(lng);
  };
  const ref = useRef(null);
  const [isOpen, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <Tooltip arrow title={t('Language Switcher')}>
        {/* TODO: Add multi-language support */}
        {/* <IconButtonWrapper color="secondary" ref={ref} onClick={handleOpen}> */}
        <IconButtonWrapper color="secondary" ref={ref}>
          {getLanguage === 'de' && (
            <ImageWrapper alt="German">{deFlag}</ImageWrapper>
          )}
          {getLanguage === 'en' && (
            <ImageWrapper alt="English">{usFlag}</ImageWrapper>
          )}
          {getLanguage === 'en-US' && (
            <ImageWrapper alt="English">{usFlag}</ImageWrapper>
          )}
          {getLanguage === 'en-GB' && (
            <ImageWrapper alt="English">{usFlag}</ImageWrapper>
          )}
          {getLanguage === 'es' && (
            <ImageWrapper alt="Spanish">{esFlag}</ImageWrapper>
          )}
          {getLanguage === 'fr' && (
            <ImageWrapper alt="French">{frFlag}</ImageWrapper>
          )}
          {getLanguage === 'cn' && (
            <ImageWrapper alt="Chinese">{cnFlag}</ImageWrapper>
          )}
          {getLanguage === 'ae' && (
            <ImageWrapper alt="Arabic">{aeFlag}</ImageWrapper>
          )}
        </IconButtonWrapper>
      </Tooltip>
      <Popover
        disableScrollLock
        anchorEl={ref.current}
        onClose={handleClose}
        open={isOpen}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <Box
          sx={{
            maxWidth: 240
          }}
        >
          <SectionHeading variant="body2" color="text.primary">
            {t('Language Switcher')}
          </SectionHeading>
          <List
            sx={{
              p: 2
            }}
            component="nav"
          >
            <ListItem
              className={
                getLanguage === 'en' || getLanguage === 'en-US' ? 'active' : ''
              }
              button
              onClick={() => {
                switchLanguage({ lng: 'en' });
                handleClose();
              }}
            >
              <ImageWrapper alt="English" src={usFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="English"
              />
            </ListItem>
            <ListItem
              className={getLanguage === 'de' ? 'active' : ''}
              button
              onClick={() => {
                switchLanguage({ lng: 'de' });
                handleClose();
              }}
            >
              <ImageWrapper alt="German" src={deFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="German"
              />
            </ListItem>
            <ListItem
              className={getLanguage === 'es' ? 'active' : ''}
              button
              onClick={() => {
                switchLanguage({ lng: 'es' });
                handleClose();
              }}
            >
              <ImageWrapper alt="Spanish" src={esFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="Spanish"
              />
            </ListItem>
            <ListItem
              className={getLanguage === 'fr' ? 'active' : ''}
              button
              onClick={() => {
                switchLanguage({ lng: 'fr' });
                handleClose();
              }}
            >
              <ImageWrapper alt="French" src={frFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="French"
              />
            </ListItem>
            <ListItem
              className={getLanguage === 'cn' ? 'active' : ''}
              button
              onClick={() => {
                switchLanguage({ lng: 'cn' });
                handleClose();
              }}
            >
              <ImageWrapper alt="Chinese" src={cnFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="Chinese"
              />
            </ListItem>
            <ListItem
              className={getLanguage === 'ae' ? 'active' : ''}
              button
              onClick={() => {
                switchLanguage({ lng: 'ae' });
                handleClose();
              }}
            >
              <ImageWrapper alt="Arabic" src={aeFlag} />
              <ListItemText
                sx={{
                  pl: 1
                }}
                primary="Arabic"
              />
            </ListItem>
          </List>
          <Divider />
          <Text color="warning">
            <Box
              p={2}
              sx={{
                maxWidth: 340
              }}
            >
              <WarningTwoToneIcon fontSize="small" />
              <Typography variant="body1">
                {t(
                  'We only translated a small part of the template, for demonstration purposes'
                )}
                !
              </Typography>
            </Box>
          </Text>
        </Box>
      </Popover>
    </>
  );
}

export default LanguageSwitcher;
