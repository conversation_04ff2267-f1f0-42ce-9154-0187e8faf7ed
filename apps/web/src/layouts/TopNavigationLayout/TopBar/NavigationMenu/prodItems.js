import AnalyticsTwoToneIcon from '@mui/icons-material/AnalyticsTwoTone';
import AccountTreeTwoToneIcon from '@mui/icons-material/AccountTreeTwoTone';
import StorefrontTwoToneIcon from '@mui/icons-material/StorefrontTwoTone';
import ChatTwoToneIcon from '@mui/icons-material/ChatTwoTone';
import CalendarMonthIcon from '@mui/icons-material/CalendarMonth';
import { useEffect, useState } from 'react';
import useAuth from '../../../../hooks/useAuth';
import MenuTwoToneIcon from '@mui/icons-material/MenuTwoTone';
import DescriptionTwoToneIcon from '@mui/icons-material/DescriptionTwoTone';
import CasesTwoToneIcon from '@mui/icons-material/CasesTwoTone';
import useSettings from '../../../../hooks/useSettings';

const useProdMenuItems = () => {
  let [menuItems, setMenuItems] = useState([
    {
      heading: '',
      items: [
        {
          name: 'Dashboard',
          icon: AnalyticsTwoToneIcon,
          link: 'dashboards/exhibit-tasks',
          badge: ''
        },
        {
          name: 'Projects',
          icon: AccountTreeTwoToneIcon,
          link: 'management/projects/list',
          badge: ''
        },
        {
          name: 'Calendar',
          icon: CalendarMonthIcon,
          link: 'events/calendar'
        },
        {
          name: 'Inventory',
          icon: StorefrontTwoToneIcon,
          link: '',
          badge: '',
          items: [
            {
              name: 'Customer Owned Inventory',
              link: 'inventory/nonrentals'
            },
            {
              name: 'Rentals',
              link: 'inventory/rentals'
            }
          ]
        },
        {
          name: 'Reports',
          icon: DescriptionTwoToneIcon,
          link: 'management/projects/list',
          badge: ''
        },
        {
          name: '',
          icon: MenuTwoToneIcon,
          link: '',
          items: []
        }
      ]
    }
  ]);
  const { user, checkoutConfig } = useAuth();
  const [settings] = useSettings();

  useEffect(() => {
    if (settings?.custrecord_enable_cases_in_portal === 'T') {
      let menuOptions = [...menuItems];
      let caseIndex = menuOptions[0].items.findIndex(
        (item) => item.name === 'Discussions'
      );
      if (caseIndex === -1) {
        menuOptions[0].items.splice(3, 0, {
          name: 'Cases',
          icon: CasesTwoToneIcon,
          link: 'discussions'
        });
      }
      setMenuItems(menuOptions);
    }
  }, [settings]);

  useEffect(() => {
    if (user && checkoutConfig) {
      let conditionalRendering = [
        {
          name: 'Customer Owned Inventory',
          link: 'inventory/nonrentals'
        },
        {
          name: 'Rentals',
          link: 'inventory/rentals'
        }
      ];

      let menuOptions = [
        {
          heading: '',
          items: [
            {
              name: 'Dashboard',
              icon: AnalyticsTwoToneIcon,
              link: 'dashboards/exhibit-tasks'
            },
            {
              name: 'Projects',
              icon: AccountTreeTwoToneIcon,
              link: 'management/projects/list'
            },
            {
              name: 'Calendar',
              icon: CalendarMonthIcon,
              link: 'events/calendar'
            },
            {
              name: 'Inventory',
              icon: StorefrontTwoToneIcon,
              link: 'inventory',
              items: conditionalRendering
            },
            {
              name: 'Reports',
              icon: DescriptionTwoToneIcon,
              link: 'management/reports',
              badge: ''
            },
            {
              name: '',
              icon: null,
              link: '',
              items: []
            }
          ]
        }
      ];

      let rentalsOption = conditionalRendering.findIndex(
        (op) => op.name === 'Rentals'
      );
      let invOption = conditionalRendering.findIndex(
        (op) => op.name === 'Customer Owned Inventory'
      );

      if (!checkoutConfig.rentals) {
        conditionalRendering.splice(rentalsOption, 1);
      }

      if (!checkoutConfig.inventory) {
        conditionalRendering.splice(invOption, 1);
      }

      if (conditionalRendering.length === 0) {
        let inventoryIndex = menuOptions[0].items.findIndex(
          (item) => item.name === 'Inventory'
        );
        // Remove the accordion from the list if both options are disabled
        menuOptions[0].items.splice(inventoryIndex, 1);
      }

      setMenuItems(menuOptions);
    }
  }, [checkoutConfig]);

  return menuItems;
};

export default useProdMenuItems;
