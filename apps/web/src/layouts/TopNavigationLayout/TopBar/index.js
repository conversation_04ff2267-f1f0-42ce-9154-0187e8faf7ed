import { useContext, useLayoutEffect, useState } from 'react';

import {
  alpha,
  Box,
  Card,
  Container,
  darken,
  Divider,
  IconButton,
  styled,
  Tooltip
} from '@mui/material';

import { useTheme } from '@mui/material/styles';
import MenuTwoToneIcon from '@mui/icons-material/MenuTwoTone';
import CloseTwoToneIcon from '@mui/icons-material/CloseTwoTone';
import { SidebarContext } from 'src/contexts/SidebarContext';
import NavigationMenu from './NavigationMenu';
import Userbox from './Userbox.old';
import HeaderCart from './Cart';
import { SettingsContext } from '../../../contexts/SettingsContext';
import useAuth from '../../../hooks/useAuth';
import LogoCustomerNav from '../../../components/LogoCustomerNav';
import useHoistedUrls from '../../../hooks/useHoistedUrls';

const TopBarWrapper = styled(Card)(
  ({ theme }) => `
    color: ${theme.header.textColor};
    background: ${alpha(darken(theme.colors.primary.dark, 0.2), 0.95)};
    backdrop-filter: blur(5px);
    margin: ${theme.spacing(0, 0, 5)};
    padding: ${theme.spacing(4, 0, 44)};

    @media (min-width: ${theme.breakpoints.values.lg}px) {
      margin: ${theme.spacing(0, 8, 5)};
      padding: ${theme.spacing(4, 3, 44)};
    }
    display: flex;
    align-items: center;
    border-radius: 0;
    border-bottom-right-radius: 40px;
    border-bottom-left-radius: 40px;
    position: relative;
    -webkit-mask-image: linear-gradient(to bottom, black 25%,transparent 99%);
    mask-image: linear-gradient(to bottom,black 25%,transparent 99%);
`
);

const TopBarImage = styled(Box)(
  () => `
    background-size: cover;
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: .7;
`
);

const DividerWrapper = styled(Divider)(
  ({ theme }) => `
    background: ${theme.colors.alpha.trueWhite[10]};
`
);

const IconButtonPrimary = styled(IconButton)(
  ({ theme }) => `
    display: flex;
    width: 48px;
    margin-left: ${theme.spacing(2)};
    border-radius: ${theme.general.borderRadiusLg};
    height: 48px;
    justify-content: center;
    font-size: ${theme.typography.pxToRem(13)};
    padding: 0;
    position: relative;
    color: ${theme.colors.alpha.trueWhite[50]};
    background-color: ${theme.colors.alpha.white[10]};

    .MuiSvgIcon-root {
      transition: ${theme.transitions.create(['color'])};
      font-size: ${theme.typography.pxToRem(26)};
      color: ${theme.colors.alpha.trueWhite[50]};
    }

    &.Mui-active,
    &:hover {
      background-color: ${alpha(theme.colors.alpha.white[30], 0.2)};

      .MuiSvgIcon-root {
        color: ${theme.colors.alpha.trueWhite[100]};
      }
    }
`
);

function TopBar() {
  const { sidebarToggle, toggleSidebar } = useContext(SidebarContext);
  const theme = useTheme();
  const [settings] = useContext(SettingsContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const { user } = useAuth();
  const [coverLogoUrl, setCoverLogoUrl] = useState('');

  useLayoutEffect(() => {
    if (user && settings) {
      console.log('Cover photo setting...');
      if (user?.profile?.record?.fields?.custentity_ng_eh_customer_cover_url) {
        console.log(
          'Cover photo setting as...',
          user.profile.record.fields.custentity_ng_eh_customer_cover_url
        );
        setCoverLogoUrl(
          user.profile.record.fields.custentity_ng_eh_customer_cover_url
        );
      } else if (settings?.custrecord_ng_eh_fallback_cover_img_url) {
        console.log(
          'Cover photo setting as (settings)...',
          settings.custrecord_ng_eh_fallback_cover_img_url
        );
        setCoverLogoUrl(settings.custrecord_ng_eh_fallback_cover_img_url);
      } else {
        setCoverLogoUrl('');
      }
    }
  }, [settings, user]);

  return (
    <TopBarWrapper className="shadow-md">
      <TopBarImage
        sx={{
          opacity: 0.7,
          background: `${theme.colors.gradients.black1}`
        }}
      />
      <TopBarImage
        sx={{
          opacity: 0.5
          // background: `${theme.colors.gradients.blue5}`
        }}
      />
      <TopBarImage
        sx={{
          opacity: 0.45,
          backgroundImage: `url(${getDeploymentUrl.core_account_url}${coverLogoUrl})`
        }}
      />
      <Container
        sx={{
          zIndex: 6
        }}
        maxWidth="xl"
      >
        <Box display="flex" justifyContent="space-between" alignItems="center">
          <Box display="flex">
            <LogoCustomerNav />
            <Box
              component="span"
              sx={{
                display: { xs: 'none', md: 'inline-flex' }
              }}
            >
              {/* <Search /> */}
            </Box>
          </Box>
          <Box display="flex">
            <Box
              component="span"
              sx={{
                display: { xs: 'none', sm: 'inline-flex' }
              }}
            >
              {/* <HeaderCart /> */}
              <Box mx={1}>
                <HeaderCart />
              </Box>
            </Box>
            <Userbox />
            <Box
              component="span"
              sx={{
                display: { md: 'none', xs: 'inline-flex' }
              }}
            >
              <Tooltip arrow title="Toggle Menu">
                <IconButtonPrimary color="primary" onClick={toggleSidebar}>
                  {!sidebarToggle ? <MenuTwoToneIcon /> : <CloseTwoToneIcon />}
                </IconButtonPrimary>
              </Tooltip>
            </Box>
          </Box>
        </Box>
        <DividerWrapper
          sx={{
            display: { xs: 'none', md: 'flex' },
            my: 4
          }}
        />
        <Box
          display="flex"
          alignItems="center"
          sx={{
            width: '100%',
            display: { xs: 'none', md: 'inline-block' }
          }}
        >
          <NavigationMenu />
        </Box>
      </Container>
    </TopBarWrapper>
  );
}

export default TopBar;
