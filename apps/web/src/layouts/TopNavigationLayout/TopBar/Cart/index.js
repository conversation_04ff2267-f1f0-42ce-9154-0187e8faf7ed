import {
  alpha,
  Ava<PERSON>,
  Badge,
  Box,
  Button,
  ButtonGroup,
  Card,
  CardHeader,
  darken,
  Divider,
  IconButton,
  lighten,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Popover,
  styled,
  Tooltip,
  Typography
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Fragment, useContext, useEffect, useRef, useState } from 'react';
import { Link as RouterLink } from 'react-router-dom';
import Link from '@mui/material/Link';
import { useTranslation } from 'react-i18next';
import ClearTwoToneIcon from '@mui/icons-material/ClearTwoTone';
import {
  ErrorTwoTone,
  RemoveTwoTone,
  ShoppingCartTwoTone
} from '@mui/icons-material';
import Scrollbar from '../../../../components/Scrollbar';
import Label from '../../../../components/Label';
import HeaderCartOptionsBox from './OptionsBox';
import useAuth from '../../../../hooks/useAuth';
import { SettingsContext } from '../../../../contexts/SettingsContext';
import AddTwoToneIcon from '@mui/icons-material/AddTwoTone';
import {
  useCart,
  useRemoveCartItem,
  useSetItemQuantity
} from '../../../../store/cartStore';
import useHoistedUrls from '../../../../hooks/useHoistedUrls';

const AvatarWarning = styled(Avatar)(
  ({ theme }) => `
          background-color: ${theme.colors.warning.lighter};
          color: ${theme.colors.warning.main};
          width: ${theme.spacing(8)};
          height: ${theme.spacing(8)};
          margin-left: auto;
          margin-right: auto;
    `
);

const CartBadge = styled(Badge)(
  ({ theme }) => `
    
    .MuiBadge-badge {
        background-color: ${alpha(theme.palette.primary.main, 0.4)};
        color: ${
          theme.palette.mode === 'dark'
            ? lighten(theme.palette.primary.main, 0.7)
            : darken(theme.palette.primary.main, 0.7)
        };
        min-width: 16px; 
        height: 16px;
        padding: 0;

        &::after {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            box-shadow: 0 0 0 1px ${alpha(theme.palette.primary.main, 0.7)};
            content: "";
        }
    }
`
);

const CardWrapper = styled(Card)(
  ({ theme }) => `
      background: ${alpha(theme.colors.alpha.black[10], 0.05)};
      border-radius: 0;
  `
);

const LinkHover = styled('box')(
  ({ theme }) => `
    transition: ${theme.transitions.create([
      'transform',
      'opacity',
      'box-shadow'
    ])};
    transform: translateY(0px);
    display: block;
    opacity: 1;

    &:hover {
        opacity: .9;
        transform: translateY(-4px);
    }
  `
);

const IconButtonWrapper = styled(IconButton)(
  ({ theme }) => `
    transition: ${theme.transitions.create(['transform', 'background'])};
    transform: scale(1);
    transform-origin: center;
    &:hover {
        transform: scale(1.1);
    }
  `
);

const ListWrapper = styled(List)(
  () => `
      .MuiListItem-root:last-of-type + .MuiDivider-root {
          display: none;
      }
  `
);

const IconButtonTrayWrapper = styled(IconButton)(
  ({ theme }) => `
        width: ${theme.spacing(6)};
        height: 48px;
        border-radius: ${theme.general.borderRadiusLg};
        padding: ${theme.spacing(0, 1)};
        color: ${theme.colors.alpha.trueWhite[50]};
        background-color: ${theme.colors.alpha.white[10]};
      :hover {
          background-color: ${alpha(theme.colors.alpha.white[30], 0.2)};
      }
      
     .MuiSvgIcon-root {
        transition: ${theme.transitions.create(['color'])};
        font-size: ${theme.typography.pxToRem(24)};
      }
`
);

function HeaderCart() {
  const ref = useRef(null);
  const { currentProject } = useAuth();
  const [isOpen, setOpen] = useState(false);
  const cart = useCart();
  const { removeCartItem } = useRemoveCartItem();
  const { setItemQuantity } = useSetItemQuantity();
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useContext(SettingsContext);
  const [totalQuantity, setTotalQuantity] = useState(0);
  const theme = useTheme();

  useEffect(() => {
    if (cart && cart.length !== 0) {
      let sum = 0;
      cart.forEach((item) => {
        sum += item.quantity;
      });
      setTotalQuantity(sum);
    } else {
      setTotalQuantity(0);
    }
  }, [cart]);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const { t } = useTranslation();

  // const items = [
  //   {
  //     id: 1,
  //     title: 'Amazon Dot Echo 3',
  //     image: '/static/images/placeholders/products/1.png',
  //     price: '79'
  //   },
  //   {
  //     id: 2,
  //     title: 'Autodesk 3D Printer PRO',
  //     image: '/static/images/placeholders/products/2.png',
  //     price: '399'
  //   },
  //   {
  //     id: 3,
  //     title: 'Apple iPhone 12 PRO',
  //     image: '/static/images/placeholders/products/3.png',
  //     price: '749'
  //   },
  //   {
  //     id: 4,
  //     title: 'GoPro Action Camera 3',
  //     image: '/static/images/placeholders/products/4.png',
  //     price: '289'
  //   },
  //   {
  //     id: 5,
  //     title: 'Apple Watch 42mm Gen. 4',
  //     image: '/static/images/placeholders/products/5.png',
  //     price: '199'
  //   }
  // ];

  const getImageFileAttachment = (url) => {
    if (url) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  return (
    <>
      <Tooltip arrow title={t('My Cart')}>
        <IconButtonTrayWrapper color="secondary" ref={ref} onClick={handleOpen}>
          <CartBadge
            badgeContent={cart?.length ?? 0}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right'
            }}
          >
            <ShoppingCartTwoTone />
          </CartBadge>
        </IconButtonTrayWrapper>
      </Tooltip>
      <Popover
        disableScrollLock
        anchorEl={ref.current}
        onClose={handleClose}
        open={isOpen}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right'
        }}
      >
        <Card
          sx={{
            minWidth: 486
          }}
        >
          <CardHeader
            sx={{
              p: 2
            }}
            disableTypography
            action={<HeaderCartOptionsBox toggleCartView={(e) => setOpen(e)} />}
            title={
              <>
                <Typography
                  sx={{
                    ml: { md: 5, xs: 1 },
                    fontSize: `${theme.typography.pxToRem(17)}`
                  }}
                  gutterBottom
                  variant="h3"
                  textAlign="center"
                >
                  {t('Shopping Cart')}
                </Typography>
              </>
            }
            subheader={
              <>
                {currentProject ? (
                  <Typography variant="subtitle2" textAlign="center">
                    {t('')}
                  </Typography>
                ) : (
                  <Typography variant="subtitle2" textAlign="center">
                    {t('')}
                  </Typography>
                )}
              </>
            }
          />
          <Divider />
          <Box
            sx={{
              height: 303
            }}
          >
            <Scrollbar>
              <ListWrapper disablePadding>
                {cart &&
                  cart.map((item) => (
                    <Fragment key={item.id}>
                      <ListItem
                        sx={{
                          display: { md: 'flex' },
                          py: 1.5,
                          px: 2
                        }}
                      >
                        <ListItemAvatar
                          sx={{
                            mr: 2,
                            mb: { xs: 1, md: 0 }
                          }}
                        >
                          <LinkHover>
                            <Avatar
                              variant="rounded"
                              sx={{
                                width: 100,
                                height: 'auto'
                              }}
                              alt={item.displayname}
                              src={getImageFileAttachment(
                                item.custitem_ng_eh_item_primary_image
                              )}
                            />
                          </LinkHover>
                        </ListItemAvatar>
                        <ListItemText
                          disableTypography
                          primary={
                            <Typography
                              sx={{
                                display: 'block',
                                mb: 1
                              }}
                              noWrap
                              color="text.primary"
                              variant="h4"
                              href="#"
                            >
                              {item.displayname}
                            </Typography>
                          }
                          secondary={
                            <>
                              <Label color="primary">
                                Quantity: {item.quantity}
                              </Label>
                              <Tooltip title="Remove Item">
                                <IconButtonWrapper
                                  sx={{
                                    ml: 1,
                                    backgroundColor: `${theme.colors.error.lighter}`,
                                    color: `${theme.colors.error.main}`,
                                    transition: `${theme.transitions.create([
                                      'all'
                                    ])}`,

                                    '&:hover': {
                                      backgroundColor: `${theme.colors.error.main}`,
                                      color: `${theme.palette.getContrastText(
                                        theme.colors.error.main
                                      )}`
                                    }
                                  }}
                                  size="small"
                                  onClick={() => removeCartItem(item.id)}
                                >
                                  <ClearTwoToneIcon fontSize="small" />
                                </IconButtonWrapper>
                              </Tooltip>
                            </>
                          }
                        />
                        <Box
                          component="span"
                          sx={{
                            display: { xs: 'flex', md: 'inline-block' }
                          }}
                        >
                          <Box ml={3} textAlign="right">
                            <ButtonGroup
                              orientation="vertical"
                              variant="contained"
                              size="small"
                            >
                              <Button
                                sx={{
                                  px: 1,
                                  transition: `${theme.transitions.create([
                                    'all'
                                  ])}`
                                }}
                                onClick={() =>
                                  setItemQuantity(item, item.quantity + 1)
                                }
                              >
                                <AddTwoToneIcon fontSize="small" />
                              </Button>
                              <Button
                                sx={{
                                  px: 1,
                                  transition: `${theme.transitions.create([
                                    'all'
                                  ])}`
                                }}
                                onClick={() =>
                                  item.quantity <= 1
                                    ? removeCartItem(item.id)
                                    : setItemQuantity(item, item.quantity - 1)
                                }
                              >
                                <RemoveTwoTone fontSize="small" />
                              </Button>
                            </ButtonGroup>
                          </Box>
                        </Box>
                      </ListItem>
                      <Divider />
                    </Fragment>
                  ))}
                {!currentProject && (
                  <Box pb={3}>
                    <Divider
                      sx={{
                        mb: 3
                      }}
                    />
                    <AvatarWarning>
                      <ErrorTwoTone />
                    </AvatarWarning>
                    <Typography
                      sx={{
                        mt: 2,
                        textAlign: 'center'
                      }}
                      variant="subtitle2"
                    >
                      Select a{' '}
                      <span>
                        <Link
                          component={RouterLink}
                          to="/management/projects/list"
                        >
                          project
                        </Link>
                      </span>{' '}
                      to begin shopping!
                    </Typography>
                  </Box>
                )}
              </ListWrapper>
            </Scrollbar>
          </Box>
          <Divider />
          <CardWrapper
            elevation={0}
            sx={{
              textAlign: 'right',
              p: 2
            }}
          >
            <Typography variant="caption" fontWeight="bold">
              {t('Total Items')}
              <Typography
                sx={{
                  pl: 1
                }}
                component="span"
                variant="h4"
                color="text.primary"
              >
                <b>{totalQuantity}</b>
              </Typography>
            </Typography>
          </CardWrapper>
          <Divider />
          <Box
            p={2}
            sx={{
              textAlign: 'center'
            }}
          >
            <Button
              component={RouterLink}
              onClick={
                !currentProject || (cart && cart.length === 0)
                  ? null
                  : handleClose
              }
              to="/checkout"
              disabled={!currentProject || (cart && cart.length === 0)}
              variant="contained"
              fullWidth
              sx={{
                py: 2,
                fontSize: `${theme.typography.pxToRem(12)}`,
                textTransform: 'uppercase'
              }}
            >
              {t('Proceed to checkout')}
            </Button>
          </Box>
        </Card>
      </Popover>
    </>
  );
}

export default HeaderCart;
