import PropTypes from 'prop-types';
import { Scrollbars } from 'react-custom-scrollbars-2';

import { Box, useTheme } from '@mui/material';
import { useState } from 'react';

const Scrollbar = ({ className, children, ...rest }) => {
  const theme = useTheme();
  const [orderAnchorEl, setOrderAnchorEl] = useState(null);

  const handleClick = (event) => {
    setOrderAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setOrderAnchorEl(null);
  };

  return (
    <Scrollbars
      onMouseEnter={handleClick}
      onMouseLeave={handleClose}
      autoHide={!orderAnchorEl}
      autoHideTimeout={1000}
      // Duration for hide animation in ms.
      autoHideDuration={200}
      renderThumbVertical={() => {
        return (
          <Box
            sx={{
              width: 5,
              background: `${theme.colors.alpha.black[10]}`,
              borderRadius: `${theme.general.borderRadiusLg}`,
              transition: `${theme.transitions.create(['background'])}`,

              '&:hover': {
                background: `${theme.colors.alpha.black[50]}`
              }
            }}
          />
        );
      }}
      {...rest}
    >
      {children}
    </Scrollbars>
  );
};

Scrollbar.propTypes = {
  children: PropTypes.node,
  className: PropTypes.string
};

export default Scrollbar;
