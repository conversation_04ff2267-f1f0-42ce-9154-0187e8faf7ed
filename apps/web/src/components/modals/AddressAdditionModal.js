import React, {
  useContext,
  useRef,
  useState,
  useEffect,
  useCallback
} from 'react';
import PropTypes from 'prop-types';
import useSWR from 'swr';
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
  useMediaQuery
} from '@mui/material';
import { Formik } from 'formik';
import { withStyles } from '@mui/styles';
import { amber, blue } from '@mui/material/colors';
import { useTranslation } from 'react-i18next';
import { useTheme } from '@mui/material/styles';

import * as Yup from 'yup';
import { LocationAutoComplete } from '../Containers/LocationAutocomplete';
import PhoneMaskedField from '../Containers/PhoneMaskedField';
import wait from '../../utils/wait';
import { DimensionsContext } from '../../contexts/DimensionsContext';
import useHoistedUrls from '../../hooks/useHoistedUrls';

// Custom debounce hook
const useDebounce = (value, delay) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

const AddressAdditionModal = (props) => {
  const [dimensions] = useContext(DimensionsContext);
  const theme = useTheme();
  const { t } = useTranslation();
  const [stateOptions, setStateOptions] = useState([]);
  const [stateInputOpen, setStateInputOpen] = useState(false);
  const [countryOptions, setCountryOptions] = useState([]);
  const [countryInputOpen, setCountryInputOpen] = useState(false);
  const [countrySelected, setCountrySelected] = useState('');
  const [countrySearchTerm, setCountrySearchTerm] = useState('');
  const [stateSearchTerm, setStateSearchTerm] = useState('');
  const [billingAddressObject, setBillingAddressObject] = useState({});
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  // Debounce search terms
  const debouncedCountrySearch = useDebounce(countrySearchTerm, 300);
  const debouncedStateSearch = useDebounce(stateSearchTerm, 300);

  const { getDeploymentUrl } = useHoistedUrls();
  const getLocationInfoUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_location_info_external
      : getDeploymentUrl.get_location_info_internal;

  const { data: countriesData, error: countriesError } = useSWR(() => {
    if (!countryInputOpen) return null;
    const params = new URLSearchParams({
      mode: 'country',
      searchQuery: debouncedCountrySearch
    });
    return `${getLocationInfoUrl}&${params.toString()}`;
  });

  const { data: statesData, error: statesError } = useSWR(() => {
    if (!stateInputOpen || !countrySelected) return null;
    const params = new URLSearchParams({
      mode: 'province',
      country: countrySelected,
      searchQuery: debouncedStateSearch
    });
    return `${getLocationInfoUrl}&${params.toString()}`;
  });

  const loadingCountries =
    countryInputOpen && !countriesData && !countriesError;
  const loadingStates = stateInputOpen && !statesData && !statesError;

  const AmberCheckbox = withStyles({
    root: {
      color: amber[400],
      '&$checked': {
        color: amber[600]
      }
    },
    checked: {}
  })((props) => <Checkbox color="default" {...props} />);

  const TealCheckbox = withStyles({
    root: {
      color: blue[400],
      '&$checked': {
        color: blue[600]
      }
    },
    checked: {}
  })((props) => <Checkbox color="default" {...props} />);

  // Update states options when statesData changes
  useEffect(() => {
    if (statesData?.states) {
      setStateOptions(statesData.states);
    }
  }, [statesData]);

  useEffect(() => {
    if (!stateInputOpen) {
      setStateOptions([]);
    }
  }, [stateInputOpen]);

  // Update country options when countriesData changes
  useEffect(() => {
    console.log('countriesData', countriesData);
    if (countriesData?.countries) {
      setCountryOptions(countriesData.countries);
    }
  }, [countriesData]);

  useEffect(() => {
    if (!stateInputOpen) {
      setStateOptions([]);
      setStateSearchTerm('');
    }
  }, [stateInputOpen]);

  useEffect(() => {
    if (!countryInputOpen) {
      setCountryOptions([]);
      setCountrySearchTerm('');
    }
  }, [countryInputOpen]);

  const handleCountryChangeSelect = useCallback((value, setFieldValue) => {
    const countryId = value?.uniquekey || '';
    setCountrySelected(countryId);
    setFieldValue('billingCountry', value?.name || '');
    // Reset state when country changes
    setFieldValue('billingState', '');
    setStateSearchTerm('');
  }, []);

  const handleAddressPost = async (
    type,
    _values,
    setStatus,
    setSubmitting,
    setErrors,
    resetForm
  ) => {
    await props.handlePostToNetSuite(
      type,
      _values,
      setStatus,
      setSubmitting,
      setErrors,
      resetForm
    );
    setBillingAddressObject({});
    setCountrySelected('');
    props.toggle(props.open);
  };

  return (
    <Dialog
      fullScreen={dimensions.width < 568}
      open={props.open}
      onClose={() => props.toggle(props.open)}
    >
      <DialogTitle
        sx={{
          p: 3
        }}
      >
        <Typography variant="h4" gutterBottom>
          {t('Add Address')}
        </Typography>
        <Typography variant="subtitle2">
          {t('Add a new address to your address book.')}
        </Typography>
      </DialogTitle>
      <Formik
        initialValues={{
          firstName: '',
          lastName: '',
          billingAddress1:
            billingAddressObject?.structured_formatting?.main_text || '',
          billingAddress2: '',
          billingState: '',
          billingCity: '',
          billingCountry: '',
          billingZip: '',
          isResidential: false,
          defaultBilling: false,
          defaultShipping: false,
          billingPhone: '',
          billingAddressName:
            billingAddressObject?.structured_formatting?.main_text,
          submit: null,
          billingAddressObject
        }}
        validationSchema={Yup.object().shape({
          firstName: Yup.string(),
          lastName: Yup.string(),
          billingPhone: Yup.string(),
          billingAddress1: Yup.string().required(
            t('Insert an address to add to your account.')
          ),
          billingAddress2: Yup.string(),
          billingState: Yup.string().required(
            t('Please select a state associated with the address.')
          ),
          billingCity: Yup.string().required(t('Please select a city')),
          billingCountry: Yup.string().required(t('Please select a country')),
          billingZip: Yup.string().min(5).required(t('Please enter a zip'))
        })}
        onSubmit={async (
          _values,
          { resetForm, setErrors, setStatus, setSubmitting }
        ) => {
          try {
            await wait(3000);
            await handleAddressPost(
              'address',
              _values,
              setStatus,
              setSubmitting,
              setErrors,
              resetForm
            );
          } catch (err) {
            console.error(err);
            setStatus({ success: false });
            setErrors({ submit: err.message });
            setSubmitting(false);
          }
        }}
      >
        {({
          errors,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting,
          touched,
          values,
          setFieldValue
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent
              dividers
              sx={{
                p: 3
              }}
            >
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    key={`first-name-${1}`}
                    onChange={handleChange}
                    name="firstName"
                    label="First Name"
                    type="text"
                    fullWidth
                    value={values.firstName}
                    error={Boolean(touched.firstName && errors.firstName)}
                    helperText={touched.firstName && errors.firstName}
                    InputProps={{
                      autoComplete: 'given-name'
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    key={`last-name-${1}`}
                    onChange={handleChange}
                    name="lastName"
                    label="Last Name"
                    type="text"
                    fullWidth
                    value={values.lastName}
                    error={Boolean(errors.lastName && touched.lastName)}
                    helperText={errors.lastName && touched.lastName}
                    InputProps={{
                      autoComplete: 'family-name'
                    }}
                  />
                </Grid>
                {/* Autocomplete Address 1 */}
                <Grid item xs={12}>
                  <LocationAutoComplete
                    name="billingAddress1"
                    setAddressName={(e, v) => {
                      console.log('Address Name Set:', v);
                      setBillingAddressObject(v);
                      let mainText = v?.structured_formatting?.main_text;
                      let description = v?.description;
                      let city = '';
                      if (description) {
                        city = description
                          .slice(
                            description.indexOf(',') + 1,
                            description.indexOf(
                              ',',
                              description.indexOf(',') + 1
                            )
                          )
                          .trim();
                      }
                      console.log('City', city);
                      console.log('Description:', description);
                      setFieldValue('billingAddress1', mainText);
                      setFieldValue('billingCity', city);
                      console.log('Address Name Set Values:', values);
                    }}
                    setAddress1={(v) => setFieldValue('billingAddress1', v)}
                    addressName={
                      billingAddressObject?.structured_formatting?.main_text ||
                      values.billingAddress1
                    }
                    manualInput={values.billingAddress1}
                    label="Address"
                    error={Boolean(
                      errors.billingAddress1 && touched.billingAddress1
                    )}
                    helperText={
                      errors.billingAddress1 && touched.billingAddress1
                    }
                    InputProps={{
                      autoComplete: 'address-line1'
                    }}
                  />
                </Grid>
                {/* Address 2 */}
                <Grid item xs={12} md={6}>
                  <TextField
                    key={`billing-address${2}`}
                    value={values.billingAddress2}
                    name="billingAddress2"
                    onChange={handleChange}
                    placeholder="Optional (Suite, Apt#, etc.)"
                    label="Address 2 (optional)"
                    type="text"
                    fullWidth
                    InputProps={{
                      autoComplete: 'address-line2'
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    value={values.billingCity}
                    onBlur={handleBlur}
                    name="billingCity"
                    onChange={handleChange}
                    label="City"
                    fullWidth
                    error={Boolean(touched.billingCity && errors.billingCity)}
                    helperText={touched.billingCity && errors.billingCity}
                    InputProps={{
                      autoComplete: 'address-level2'
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    fullWidth
                    name="billingCountry"
                    key="country-ac"
                    label="Country/region"
                    open={countryInputOpen}
                    loading={loadingCountries}
                    onOpen={() => {
                      setCountryInputOpen(true);
                    }}
                    onClose={() => {
                      setCountryInputOpen(false);
                    }}
                    options={[...countryOptions]}
                    getOptionSelected={(option, value) => {
                      // console.log('Option', option)
                      // console.log('value', value)
                      return option.name === value;
                    }}
                    getOptionLabel={(option) => option?.name || ''}
                    // filterSelectedOptions
                    // filterOptions={option => option}
                    /* TODO: Make an override capture */
                    onInputChange={(_, newInputValue) => {
                      setCountrySearchTerm(newInputValue);
                    }}
                    onChange={(_, value) => {
                      handleCountryChangeSelect(value, setFieldValue);
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        fullWidth
                        type="text"
                        name="billingCountry"
                        label="Country/region"
                        value={values.billingCountry}
                        error={Boolean(
                          touched.billingCountry && errors.billingCountry
                        )}
                        helperText={
                          touched.billingCountry && errors.billingCountry
                        }
                        InputProps={{
                          ...params.InputProps,
                          autoComplete: 'country-name',
                          endAdornment: (
                            <>
                              {loadingCountries ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          )
                        }}
                      />
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    fullWidth
                    name="billingState"
                    key="state-ac"
                    label="State"
                    open={stateInputOpen}
                    loading={loadingStates}
                    onOpen={() => {
                      setStateInputOpen(true);
                    }}
                    onClose={() => {
                      setStateInputOpen(false);
                    }}
                    options={stateOptions}
                    getOptionSelected={(option, value) =>
                      option.fullname === value
                    }
                    getOptionLabel={(option) => option?.fullname || ''}
                    // filterSelectedOptions
                    // filterOptions={option => option}
                    onInputChange={(_, newInputValue) => {
                      setStateSearchTerm(newInputValue);
                    }}
                    onChange={(_, value) => {
                      setFieldValue('billingState', value?.fullname || '');
                    }}
                    renderInput={(params) => (
                      <TextField
                        {...params}
                        name="billingState"
                        label="State"
                        type="text"
                        fullWidth
                        value={values.billingState}
                        error={Boolean(
                          touched.billingState && errors.billingState
                        )}
                        helperText={touched.billingState && errors.billingState}
                        InputProps={{
                          ...params.InputProps,
                          autoComplete: 'address-level1',
                          endAdornment: (
                            <>
                              {loadingStates ? (
                                <CircularProgress color="inherit" size={20} />
                              ) : null}
                              {params.InputProps.endAdornment}
                            </>
                          )
                        }}
                      />
                    )}
                  />
                  {/* <MDBInput label='Country' group validate /> */}
                </Grid>
                <Grid item xs={12} md={6}>
                  <TextField
                    value={values.billingZip}
                    name="billingZip"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    label="ZIP Code"
                    fullWidth
                    type="text"
                    error={Boolean(touched.billingZip && errors.billingZip)}
                    helperText={touched.billingZip && errors.billingZip}
                    InputProps={{
                      autoComplete: 'postal-code'
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <PhoneMaskedField
                    value={values.billingPhone}
                    name="billingPhone"
                    onChange={handleChange}
                    label="Phone"
                    fullWidth
                    variant="outlined"
                    type="tel"
                    onBlur={handleBlur}
                    error={Boolean(touched.billingPhone && errors.billingPhone)}
                    helperText={touched.billingPhone && errors.billingPhone}
                    sx={{
                      '& .MuiInputBase-input': {
                        color: theme === 'dark' ? 'white' : 'black'
                      }
                    }}
                    InputProps={{
                      autoComplete: 'tel'
                    }}
                  />
                  {/* <CssTextField value={props.phone} name='billingPhone' onChange={(e, v) => props.onChange(e,v)} label="Phone" fullWidth variant='outlined' /> */}
                </Grid>
                <Grid item xs={12}>
                  <Box>
                    <FormControlLabel
                      control={
                        <AmberCheckbox
                          name="defaultBilling"
                          onChange={(e, v) =>
                            setFieldValue('defaultBilling', v)
                          }
                          checked={values.defaultBilling}
                        />
                      }
                      label="Default Billing"
                    />
                    <FormControlLabel
                      control={
                        <TealCheckbox
                          name="defaultShipping"
                          onChange={(e, v) =>
                            setFieldValue('defaultShipping', v)
                          }
                          checked={values.defaultShipping}
                        />
                      }
                      label="Default Shipping"
                    />
                  </Box>
                  {/* <MDBCol> */}
                  {/*	<FormControlLabel */}
                  {/*		control={<TealCheckbox name="defaultShipping" */}
                  {/*							   onChange={(e, v) => props.p_onChangeSaveAddress(e, v)} */}
                  {/*							   checked={props.defaultShipping} */}
                  {/*		/>} */}
                  {/*		label="Default Shipping" */}
                  {/*	/> */}
                  {/* </MDBCol> */}
                  {/* <MDBCol> */}
                  {/*	<FormControlLabel */}
                  {/*		control={<Checkbox icon={<BusinessIcon style={{ color: lightGreen[600]}}/>} */}
                  {/*						   checkedIcon={<HomeIcon style={{color: blue[500]}}/>} */}
                  {/*						   name="isResidential" */}
                  {/*						   onChange={(e, v) => props.handleIsResidential(e, v)} */}
                  {/*		/>} */}
                  {/*		label={props.isResidential ? "Residential Address" : "Commercial Address"} */}
                  {/*	/> */}
                  {/* </MDBCol> */}
                </Grid>
              </Grid>
            </DialogContent>
            <Box
              sx={{
                display: { xs: 'block', sm: 'flex' },
                alignItems: 'center',
                justifyContent: 'flex-end',
                p: 3
              }}
            >
              <Box>
                <Button
                  fullWidth={mobile}
                  sx={{
                    mr: { xs: 0, sm: 2 },
                    my: { xs: 2, sm: 0 }
                  }}
                  color="secondary"
                  variant="outlined"
                  onClick={props.toggle}
                >
                  {t('Close')}
                </Button>
                <Button
                  fullWidth={mobile}
                  type="submit"
                  startIcon={
                    isSubmitting ? <CircularProgress size="1rem" /> : null
                  }
                  disabled={Boolean(errors.submit) || isSubmitting}
                  variant="contained"
                  size="large"
                >
                  {t('Add Address')}
                </Button>
              </Box>
            </Box>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

AddressAdditionModal.propTypes = {
  handlePostToNetSuite: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired
};

export default AddressAdditionModal;
