import React, { useContext } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  Button,
  Checkbox,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControlLabel,
  Grid,
  TextField,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTheme, withStyles } from '@mui/styles';
import { teal } from '@mui/material/colors';
import { t } from 'i18next';
import { Formik } from 'formik';
import * as Yup from 'yup';
import wait from '../../utils/wait';
import { DimensionsContext } from '../../contexts/DimensionsContext';

const PaymentAdditionModal = (props) => {
  const [dimensions] = useContext(DimensionsContext);
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));

  const TealCheckbox = withStyles({
    root: {
      color: teal[400],
      '&$checked': {
        color: teal[600]
      }
    },
    checked: {}
  })((props) => <Checkbox color="default" {...props} />);

  const handleCardPost = async (
    type,
    _values,
    setStatus,
    setSubmitting,
    setErrors,
    resetForm
  ) => {
    await props.handlePostToNetSuite(
      type,
      _values,
      setStatus,
      setSubmitting,
      setErrors,
      resetForm
    );
    props.toggle(props.open);
  };

  return (
    <Dialog
      fullScreen={dimensions.width < 568}
      open={props.open}
      onClose={() => props.toggle(props.open)}
    >
      <DialogTitle
        sx={{
          p: 3
        }}
      >
        <Typography variant="h4" gutterBottom>
          {t('Add Payment')}
        </Typography>
        <Typography variant="subtitle2">
          {t('Add a payment method to your account.')}
        </Typography>
      </DialogTitle>
      <Formik
        initialValues={{
          paymentNameOnCard: '',
          paymentCardNumber: '',
          paymentExpiry: '',
          paymentCardNote: '',
          defaultPayment: false,
          submit: null
        }}
        validationSchema={Yup.object().shape({
          paymentNameOnCard: Yup.string().required(
            t('Please enter a cardholder name')
          ),
          paymentCardNumber: Yup.string().required(
            t('Enter a valid card number')
          ),
          paymentExpiry: Yup.date().required(
            t('Enter a valid card expiration date')
          ),
          paymentCardNote: Yup.string(),
          defaultPayment: Yup.bool()
        })}
        onSubmit={async (
          _values,
          { resetForm, setErrors, setStatus, setSubmitting }
        ) => {
          try {
            await wait(3000);
            await handleCardPost(
              'card',
              _values,
              setStatus,
              setSubmitting,
              setErrors,
              resetForm
            );
          } catch (err) {
            console.error(err);
            setStatus({ success: false });
            setErrors({ submit: err.message });
            setSubmitting(false);
          }
        }}
      >
        {({
          errors,
          handleBlur,
          handleChange,
          handleSubmit,
          isSubmitting,
          touched,
          values,
          setFieldValue
        }) => (
          <form onSubmit={handleSubmit}>
            <DialogContent
              dividers
              sx={{
                p: 3
              }}
            >
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TextField
                    key={`cardholder-name-${1}`}
                    onChange={handleChange}
                    name="paymentNameOnCard"
                    label="Full Name (Name On Card)"
                    type="text"
                    fullWidth
                    value={values.paymentNameOnCard}
                    onBlur={handleBlur}
                    error={Boolean(
                      touched.paymentNameOnCard && errors.paymentNameOnCard
                    )}
                    helperText={
                      touched.paymentNameOnCard && errors.paymentNameOnCard
                    }
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    key={`'p-card-number-${1}`}
                    onChange={handleChange}
                    onBlur={handleBlur}
                    name="paymentCardNumber"
                    label="Card Number"
                    type="number"
                    id="ccNumber"
                    fullWidth
                    // onBlur={() => props.p_encryptCard()} TODO: Use for paytrace
                    value={values.paymentCardNumber}
                    error={Boolean(
                      errors.paymentCardNumber && touched.paymentCardNumber
                    )}
                    helperText={
                      errors.paymentCardNumber && touched.paymentCardNumber
                    }
                  />
                </Grid>

                <Grid item xs={12} md={6}>
                  <TextField
                    value={values.paymentExpiry}
                    key={`p-expiry-${1}`}
                    name="paymentExpiry"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    label="Expiration"
                    fullWidth
                    type="month"
                    error={Boolean(
                      touched.paymentExpiry && errors.paymentExpiry
                    )}
                    helperText={touched.paymentExpiry && errors.paymentExpiry}
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    value={values.paymentCardNote}
                    key={`p-memo-${1}`}
                    name="paymentCardNote"
                    onChange={handleChange}
                    onBlur={handleBlur}
                    label="Memo"
                    fullWidth
                    type="text"
                    error={Boolean(
                      touched.paymentCardNote && errors.paymentCardNote
                    )}
                    helperText={
                      touched.paymentCardNote && errors.paymentCardNote
                    }
                  />
                </Grid>

                <Grid item xs={12}>
                  <Box>
                    <FormControlLabel
                      control={
                        <TealCheckbox
                          name="defaultPayment"
                          onChange={(e, v) =>
                            setFieldValue('defaultPayment', v)
                          }
                          checked={values.defaultPayment}
                        />
                      }
                      label="Default Payment"
                    />
                  </Box>
                  {/* <MDBCol> */}
                  {/*	<FormControlLabel */}
                  {/*		control={<TealCheckbox name="defaultShipping" */}
                  {/*							   onChange={(e, v) => props.p_onChangeSaveAddress(e, v)} */}
                  {/*							   checked={props.defaultShipping} */}
                  {/*		/>} */}
                  {/*		label="Default Shipping" */}
                  {/*	/> */}
                  {/* </MDBCol> */}
                  {/* <MDBCol> */}
                  {/*	<FormControlLabel */}
                  {/*		control={<Checkbox icon={<BusinessIcon style={{ color: lightGreen[600]}}/>} */}
                  {/*						   checkedIcon={<HomeIcon style={{color: blue[500]}}/>} */}
                  {/*						   name="isResidential" */}
                  {/*						   onChange={(e, v) => props.handleIsResidential(e, v)} */}
                  {/*		/>} */}
                  {/*		label={props.isResidential ? "Residential Address" : "Commercial Address"} */}
                  {/*	/> */}
                  {/* </MDBCol> */}
                </Grid>
              </Grid>
            </DialogContent>
            <Box
              sx={{
                display: { xs: 'block', sm: 'flex' },
                alignItems: 'center',
                justifyContent: 'flex-end',
                p: 3
              }}
            >
              <Box>
                <Button
                  fullWidth={mobile}
                  sx={{
                    mr: { xs: 0, sm: 2 },
                    my: { xs: 2, sm: 0 }
                  }}
                  color="secondary"
                  variant="outlined"
                  onClick={props.toggle}
                >
                  {t('Close')}
                </Button>
                <Button
                  fullWidth={mobile}
                  type="submit"
                  startIcon={
                    isSubmitting ? <CircularProgress size="1rem" /> : null
                  }
                  disabled={Boolean(errors.submit) || isSubmitting}
                  variant="contained"
                  size="large"
                >
                  {t('Add Payment')}
                </Button>
              </Box>
            </Box>
          </form>
        )}
      </Formik>
    </Dialog>
  );
};

PaymentAdditionModal.propTypes = {
  handlePostToNetSuite: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  toggle: PropTypes.func.isRequired
};

export default PaymentAdditionModal;
