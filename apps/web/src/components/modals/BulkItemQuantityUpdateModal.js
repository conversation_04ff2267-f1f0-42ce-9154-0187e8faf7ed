import React from 'react';
import PropTypes from 'prop-types';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle
} from '@mui/material';

const BulkItemQuantityUpdateModal = (props) => {
  return (
    <Dialog open={props.isOpen} onClose={props.toggle} size={props.size}>
      <DialogTitle className="bg-info text-white text-center justify-content-center">
        Update Quantity
      </DialogTitle>
      <DialogContent>{props.children}</DialogContent>
      <DialogActions className="text-center justify-content-center">
        <Button fullWidth variant="outlined" onClick={props.toggle}>
          Cancel
        </Button>
        <hr />
        <Button
          fullWidth
          variant="contained"
          color="info"
          onClick={props.onQuantityUpdate}
        >
          Update Quantity
        </Button>
      </DialogActions>
    </Dialog>
  );
};

BulkItemQuantityUpdateModal.propTypes = {
  onQuantityUpdate: PropTypes.func.isRequired,
  toggle: PropTypes.func.isRequired,
  size: PropTypes.string,
  isOpen: PropTypes.bool
};

export default BulkItemQuantityUpdateModal;
