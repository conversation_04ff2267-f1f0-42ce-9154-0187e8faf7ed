import React from 'react';
import {
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  Typography
} from '@mui/material';
import { t } from 'i18next';

const DeleteConfirmModal = (props) => {
  return (
    <Dialog open={props.isOpen} onClose={props.toggle}>
      <DialogTitle
        sx={{
          p: 3
        }}
      >
        <Typography variant="h4" gutterBottom>
          {t('Confirm Delete')}
        </Typography>
      </DialogTitle>
      <DialogContent>{props.children}</DialogContent>
      <DialogActions className="text-center justify-content-center">
        <Button fullWidth variant="outlined" onClick={props.toggle}>
          No
        </Button>
        <hr />
        <Button
          fullWidth
          variant="contained"
          onClick={props.onDelete}
          color="error"
        >
          Yes
        </Button>
      </DialogActions>
    </Dialog>
  );
};

DeleteConfirmModal.propTypes = {};

export default DeleteConfirmModal;
