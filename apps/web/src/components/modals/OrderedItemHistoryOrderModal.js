import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import {
  Box,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  Fade,
  Grid,
  IconButton,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';

import { DataGridPro, GridToolbar } from '@mui/x-data-grid-pro';
import useHoistedUrls from '../../hooks/useHoistedUrls';
import useSWR from 'swr';
import { useTranslation } from 'react-i18next';
import { CloseTwoTone } from '@mui/icons-material';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Fade direction="up" ref={ref} {...props} />;
});
const OrderedItemHistoryOrderModal = ({ productId, isOpen, toggle }) => {
  // Load item history ordered by sales order
  const { t } = useTranslation();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('md'));
  const { getDeploymentUrl } = useHoistedUrls();
  const getItemsUrl =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.get_item_order_history_external
      : getDeploymentUrl.get_item_order_history_internal;

  // const { rows, columns } = useItemOrderHistory(productId);

  const { data, error } = useSWR(
    productId && isOpen ? `${getItemsUrl}&productId=${productId}` : null
  );

  useEffect(() => {
    console.log('Item history', data);
  }, [data]);

  useEffect(() => {
    let active = true;
    if (active) {
      if (data?.columns && data?.rows && !error) {
        console.log('Orders:', data);
      }
    }
  }, [isOpen, productId]);

  return (
    <div>
      <Dialog
        fullScreen={mobile}
        open={isOpen}
        onClose={toggle}
        TransitionComponent={Transition}
        maxWidth="lg"
      >
        <DialogTitle
          sx={{
            p: 3
          }}
        >
          <Typography variant="h4" gutterBottom>
            {t('Item Order History')}
          </Typography>
          <Typography variant="subtitle2">
            {t('View orders of this item from past transactions')}
          </Typography>
          <IconButton
            aria-label="close"
            onClick={toggle}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: (theme) => theme.palette.grey[500]
            }}
          >
            <CloseTwoTone />
          </IconButton>
        </DialogTitle>
        <DialogContent
          dividers
          sx={{
            p: 3
          }}
        >
          {data?.rows && data?.columns ? (
            <DataGridPro
              loading={!data?.rows}
              rows={data.rows}
              columns={data.columns}
              initialState={{
                pagination: {
                  paginationModel: {
                    pageSize: 5
                  }
                },
                columns: {
                  columnVisibilityModel: {
                    id: false
                  }
                }
              }}
              autoHeight
              pageSizeOptions={[5]}
              slots={{ toolbar: GridToolbar }}
              checkboxSelection={false}
              disableRowSelectionOnClick
            />
          ) : (
            <Grid
              container
              spacing={2}
              alignItems="center"
              justifyContent="center"
            >
              <CircularProgress size={90} disableShrink />
              <Grid item xs={12}>
                <Box display="flex" alignItems="center" justifyContent="center">
                  <Typography>Loading past orders for item...</Typography>
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

OrderedItemHistoryOrderModal.defaultProps = {
  productId: ''
};

OrderedItemHistoryOrderModal.propTypes = {
  productId: PropTypes.oneOfType([PropTypes.string, PropTypes.number])
};

export default OrderedItemHistoryOrderModal;
