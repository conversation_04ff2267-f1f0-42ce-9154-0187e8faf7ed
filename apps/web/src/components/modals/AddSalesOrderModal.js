import React, { useContext } from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  InputLabel,
  lighten,
  MenuItem,
  Select,
  Slide,
  Table,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';
import { t } from 'i18next';
import * as Yup from 'yup';
import { Formik } from 'formik';
import numeral from 'numeral';
import { useSnackbar } from 'notistack';
import { styled } from '@mui/material/styles';
import { DataGridPro } from '@mui/x-data-grid-pro';
import Grow from '@mui/material/Grow';
import {
  AssignmentOutlined,
  CopyAllOutlined,
  DeleteOutlined
} from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import useAuth from '../../hooks/useAuth';
import wait from '../../utils/wait';
import SalesOrderAutoComplete from '../containers/SalesOrderAutoComplete';
import { SettingsContext } from '../../contexts/SettingsContext';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const IconButtonError = styled(IconButton)(
  ({ theme }) => `
     background: ${theme.colors.error.lighter};
     color: ${theme.colors.error.main};
     padding: ${theme.spacing(0.5)};

     &:hover {
      background: ${lighten(theme.colors.error.lighter, 0.4)};
     }
`
);

const AddSalesOrderModal = (props) => {
  const [settings] = useContext(SettingsContext);
  const { t } = useTranslation();
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();

  const handle_sales_order_create = async (values) => {
    await props.createOrderOn(values);
  };

  return (
    <div className="card">
      <Dialog
        fullScreen
        open={props.isOpen}
        onClose={props.toggle}
        TransitionComponent={Transition}
      >
        <DialogTitle
          sx={{
            p: 3
          }}
        >
          <Typography variant="h4" gutterBottom>
            {t(props.headerTitle)}
          </Typography>
          <Typography variant="subtitle2">
            {t('Use this modal dialog to create a new sales order')}
          </Typography>
        </DialogTitle>
        <Formik
          enableReinitialize={props.copyingOrder}
          initialValues={{
            items: props.salesOrderItems,
            po_number: props.copyingOrder ? props.poNumber : '',
            ship_to_address: props.copyingOrder ? props.shipToAddress : '',
            comments: props.copyingOrder ? props.comments : '',
            entity: user.customer.fields.id, // provided user id
            submit: null
          }}
          validationSchema={Yup.object().shape({
            po_number: Yup.string().max(255),
            comments: Yup.string().max(255),
            ship_to_address: Yup.string().required(
              t('A Shipping Address is Required')
            ),
            items: Yup.array().required(
              t('Items are required in order to create an order')
            )
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              await wait(3000);
              resetForm();
              await handle_sales_order_create(_values).then(() => {
                setStatus({ success: true });
                setSubmitting(false);
              });
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values
          }) => (
            <form onSubmit={handleSubmit}>
              <DialogContent
                dividers
                sx={{
                  p: 3
                }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box pb={1}>
                      <b>{t('Shipping Address')}:</b>
                    </Box>
                    <FormControl fullWidth>
                      <InputLabel id="demo-multiple-chip-label">
                        {t('Select Address')}
                      </InputLabel>
                      <Select
                        error={Boolean(
                          touched.ship_to_address && errors.ship_to_address
                        )}
                        name="ship_to_address"
                        label="Ship To Address"
                        labelId="ship-to-select-label"
                        selected="Select Delivery Address"
                        onBlur={handleBlur}
                        // options={sales_order_delivery_addresses}
                        onChange={handleChange}
                        fullWidth
                        value={values.ship_to_address}
                        sx={{
                          '& .MuiInputBase-input': {
                            // color: theme === 'dark' ? 'white' : 'black'
                          }
                        }}
                      >
                        <MenuItem value="">None</MenuItem>
                        {props.salesOrderDeliveryAddresses.length !== 0 ? (
                          props.salesOrderDeliveryAddresses.map(
                            (address, i) => (
                              <MenuItem
                                key={`address-${i}`}
                                value={address.value.addrtext_initialvalue}
                              >
                                {address.text}
                              </MenuItem>
                            )
                          )
                        ) : (
                          <></>
                        )}
                      </Select>
                      <FormHelperText>
                        {touched.ship_to_address && errors.ship_to_address}
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box pb={1}>
                      <b>{t('PO Number')}:</b>
                    </Box>
                    <TextField
                      label="PO#"
                      name="po_number"
                      fullWidth
                      type="text"
                      onChange={handleChange}
                      value={values.po_number}
                      sx={{
                        '& .MuiInputBase-input': {
                          // color: theme === 'dark' ? 'white' : 'black'
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Box pb={1}>
                      <b>{t('Item Select')}:</b>
                    </Box>
                    <SalesOrderAutoComplete
                      data={
                        props.itemCatalogLoading
                          ? []
                          : props.itemCatalog?.results?.rows
                      }
                      loading={
                        props.itemCatalogLoading || !props.itemCatalog?.results
                      }
                      disabled={
                        !props.itemCatalog &&
                        props.itemCatalog?.results?.rows.length === 0
                      }
                      onItemSelect={(e, value) =>
                        props.handleItemAddSalesOrderSelect(e, value)
                      }
                      theme="light"
                      imagesEnabled={
                        settings?.custrecord_ng_pg_item_images_enabled === 'T'
                      }
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <TableContainer>
                {props.salesItemToolbarVisible &&
                props.selectedSalesOrderRows.size !== 0 ? (
                  <Box
                    sx={{
                      display: { xs: 'block', sm: 'flex' },
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      p: 3
                    }}
                  >
                    <Grow in>
                      <ButtonGroup
                        className="float-right"
                        aria-label="Bulk item actions"
                      >
                        <Tooltip title="Update Quantity" placement="top" arrow>
                          <Button
                            className="rounded-l-full"
                            id="impo"
                            size="sm"
                            variant="outlined"
                            color="info"
                            onClick={() => props.handleBulkQuantityUpdate()}
                          >
                            <AssignmentOutlined />
                          </Button>
                        </Tooltip>
                        {props.selectedSalesOrderRows.size === 1 && (
                          <Tooltip title="Copy Item" placement="top" arrow>
                            <Button
                              size="sm"
                              variant="outlined"
                              onClick={() => props.handleItemCopy()}
                            >
                              <CopyAllOutlined />
                            </Button>
                          </Tooltip>
                        )}
                        <Tooltip title="Delete Items" placement="top" arrow>
                          <Button
                            className="rounded-r-md"
                            onClick={() => props.handleBulkItemDelete()}
                            size="sm"
                            variant="outlined"
                            color="error"
                          >
                            <DeleteOutlined />
                          </Button>
                        </Tooltip>
                      </ButtonGroup>
                    </Grow>
                  </Box>
                ) : null}
                <DataGridPro
                  onCellEditCommit={(params, event, details) =>
                    props.itemCellEditCommit(params, event, details)
                  }
                  columns={props.salesOrderItemColumns}
                  rows={props.salesOrderItems}
                  checkboxSelection
                  // disableSelectionOnClick
                  onCellClick={(params, e, details) =>
                    console.log(
                      'Clicked:',
                      params,
                      '\nEvent:',
                      e,
                      '\nDetails:',
                      details,
                      '\nSales order items:',
                      props.salesOrderItems
                    )
                  }
                  onSelectionModelChange={(newSelectionModel) =>
                    props.setSelectedSalesOrderRows(
                      () => new Set(newSelectionModel)
                    )
                  }
                  selectionModel={[...props.selectedSalesOrderRows]}
                  rowsPerPageOptions={[5]}
                  // onRowsChange={handle_rows_change}
                  // onRowClick={(row, e) => handle_rows_select(row, e)}
                  disableSelectionOnClick
                  rowHeight={80}
                  density="comfortable"
                  style={{ height: '60vh' }}
                  // selectedRows={selectedRows}
                  // onSelectedRowsChange={setSelectedRows}
                  /* eslint-disable-next-line react/jsx-no-bind */
                  getRowId={(row) => props.itemRowKeyGetter(row)}
                  // summaryRows={sales_summary_rows}
                />
                <Table>
                  <TableFooter>
                    {settings &&
                    settings.custrecord_ng_pg_hide_dollar_calcs !== 'T' ? (
                      <TableRow>
                        <TableCell colSpan={4} align="right">
                          <Typography
                            gutterBottom
                            variant="caption"
                            color="text.secondary"
                            fontWeight="bold"
                          >
                            {t('Total')}:
                          </Typography>
                          <Typography variant="h3" fontWeight="bold">
                            {numeral(props.salesOrderTotal).format(`$0,0.00`)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : null}
                  </TableFooter>
                </Table>
              </TableContainer>
              <Box px={3} pt={3}>
                <TextField
                  multiline
                  fullWidth
                  label={t('Comments')}
                  name="comments"
                  minRows={3}
                  maxRows={8}
                  onChange={handleChange}
                  placeholder={t(
                    'Write here any additional information you might have...'
                  )}
                  value={values.comments}
                />
              </Box>
              <Box
                sx={{
                  display: { xs: 'block', sm: 'flex' },
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  p: 3
                }}
              >
                <Box>
                  <Button
                    fullWidth={mobile}
                    sx={{
                      mr: { xs: 0, sm: 2 },
                      my: { xs: 2, sm: 0 }
                    }}
                    color="secondary"
                    variant="outlined"
                    onClick={props.toggle}
                  >
                    {t('Close')}
                  </Button>
                  <Button
                    fullWidth={mobile}
                    type="submit"
                    startIcon={
                      isSubmitting ? <CircularProgress size="1rem" /> : null
                    }
                    disabled={Boolean(errors.submit) || isSubmitting}
                    variant="contained"
                    size="large"
                  >
                    {t('Create Order')}
                  </Button>
                </Box>
              </Box>
            </form>
          )}
        </Formik>
      </Dialog>
    </div>
  );
};

AddSalesOrderModal.propTypes = {};

export default AddSalesOrderModal;
