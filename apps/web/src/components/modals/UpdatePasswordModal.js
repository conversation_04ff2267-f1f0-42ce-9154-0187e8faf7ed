import React, { useContext, useState } from 'react';
import {
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  FormHelperText,
  Grid,
  Slide,
  TextField,
  Typography,
  useMediaQuery
} from '@mui/material';
import { Save } from '@mui/icons-material';
import { LoadingButton } from '@mui/lab';
import * as Yup from 'yup';
import { useTranslation } from 'react-i18next';
import { Formik } from 'formik';
import { useTheme } from '@mui/material/styles';
import wait from '../../utils/wait';
import { DimensionsContext } from '../../contexts/DimensionsContext';
import useHoistedUrls from '../../hooks/useHoistedUrls';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

function equalTo(ref, msg) {
  return Yup.mixed().test({
    name: 'equalTo',
    exclusive: false,
    // eslint-disable-next-line no-template-curly-in-string
    message: msg || '${path} must be the same as ${reference}',
    params: {
      reference: ref.path
    },
    test(value) {
      return value === this.resolve(ref);
    }
  });
}

const UpdatePasswordModal = (props) => {
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { t } = useTranslation();
  const [dimensions] = useContext(DimensionsContext);
  const { getDeploymentUrl } = useHoistedUrls();
  const [passwordChangeMsg, setPwChangeMsg] = useState(<></>);
  const [submittingPasswordChange, setLoadingPasswordChange] = useState(false);

  // eslint-disable-next-line camelcase
  let get_record_function_url =
    process.env.NODE_ENV === 'development'
      ? getDeploymentUrl.record_operation_external
      : getDeploymentUrl.record_operation_internal;

  const toggleModal = (state) => {
    props.onClose(!state);
  };

  const handlePasswordUpdate = (
    values,
    submitting,
    resetForm,
    setStatus,
    setSubmitting,
    setErrors
  ) => {
    let requestOptions = {
      method: 'PUT',
      redirect: 'follow',
      body: JSON.stringify(values)
    };

    setLoadingPasswordChange(true);
    if (
      values.confirm_password &&
      values.current_password &&
      values.new_password
    ) {
      // eslint-disable-next-line camelcase
      fetch(`${get_record_function_url}&type=UPDATEPW`, requestOptions)
        .then((res) => res.json())
        .then((resJSON) => {
          console.log('Password updated:', resJSON);
          if (resJSON?.error) {
            setStatus({ success: false });
            setSubmitting(false);
            if (resJSON?.error?.message) {
              setErrors({ submit: resJSON.error.message });
              setPwChangeMsg(
                <FormHelperText error>{resJSON?.error?.message}</FormHelperText>
              );
            }
          } else {
            setPwChangeMsg(
              <FormHelperText success>{resJSON.message}</FormHelperText>
            );
            setStatus({ success: true });
            setSubmitting(false);
            resetForm();
            toggleModal(props.open);
          }
          setLoadingPasswordChange(false);
        })
        .catch((err) => {
          setLoadingPasswordChange(false);
          console.err('Update failed on password', err);
        });
    } else {
      setLoadingPasswordChange(false);
      // eslint-disable-next-line no-alert
      alert('Please fill out current password, followed by your new password.');
    }
  };

  Yup.addMethod(Yup.string, 'equalTo', equalTo);

  return (
    <div>
      <Dialog
        fullScreen={dimensions.width < 568}
        centered
        onClose={props.onClose}
        open={props.open}
        className="text-center"
        TransitionComponent={Transition}
      >
        <DialogTitle className="text-center justify-content-center mb-3 pb-3">
          <Typography variant="h4" gutterBottom>
            {t('Update Password')}
          </Typography>
          <Typography variant="subtitle2">
            {t(
              'Update account password here. Your current password is required.'
            )}
          </Typography>
        </DialogTitle>
        <Formik
          initialValues={{
            new_password: '',
            confirm_password: '',
            current_password: '',
            submit: null
          }}
          validationSchema={Yup.object().shape({
            new_password: Yup.string()
              .min(8)
              .required(t('Enter your new password.')),
            confirm_password: Yup.string()
              .min(8)
              .equalTo(Yup.ref('new_password'), 'Passwords must match')
              .required(t('Confirm your new password')),
            current_password: Yup.string()
              .min(8)
              .required(t('Enter your current password'))
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              await wait(3000);
              resetForm();
              await handlePasswordUpdate(
                _values,
                _values.submit,
                resetForm,
                setStatus,
                setSubmitting,
                setErrors
              );
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values
          }) => (
            <form className="needs-validation" onSubmit={handleSubmit}>
              <DialogContent
                dividers
                sx={{
                  p: 3
                }}
              >
                <Grid container spacing={3}>
                  <Grid container justifyContent="center">
                    {passwordChangeMsg}
                  </Grid>
                  <br />
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      error={Boolean(
                        touched.current_password && errors.current_password
                      )}
                      name="current_password"
                      // type={password_update_form.showPassword === 1 ? 'text' : 'password'}
                      type="password"
                      label="Current Password"
                      value={values.current_password}
                      sx={{
                        '& .MuiInputBase-input': {
                          color: theme === 'dark' ? 'white' : 'black'
                        }
                      }}
                      helperText={
                        touched.current_password && errors.current_password
                      }
                      // InputProps={{
                      // 	endAdornment: <InputAdornment position="end">
                      // 		<Tooltip title='Double click to toggle visibility' placement='right'>
                      // 			<IconButton
                      // 				aria-label="toggle password visibility"
                      // 				onClick={() => handleClickShowPassword(1)}
                      // 				onMouseDown={handleMouseDownPassword}
                      // 				edge="end"
                      // 			>
                      // 				{password_update_form.showPassword === 1 ? <VisibilityOff /> : <Visibility />}
                      // 			</IconButton>
                      // 		</Tooltip>
                      // 	</InputAdornment>
                      // }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      fullWidth
                      required
                      onChange={handleChange}
                      onBlur={handleBlur}
                      name="new_password"
                      type="password"
                      // type={password_update_form.showPassword === 2 ? 'text' : 'password'}
                      label="New Password"
                      value={values.new_password}
                      error={Boolean(
                        touched.new_password && errors.new_password
                      )}
                      helperText={touched.new_password && errors.new_password}
                      // InputProps={{
                      // 	endAdornment: <InputAdornment position="end">
                      // 		<Tooltip title='Double click to toggle visibility' placement='right'>
                      // 			<IconButton
                      // 				aria-label="toggle password visibility"
                      // 				onClick={() => handleClickShowPassword(2)}
                      // 				onMouseDown={handleMouseDownPassword}
                      // 				edge="end"
                      // 			>
                      // 				{password_update_form.showPassword === 2 ? <VisibilityOff /> : <Visibility />}
                      // 			</IconButton>
                      // 		</Tooltip>
                      // 	</InputAdornment>
                      // }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <TextField
                      onBlur={handleBlur}
                      onChange={handleChange}
                      required
                      name="confirm_password"
                      // type={password_update_form.showPassword === 3 ? 'text' : 'password'}
                      type="password"
                      label="Confirm New Password"
                      value={values.confirm_password}
                      error={Boolean(
                        touched.confirm_password && errors.confirm_password
                      )}
                      helperText={
                        touched.confirm_password && errors.confirm_password
                      }
                      fullWidth
                      // InputProps={{
                      //    endAdornment: <InputAdornment position="end">
                      // 	   <Tooltip title='Double click to toggle visibility' placement='right'>
                      // 		   <IconButton
                      // 			   aria-label="toggle password visibility"
                      // 			   onClick={() => handleClickShowPassword(3)}
                      // 			   onMouseDown={handleMouseDownPassword}
                      // 			   edge="end"
                      // 		   >
                      // 			   {password_update_form.showPassword === 3 ? <VisibilityOff /> : <Visibility />}
                      // 		   </IconButton>
                      // 	   </Tooltip>
                      //    </InputAdornment>
                      // }}
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <Box
                sx={{
                  display: { xs: 'block', sm: 'flex' },
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  p: 3
                }}
              >
                <Box>
                  <Button
                    fullWidth={mobile}
                    sx={{
                      mr: { xs: 0, sm: 2 },
                      my: { xs: 2, sm: 0 }
                    }}
                    variant="outlined"
                    color="primary"
                    onClick={() => toggleModal(props.open)}
                  >
                    Close
                  </Button>
                  <LoadingButton
                    disabled={Boolean(errors.submit) || isSubmitting}
                    fullWidth={mobile}
                    type="submit"
                    loading={isSubmitting || submittingPasswordChange}
                    loadingPosition="end"
                    endIcon={<Save />}
                    size="large"
                    variant="contained"
                  >
                    Submit Change
                  </LoadingButton>
                </Box>
              </Box>
            </form>
          )}
        </Formik>
      </Dialog>
    </div>
  );
};

UpdatePasswordModal.propTypes = {};

export default UpdatePasswordModal;
