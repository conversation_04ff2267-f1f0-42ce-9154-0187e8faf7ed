import React, {
  useContext,
  useEffect,
  useLayoutEffect,
  useMemo,
  useRef,
  useState
} from 'react';
import {
  Alert,
  AlertTitle,
  Box,
  Button,
  Dialog,
  DialogContent,
  DialogTitle,
  Grid,
  Slide,
  Typography,
  useMediaQuery
} from '@mui/material';
import { t } from 'i18next';
import '@google/model-viewer';
import { Player } from '@lottiefiles/react-lottie-player';
import { useTheme } from '@mui/material/styles';
import { validateBytes } from 'gltf-validator';
import Scrollbar from '../Scrollbar';
import useRefMounted from '../../hooks/useRefMounted';
import { RubixCube } from '../../utils/lottieAnimate';
import useHoistedUrls from '../../hooks/useHoistedUrls';
// import chessModel from '../../../public/static/images/models/scene.gltf'

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="down" ref={ref} {...props} />;
});

const ThreeDimensionalModelViewer = (props) => {
  const theme = useTheme();
  const isMounted = useRefMounted();
  const loaded = useRef(false);
  const mdScreen = useMediaQuery(theme.breakpoints.down('md'));
  const { getDeploymentUrl } = useHoistedUrls();
  const [loadError, setLoadError] = useState(false);
  const [modelViewer, setModelViewer] = useState(null);
  const [loadingModelViewer, setLoadingModelViewer] = useState(false);

  const checkModelFile = async (fileUrl) => {
    setLoadingModelViewer(true);
    fetch(fileUrl)
      .then((response) => response.arrayBuffer())
      .then((asset) => validateBytes(new Uint8Array(asset)))
      .then((report) => {
        console.log('Validation succeeded: ', report);
        if (report?.issues.numErrors >= 1) {
          let reportMsgs = report.issues.messages;
          let reportMsgCodes = reportMsgs.map((msg) => msg.code);

          if (reportMsgCodes.includes('INVALID_JSON')) {
            setLoadError(true);
          }
        } else {
          setLoadError(false);
        }
      })
      .catch((error) => {
        console.error('Validation failed: ', error);
        setLoadError(true);
      })
      .finally(() => setLoadingModelViewer(false));
  };

  const memoViewer = useMemo(() => {
    let model = null;
    setTimeout(() => {
      setModelViewer(document.querySelector('#model-viewer'));
    }, 700);

    return model;
  }, [window, isMounted, props.open]);

  useLayoutEffect(() => {
    if (item && item.url && props.open) {
      setLoadError(false);
      checkModelFile(`${getDeploymentUrl.core_account_url}${item?.url}`);
      return () => true;
    }
    return () => false;
  }, [props.open]);

  useEffect(() => {
    console.log('Error:', loadError);
    console.log('Viewer:', memoViewer);
    console.log('State Viewer:', modelViewer);
  }, [loadError, loaded, memoViewer, props.open, modelViewer]);

  const toggleModal = (state) => {
    props.onClose(!state);
  };

  let { item } = props;

  return (
    <Dialog
      fullScreen={mdScreen}
      fullWidth
      maxWidth="lg"
      onClose={props.onClose}
      open={props.open}
      className="text-center"
      TransitionComponent={Transition}
    >
      <DialogTitle className="bg-dark text-center justify-content-center text-white mb-3 pb-3">
        <Typography variant="h4" gutterBottom>
          {t('3D Model View')}
        </Typography>
        <Typography variant="subtitle2">
          {t('We have complied your model for viewing!')}
        </Typography>
      </DialogTitle>
      <DialogContent
        dividers
        sx={{
          p: 3
        }}
      >
        {loadError && (
          <Grid container justifyContent="center" py={3}>
            <Alert severity="error" variant="outlined">
              <AlertTitle>
                <Typography variant="h4">Failed to load model</Typography>
              </AlertTitle>
            </Alert>
          </Grid>
        )}
        <Box
          sx={{
            height: mdScreen ? '100%' : ' 75vh',
            display: 'flex',
            justifyContent: 'center'
          }}
        >
          <Scrollbar>
            <model-viewer
              id="model-viewer"
              bounds="tight"
              src={`${getDeploymentUrl?.core_account_url}${item?.url}`}
              ar="true"
              ar-modes="webxr scene-viewer quick-look"
              camera-controls="true"
              environment-image="neutral"
              poster="poster.webp"
              shadow-intensity="1"
              auto-rotate="true"
              style={{
                height: '100%',
                width: '100%'
              }}
            >
              {loadingModelViewer && (
                <Grid container justifyContent="center">
                  <Box
                    sx={{
                      height: '100%',
                      width: '100%',
                      justifyContent: 'center'
                    }}
                    pt={5}
                    p={5}
                  >
                    <Player
                      src={RubixCube()}
                      autoplay
                      loop
                      controls={false}
                      style={{
                        height: '50%',
                        width: '50%',
                        borderRadius: '1rem'
                      }}
                    />
                    <Typography variant="h3" sx={{ textAlign: 'center' }}>
                      Verifying Model...
                    </Typography>
                  </Box>
                </Grid>
              )}

              <div className="progress-bar hide" slot="progress-bar">
                <div className="update-bar" />
              </div>
            </model-viewer>
          </Scrollbar>
        </Box>
      </DialogContent>
      <Box
        sx={{
          display: { xs: 'block', sm: 'flex' },
          alignItems: 'center',
          justifyContent: 'flex-end',
          p: 3
        }}
      >
        <Box>
          <Button
            fullWidth={mdScreen}
            sx={{
              mr: { xs: 0, sm: 2 },
              my: { xs: 2, sm: 0 }
            }}
            variant="outlined"
            color="primary"
            onClick={() => toggleModal(props.open)}
          >
            Close
          </Button>
        </Box>
      </Box>
    </Dialog>
  );
};

ThreeDimensionalModelViewer.propTypes = {};

export default ThreeDimensionalModelViewer;
