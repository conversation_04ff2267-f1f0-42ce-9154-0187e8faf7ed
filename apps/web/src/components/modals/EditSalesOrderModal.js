import React, { useContext, useEffect, useState } from 'react';
import {
  Box,
  Button,
  ButtonGroup,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogTitle,
  FormControl,
  FormHelperText,
  Grid,
  InputLabel,
  MenuItem,
  Select,
  Slide,
  Table,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery
} from '@mui/material';
import { useTheme } from '@mui/material/styles';
import { Formik } from 'formik';
import * as Yup from 'yup';
import { t } from 'i18next';
import numeral from 'numeral';
import { DataGridPro } from '@mui/x-data-grid-pro';
import Grow from '@mui/material/Grow';
import { useSnackbar } from 'notistack';
import {
  AssignmentOutlined,
  CopyAllOutlined,
  DeleteOutlined
} from '@mui/icons-material';
import wait from '../../utils/wait';
import SalesOrderAutoComplete from '../containers/SalesOrderAutoComplete';
import { SettingsContext } from '../../contexts/SettingsContext';
import useAuth from '../../hooks/useAuth';

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

const EditSalesOrderModal = (props) => {
  const [settings] = useContext(SettingsContext);
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const { enqueueSnackbar } = useSnackbar();
  const { user } = useAuth();
  const [salesOrderComments, setSalesOrderComments] = useState('');
  const [salesOrderShipping, setSalesOrderShipping] = useState('');
  const [salesOrderPo, setSalesOrderPo] = useState('');

  useEffect(() => {
    console.log(
      'Props Updated:',
      props.shipToAddress,
      props.comments,
      props.poNumber
    );
    setSalesOrderComments(props.comments);
    setSalesOrderPo(props.poNumber);
    setSalesOrderShipping(props.shipToAddress);
  }, [props.shipToAddress, props.comments, props.poNumber]);

  const handle_sales_order_update = async (_values) => {
    await props.updateOrderOn(_values);
  };

  return (
    <div>
      <Dialog
        fullScreen
        open={props.isOpen}
        onClose={props.toggle}
        TransitionComponent={Transition}
      >
        <DialogTitle
          sx={{
            p: 3
          }}
        >
          <Typography variant="h4" gutterBottom>
            {t(props.headerTitle)}
          </Typography>
          <Typography variant="subtitle2">
            {t('Use this modal dialog to edit an existing sales order')}
          </Typography>
        </DialogTitle>
        {/*
				salesOrderComments
salesOrderShipping
salesOrderPo
				*/}
        <Formik
          enableReinitialize
          initialValues={{
            items: props.salesOrderItems,
            po_number: salesOrderPo,
            ship_to_address: salesOrderShipping,
            comments: salesOrderComments,
            entity: user.customer.fields.id, // provided user id
            submit: null
          }}
          validationSchema={Yup.object().shape({
            po_number: Yup.string().max(255),
            comments: Yup.string().max(255),
            ship_to_address: Yup.string().required(
              t('A Shipping Address is Required')
            ),
            items: Yup.array().required(
              t('Items are required in order to create an order')
            )
          })}
          onSubmit={async (
            _values,
            { resetForm, setErrors, setStatus, setSubmitting }
          ) => {
            try {
              await wait(3000);
              resetForm();
              await handle_sales_order_update(_values).then(() => {
                setStatus({ success: true });
                setSubmitting(false);
              });
            } catch (err) {
              console.error(err);
              setStatus({ success: false });
              setErrors({ submit: err.message });
              setSubmitting(false);
            }
          }}
        >
          {({
            errors,
            handleBlur,
            handleChange,
            handleSubmit,
            isSubmitting,
            touched,
            values
          }) => (
            <form onSubmit={handleSubmit}>
              <DialogContent
                dividers
                sx={{
                  p: 3
                }}
              >
                <Grid container spacing={3}>
                  <Grid item xs={12} md={6}>
                    <Box pb={1}>
                      <b>{t('Shipping Address')}:</b>
                    </Box>
                    <FormControl fullWidth>
                      <InputLabel id="demo-multiple-chip-label">
                        {t('Select Address')}
                      </InputLabel>
                      <Select
                        error={Boolean(
                          touched.ship_to_address && errors.ship_to_address
                        )}
                        name="ship_to_address"
                        label="Ship To Address"
                        labelId="ship-to-select-label"
                        selected="Select Delivery Address"
                        onBlur={handleBlur}
                        // options={sales_order_delivery_addresses}
                        onChange={handleChange}
                        fullWidth
                        value={values.ship_to_address}
                        sx={{
                          '& .MuiInputBase-input': {
                            // color: theme === 'dark' ? 'white' : 'black'
                          }
                        }}
                      >
                        <MenuItem value="">None</MenuItem>
                        {props.salesOrderDeliveryAddresses.length !== 0 ? (
                          props.salesOrderDeliveryAddresses.map(
                            (address, i) => (
                              <MenuItem
                                key={`address-${i}`}
                                value={address.value.addrtext_initialvalue}
                              >
                                {address.text}
                              </MenuItem>
                            )
                          )
                        ) : (
                          <></>
                        )}
                      </Select>
                      <FormHelperText>
                        {touched.ship_to_address && errors.ship_to_address}
                      </FormHelperText>
                    </FormControl>
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <Box pb={1}>
                      <b>{t('PO Number')}:</b>
                    </Box>
                    <TextField
                      label="PO#"
                      name="po_number"
                      fullWidth
                      type="text"
                      onChange={handleChange}
                      value={values.po_number}
                      sx={{
                        '& .MuiInputBase-input': {
                          // color: theme === 'dark' ? 'white' : 'black'
                        }
                      }}
                    />
                  </Grid>
                  <Grid item xs={12}>
                    <Box pb={1}>
                      <b>{t('Item Select')}:</b>
                    </Box>
                    <SalesOrderAutoComplete
                      data={
                        props.itemCatalogLoading
                          ? []
                          : props.itemCatalog?.results?.rows
                      }
                      loading={
                        props.itemCatalogLoading || !props.itemCatalog?.results
                      }
                      disabled={
                        !props.itemCatalog &&
                        props.itemCatalog?.results?.rows.length === 0
                      }
                      onItemSelect={(e, value) =>
                        props.handleItemAddSalesOrderSelect(e, value)
                      }
                      theme="light"
                      imagesEnabled={
                        settings?.custrecord_ng_pg_item_images_enabled === 'T'
                      }
                    />
                  </Grid>
                </Grid>
              </DialogContent>
              <TableContainer>
                {props.salesItemToolbarVisible &&
                props.selectedSalesOrderRows.size !== 0 ? (
                  <Box
                    sx={{
                      display: { xs: 'block', sm: 'flex' },
                      alignItems: 'center',
                      justifyContent: 'flex-end',
                      p: 3
                    }}
                  >
                    <Grow in>
                      <ButtonGroup
                        className="float-right"
                        aria-label="Bulk item actions"
                      >
                        <Tooltip title="Update Quantity" placement="top" arrow>
                          <Button
                            className="rounded-l-full"
                            id="impo"
                            size="sm"
                            variant="outlined"
                            color="info"
                            onClick={() => props.handleBulkQuantityUpdate()}
                          >
                            <AssignmentOutlined />
                          </Button>
                        </Tooltip>
                        {props.selectedSalesOrderRows.size === 1 && (
                          <Tooltip title="Copy Item" placement="top" arrow>
                            <Button
                              size="sm"
                              variant="outlined"
                              onClick={() => props.handleItemCopy()}
                            >
                              <CopyAllOutlined />
                            </Button>
                          </Tooltip>
                        )}
                        <Tooltip title="Delete Items" placement="top" arrow>
                          <Button
                            className="rounded-r-md"
                            onClick={() => props.handleBulkItemDelete()}
                            size="sm"
                            variant="outlined"
                            color="error"
                          >
                            <DeleteOutlined />
                          </Button>
                        </Tooltip>
                      </ButtonGroup>
                    </Grow>
                  </Box>
                ) : null}
                <DataGridPro
                  onCellEditCommit={(params, event, details) =>
                    props.itemCellEditCommit(params, event, details)
                  }
                  columns={props.salesOrderItemColumns}
                  rows={props.salesOrderItems}
                  checkboxSelection
                  // disableSelectionOnClick
                  onCellClick={(params, e, details) =>
                    console.log(
                      'Clicked:',
                      params,
                      '\nEvent:',
                      e,
                      '\nDetails:',
                      details,
                      '\nSales order items:',
                      props.salesOrderItems
                    )
                  }
                  onSelectionModelChange={(newSelectionModel) =>
                    props.setSelectedSalesOrderRows(
                      () => new Set(newSelectionModel)
                    )
                  }
                  selectionModel={[...props.selectedSalesOrderRows]}
                  rowsPerPageOptions={[5]}
                  // onRowsChange={handle_rows_change}
                  // onRowClick={(row, e) => handle_rows_select(row, e)}
                  disableSelectionOnClick
                  rowHeight={80}
                  density="comfortable"
                  style={{ height: '60vh' }}
                  // selectedRows={selectedRows}
                  // onSelectedRowsChange={setSelectedRows}
                  /* eslint-disable-next-line react/jsx-no-bind */
                  getRowId={(row) => props.itemRowKeyGetter(row)}
                  // summaryRows={sales_summary_rows}
                />
                <Table>
                  <TableFooter>
                    {settings &&
                    settings.custrecord_ng_pg_hide_dollar_calcs !== 'T' ? (
                      <TableRow>
                        <TableCell colSpan={4} align="right">
                          <Typography
                            gutterBottom
                            variant="caption"
                            color="text.secondary"
                            fontWeight="bold"
                          >
                            {t('Total')}:
                          </Typography>
                          <Typography variant="h3" fontWeight="bold">
                            {numeral(props.salesOrderTotal).format(`$0,0.00`)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ) : null}
                  </TableFooter>
                </Table>
              </TableContainer>
              <Box px={3} pt={3}>
                <TextField
                  multiline
                  fullWidth
                  label={t('Comments')}
                  name="comments"
                  minRows={3}
                  maxRows={8}
                  onChange={handleChange}
                  placeholder={t(
                    'Write here any additional information you might have...'
                  )}
                  value={values.comments}
                />
              </Box>
              <Box
                sx={{
                  display: { xs: 'block', sm: 'flex' },
                  alignItems: 'center',
                  justifyContent: 'flex-end',
                  p: 3
                }}
              >
                <Box>
                  <Button
                    fullWidth={mobile}
                    sx={{
                      mr: { xs: 0, sm: 2 },
                      my: { xs: 2, sm: 0 }
                    }}
                    color="secondary"
                    variant="outlined"
                    onClick={props.toggle}
                  >
                    {t('Close')}
                  </Button>
                  <Button
                    color="warning"
                    fullWidth={mobile}
                    type="submit"
                    startIcon={
                      isSubmitting ? <CircularProgress size="1rem" /> : null
                    }
                    disabled={Boolean(errors.submit) || isSubmitting}
                    variant="contained"
                    size="large"
                  >
                    {t('Update Order')}
                  </Button>
                </Box>
              </Box>
            </form>
          )}
        </Formik>
      </Dialog>
    </div>
  );
};

EditSalesOrderModal.propTypes = {};

export default EditSalesOrderModal;
