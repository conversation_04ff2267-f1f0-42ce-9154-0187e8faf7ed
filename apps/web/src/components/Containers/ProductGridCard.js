import React, { useState } from 'react';
import PropTypes from 'prop-types';
import {
  Avatar,
  Box,
  Button,
  Card,
  Chip,
  FormControl,
  IconButton,
  Stack,
  styled,
  TextField,
  Tooltip,
  Typography,
  useMediaQuery,
  Collapse,
  alpha
} from '@mui/material';

import {
  AddShoppingCartTwoTone,
  LabelTwoTone,
  HistoryTwoTone,
  ExpandMoreOutlined,
  InventoryOutlined,
  CategoryOutlined
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import useHoistedUrls from '../../hooks/useHoistedUrls';
import useSettings from '../../hooks/useSettings';
import useAuth from '../../hooks/useAuth';
import { useTheme } from '@mui/material/styles';

import { useTranslation } from 'react-i18next';
import OrderedItemHistoryOrderModal from '../modals/OrderedItemHistoryOrderModal';

const StyledCard = styled(Card)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  flexDirection: 'column',
  position: 'relative',
  overflow: 'hidden',
  transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
  cursor: 'pointer',
  '&:hover': {
    transform: 'translateY(-4px)',
    boxShadow: theme.shadows[8],
    '& .product-image': {
      transform: 'scale(1.05)'
    },
    '& .quick-view-overlay': {
      opacity: 1
    }
  }
}));

const ImageContainer = styled(Box)(({ theme }) => ({
  position: 'relative',
  paddingTop: '75%', // 4:3 Aspect Ratio
  overflow: 'hidden',
  backgroundColor: theme.palette.background.default,
  '& .product-image': {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    transition: 'transform 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
  }
}));

const QuickViewOverlay = styled(Box)(({ theme }) => ({
  position: 'absolute',
  top: 0,
  left: 0,
  right: 0,
  bottom: 0,
  backgroundColor: alpha(theme.palette.background.paper, 0.8),
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  opacity: 0,
  transition: 'opacity 0.3s ease',
  backdropFilter: 'blur(4px)'
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  flex: 1,
  display: 'flex',
  flexDirection: 'column',
  padding: theme.spacing(2),
  gap: theme.spacing(1.5)
}));

const StockBadge = styled(Box)(({ theme, inStock }) => ({
  position: 'absolute',
  top: theme.spacing(1),
  right: theme.spacing(1),
  backgroundColor:
    inStock > 0
      ? alpha(theme.palette.success.main, 0.9)
      : alpha(theme.palette.error.main, 0.9),
  color: theme.palette.common.white,
  padding: theme.spacing(0.5, 1),
  borderRadius: theme.shape.borderRadius,
  fontSize: '0.75rem',
  fontWeight: 600,
  backdropFilter: 'blur(8px)',
  boxShadow: theme.shadows[2]
}));

const QuantityContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  marginTop: 'auto'
}));

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiOutlinedInput-root': {
    borderRadius: theme.shape.borderRadius,
    '& input': {
      padding: theme.spacing(1),
      textAlign: 'center',
      fontSize: '0.875rem'
    }
  }
}));

const AddToCartButton = styled(Button)(({ theme }) => ({
  flex: 1,
  borderRadius: theme.shape.borderRadius,
  textTransform: 'none',
  fontWeight: 600,
  transition: 'all 0.2s ease',
  '&:hover': {
    transform: 'scale(1.02)'
  }
}));

const ExpandButton = styled(IconButton)(({ theme, expanded }) => ({
  transform: expanded ? 'rotate(180deg)' : 'rotate(0deg)',
  transition: theme.transitions.create('transform', {
    duration: theme.transitions.duration.shortest
  }),
  padding: theme.spacing(0.5)
}));

const ProductGridCard = ({
  product,
  handleItemQuantityChange,
  showProductDetails,
  quantityValue,
  setItemQuantityInCart
}) => {
  const { getDeploymentUrl } = useHoistedUrls();
  const [settings] = useSettings();
  const { currentProject } = useAuth();
  const theme = useTheme();
  const { t } = useTranslation();
  const mobile = useMediaQuery(theme.breakpoints.down('md'));
  const [historyOpen, setHistoryOpen] = useState(false);
  const [expanded, setExpanded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const getImageFileAttachment = (url) => {
    if (url && !imageError) {
      return `${getDeploymentUrl.core_account_url}${url}`;
    }
    return `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_item_fallback_img_url}`;
  };

  const toggleOrderHistory = (open) => {
    setHistoryOpen(!open);
  };

  const handleExpandClick = (e) => {
    e.stopPropagation();
    setExpanded(!expanded);
  };

  const stockValue =
    product?.itemType === 'InvtPart'
      ? product?.stock?.item?.available
      : product?.stock?.item?.availableTotal;

  const isInStock = stockValue > 0;

  return (
    <>
      <OrderedItemHistoryOrderModal
        productId={product.id}
        isOpen={historyOpen}
        toggle={() => toggleOrderHistory(historyOpen)}
      />

      <motion.div
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        transition={{ duration: 0.3 }}
      >
        <StyledCard>
          {/* Stock Badge */}
          <AnimatePresence>
            {stockValue !== undefined && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.2 }}
              >
                <StockBadge inStock={stockValue}>
                  {t('In Stock')}: {stockValue || 0}
                </StockBadge>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Image Section */}
          <ImageContainer onClick={() => showProductDetails(product)}>
            <img
              className="product-image"
              src={getImageFileAttachment(
                product.images?.custitem_ng_eh_item_primary_image
              )}
              alt={product.displayname}
              onError={() => setImageError(true)}
            />
            <QuickViewOverlay className="quick-view-overlay">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  variant="contained"
                  size="large"
                  sx={{
                    borderRadius: 2,
                    px: 4,
                    fontWeight: 600
                  }}
                >
                  Quick View
                </Button>
              </motion.div>
            </QuickViewOverlay>
          </ImageContainer>

          {/* Content Section */}
          <ContentContainer>
            {/* Category */}
            {(product.custitem_ng_eh_item_category?.title ||
              product.custitem_ng_eh_item_subcategory?.title) && (
              <Box display="flex" alignItems="center" gap={0.5}>
                <CategoryOutlined
                  sx={{ fontSize: 14, color: 'text.secondary' }}
                />
                <Typography variant="caption" color="text.secondary">
                  {[
                    product.custitem_ng_eh_item_category?.title,
                    product.custitem_ng_eh_item_subcategory?.title
                  ]
                    .filter(Boolean)
                    .join(' • ')}
                </Typography>
              </Box>
            )}

            {/* Product Name */}
            <Typography
              variant="h6"
              fontWeight={600}
              sx={{
                lineHeight: 1.2,
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}
            >
              {product.displayname}
            </Typography>

            {/* Product ID */}
            <Typography variant="body2" color="text.secondary">
              {product.itemid}
            </Typography>

            {/* Tags */}
            <Stack direction="row" spacing={1} flexWrap="wrap" gap={0.5}>
              {product.metadata?.portalTag && (
                <motion.div
                  whileHover={{ scale: 1.05 }}
                  transition={{ type: 'spring', stiffness: 400 }}
                >
                  <Chip
                    size="small"
                    icon={<LabelTwoTone sx={{ fontSize: 16 }} />}
                    color="primary"
                    label={product.metadata.portalTag}
                    sx={{ fontWeight: 500 }}
                  />
                </motion.div>
              )}
              {product?.itemType === 'InvtPart' && (
                <Tooltip
                  title={t(
                    'Expendable items have a stock quantity and are not reserved by a date range.'
                  )}
                  placement="top"
                  arrow
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    transition={{ type: 'spring', stiffness: 400 }}
                  >
                    <Chip
                      size="small"
                      icon={<InventoryOutlined sx={{ fontSize: 16 }} />}
                      color="info"
                      label="Perishable"
                      sx={{ fontWeight: 500 }}
                    />
                  </motion.div>
                </Tooltip>
              )}
            </Stack>

            {/* Expandable Description */}
            {product.salesdescription && (
              <Box>
                <Box
                  display="flex"
                  alignItems="center"
                  justifyContent="space-between"
                >
                  <Typography variant="body2" fontWeight={500}>
                    Product Details
                  </Typography>
                  <ExpandButton
                    expanded={expanded}
                    onClick={handleExpandClick}
                    size="small"
                  >
                    <ExpandMoreOutlined />
                  </ExpandButton>
                </Box>
                <Collapse in={expanded} timeout="auto" unmountOnExit>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ mt: 1 }}
                  >
                    {product.salesdescription}
                  </Typography>
                </Collapse>
              </Box>
            )}

            {/* Stock Information */}
            <Box
              sx={{
                p: 1.5,
                backgroundColor: 'background.paper',
                borderRadius: 1,
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Typography
                variant="body2"
                fontWeight={500}
                color="text.secondary"
              >
                {product?.itemType === 'InvtPart' ? (
                  <>
                    {t('In Stock')}:{' '}
                    <Typography
                      component="span"
                      color="text.primary"
                      variant="h6"
                      fontWeight={600}
                    >
                      {product?.stock?.item
                        ? `${product?.stock?.item.available}`
                        : 'Loading...'}
                    </Typography>
                  </>
                ) : (
                  <>
                    {t('Units Available')}:{' '}
                    <Typography
                      component="span"
                      color="text.primary"
                      variant="h6"
                      fontWeight={600}
                    >
                      {product?.stock?.item
                        ? `${product?.stock?.item.available} / ${product?.stock?.item.availableTotal}`
                        : 'Loading...'}
                    </Typography>
                  </>
                )}
              </Typography>
            </Box>

            {/* Quantity and Add to Cart */}
            <QuantityContainer>
              <StyledTextField
                variant="outlined"
                size="small"
                label="Qty"
                id={`quantity-item-${product.id}`}
                onChange={(e) =>
                  handleItemQuantityChange(product.id, e.target.value)
                }
                value={quantityValue}
                InputProps={{
                  type: 'number',
                  inputProps: {
                    min: 1,
                    'aria-valuemin': '1'
                  }
                }}
                sx={{ width: 80 }}
              />

              <Tooltip
                arrow
                title={
                  !currentProject ? (
                    <Box textAlign="center">
                      <Typography
                        variant="subtitle2"
                        fontWeight={600}
                        color="inherit"
                      >
                        No Project Selected
                      </Typography>
                      <Typography variant="caption" color="inherit">
                        Please select a project to begin shopping
                      </Typography>
                    </Box>
                  ) : (
                    t('Add to cart')
                  )
                }
              >
                <span style={{ flex: 1 }}>
                  <AddToCartButton
                    variant="contained"
                    fullWidth
                    disabled={!currentProject || !isInStock}
                    onClick={() => setItemQuantityInCart(product)}
                    startIcon={<AddShoppingCartTwoTone />}
                  >
                    {mobile ? '' : 'Add to Cart'}
                  </AddToCartButton>
                </span>
              </Tooltip>

              <Tooltip title="View ordering history" placement="top" arrow>
                <motion.div
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                >
                  <IconButton
                    size="small"
                    onClick={() => toggleOrderHistory(historyOpen)}
                    sx={{ ml: 0.5 }}
                  >
                    <HistoryTwoTone />
                  </IconButton>
                </motion.div>
              </Tooltip>
            </QuantityContainer>
          </ContentContainer>
        </StyledCard>
      </motion.div>
    </>
  );
};

ProductGridCard.defaultProps = {
  product: {}
};

ProductGridCard.propTypes = {
  product: PropTypes.object.isRequired,
  handleItemQuantityChange: PropTypes.func.isRequired,
  showProductDetails: PropTypes.func.isRequired,
  quantityValue: PropTypes.number.isRequired,
  setItemQuantityInCart: PropTypes.func.isRequired
};

export default ProductGridCard;
