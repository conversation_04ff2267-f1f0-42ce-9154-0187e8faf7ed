import React, { Fragment, useContext, useState } from 'react';
import PropTypes from 'prop-types';
import {
  alpha,
  Button,
  IconButton,
  Menu,
  MenuItem,
  Typography,
  useMediaQuery,
  useTheme
} from '@mui/material';
import MoreHorizTwoToneIcon from '@mui/icons-material/MoreHorizTwoTone';
import ThreeDRotationTwoToneIcon from '@mui/icons-material/ThreeDRotationTwoTone';
import { LaunchTwoTone } from '@mui/icons-material';
import useHoistedUrls from '../../hooks/useHoistedUrls';

const FileOptionsMenu = (props) => {
  const { getDeploymentUrl } = useHoistedUrls();
  const [fileAnchorEl, setFileAnchorEl] = useState(null);
  const theme = useTheme();
  const mobile = useMediaQuery(theme.breakpoints.down('sm'));
  const openFileMenu = Boolean(fileAnchorEl);
  const handleFileMenuClick = (event) => {
    setFileAnchorEl(event.currentTarget);
  };
  const handleFileMenuClose = () => {
    setFileAnchorEl(null);
  };

  const viewFileAttachment = (fileAtt) => {
    window.open(`${getDeploymentUrl.core_account_url}${fileAtt.url}`);
    handleFileMenuClose();
  };

  let { item } = props;

  return (
    <Fragment key={item?.internalid}>
      {item && ['glb', 'gltf'].includes(item?.extension) ? (
        <Button
          onClick={() => props.setModelViewOpen(true)}
          sx={{
            alignSelf: 'center',
            fontWeight: 'normal',
            backgroundColor: `${alpha(theme.colors.alpha.trueWhite[100], 0)}`,
            color: `${theme.colors.alpha.trueWhite[70]}`,

            '&:hover': {
              backgroundColor: `${alpha(
                theme.colors.alpha.trueWhite[100],
                0.2
              )}`,
              color: `${theme.colors.alpha.trueWhite[100]}`
            }
          }}
        >
          <ThreeDRotationTwoToneIcon />
          &nbsp;
          {!mobile && <Typography>View 3D Design</Typography>}
        </Button>
      ) : null}
      <IconButton
        key={`file-item-menu-button-${item?.internalid}`}
        id={`file-item-menu-button-${item?.internalid}`}
        aria-controls={
          openFileMenu ? `file-item-menu-${item?.internalid}` : undefined
        }
        aria-haspopup="true"
        aria-expanded={openFileMenu ? 'true' : undefined}
        onClick={handleFileMenuClick}
        size="small"
        sx={{
          alignSelf: 'center',
          fontWeight: 'normal',
          backgroundColor: `${alpha(theme.colors.alpha.trueWhite[100], 0)}`,
          color: `${theme.colors.alpha.trueWhite[70]}`,

          '&:hover': {
            backgroundColor: `${alpha(theme.colors.alpha.trueWhite[100], 0.2)}`,
            color: `${theme.colors.alpha.trueWhite[100]}`
          }
        }}
      >
        <MoreHorizTwoToneIcon />
      </IconButton>
      <Menu
        key={`file-item-menu-${item?.internalid}`}
        id={`file-item-menu-${item?.internalid}`}
        anchorEl={fileAnchorEl}
        open={openFileMenu}
        onClose={handleFileMenuClose}
        MenuListProps={{
          'aria-labelledby': `file-item-menu-button-${item?.internalid}`,
          key: `file-item-menu-button-${item?.internalid}`
        }}
      >
        <MenuItem
          key={`file-item-menu-${item?.internalid}-${item?.internalid}`}
          onClick={() => viewFileAttachment(item)}
        >
          <LaunchTwoTone />
          &nbsp;View File
        </MenuItem>
      </Menu>
    </Fragment>
  );
};

FileOptionsMenu.propTypes = {
  // eslint-disable-next-line react/no-unused-prop-types
  item: PropTypes.object.isRequired,
  setModelViewOpen: PropTypes.func.isRequired
};

export default FileOptionsMenu;
