import React from 'react';
import throttle from 'lodash/throttle';
import parse from 'autosuggest-highlight/parse';
import { Autocomplete, Box, Grid, TextField, Typography } from '@mui/material';
import { LocationOnTwoTone } from '@mui/icons-material';

const autocompleteService = { current: null };

export const LocationAutoComplete = (props) => {
  const [inputValue, setInputValue] = React.useState('');
  const [options, setOptions] = React.useState([]);

  // if (typeof window !== 'undefined' && !loaded.current) {
  //     if (!document.querySelector('#google-maps')) {
  //         loadScript(
  //             'https://maps.googleapis.com/maps/api/js?key=AIzaSyDTZ5dA53eNtQbVDdMpWtrcW7MJFUVEZ8I&libraries=places',
  //             document.querySelector('head'),
  //             'google-maps',
  //         );
  //     }
  //
  //     loaded.current = true;
  // }

  const fetchMap = React.useMemo(
    () =>
      throttle((request, callback) => {
        autocompleteService.current.getPlacePredictions(request, callback);
      }, 200),
    []
  );

  React.useEffect(() => {
    let active = true;

    if (!autocompleteService.current && window.google) {
      // console.log('Service: ', autocompleteService)
      let addressField = document.querySelector('#google-map-autocomplete');
      autocompleteService.current =
        new window.google.maps.places.AutocompleteService(addressField, {
          componentRestrictions: { country: ['us', 'ca'] },
          fields: ['address_components', 'geometry'],
          types: ['address']
        });
    }
    if (!autocompleteService.current) {
      return undefined;
    }

    if (inputValue === '') {
      setOptions([]);
      return undefined;
    }

    fetchMap({ input: inputValue }, (results) => {
      if (active) {
        let newOptions = [];

        if (results) {
          newOptions = [...newOptions, ...results];
        }

        setOptions(newOptions);
      }
    });

    return () => {
      active = false;
    };
  }, [inputValue, fetchMap]);

  return (
    <Autocomplete
      id="google-map-autocomplete"
      helperText={props.helperText}
      getOptionLabel={(option) => {
        // Value selected with enter, right from the input
        if (typeof option === 'string') {
          return option;
        }
        // Add "xxx" option created dynamically
        if (option.inputValue) {
          console.error('Input value: ', option);
          // return option.description;
        }
        // Regular option
        return option.description;
      }}
      /* filterOptions={(options, params) => {
                const filtered = filter(options, params);

                // Suggest the creation of a new value
                if (params.inputValue !== '') {
                    filtered.push({
                        structured_formatting: {
                            main_text_matched_substrings: params.inputValue,
                        },
                        description: `Add "${params.inputValue}"`,
                    });
                }

                return filtered;
            }} */
      filterOptions={(x) => x}
      options={options}
      autoComplete
      freeSolo
      includeInputInList
      filterSelectedOptions
      value={props.addressName}
      name={props.name}
      onChange={async (event, newValue) => {
        console.log('Change Event: ', event);
        console.log('Change newValue: ', newValue);
        if (typeof newValue === 'string') {
          props.setAddressName(event, newValue);
          setOptions([newValue, ...options]);
        } /* else if (newValue && newValue.inputlue) {
                    // Create a new value from the user input
                    setOptions( [{
                        structured_formatting: {
                            main_text_matched_substrings: newValue
                        }
                    }, ...options]);
                    props.setAddressName(event, newValue)
                } */ else {
          setOptions(options);
          props.setAddressName(event, newValue);
        }
      }}
      onInputChange={(event, newInputValue) => {
        setInputValue(newInputValue);
        props.setAddress1(newInputValue);
      }}
      renderInput={(params) => {
        // console.log('input render params: ', params)
        return (
          <TextField
            {...params}
            label={props.label}
            fullWidth
            variant="outlined"
            error={props.error}
            helperText={props.helperText}
          />
        );
      }}
      renderOption={(params, option) => {
        console.log('Render option:', option);
        const matches =
          option.structured_formatting.main_text_matched_substrings;
        const parts = parse(
          option.structured_formatting.main_text,
          matches.map((match) => [match.offset, match.offset + match.length])
        );
        return (
          <Grid {...params} container alignItems="center" spacing={3}>
            <Grid item>
              <LocationOnTwoTone />
            </Grid>
            <Grid item xs>
              {parts.map((part, index) => (
                <Typography
                  key={index}
                  component="span"
                  style={{ fontWeight: 300, fontFamily: 'Roboto,sans-serif' }}
                  gutterBottom
                >
                  {part.text}
                </Typography>
              ))}
              <Typography variant="subtitle2">
                <Box tag="footer" mb={3} className="blockquote-footer">
                  {option.structured_formatting.secondary_text}
                </Box>
              </Typography>
            </Grid>
          </Grid>
        );
      }}
    />
  );
};
