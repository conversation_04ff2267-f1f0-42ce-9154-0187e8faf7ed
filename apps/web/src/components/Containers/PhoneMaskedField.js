import React from 'react';
import PropTypes from 'prop-types';
import { IMaskInput } from 'react-imask';
import { TextField } from '@mui/material';

const TextMaskCustom = React.forwardRef(function TextMaskCustom(props, ref) {
  const { onChange, ...other } = props;
  return (
    <IMaskInput
      {...other}
      mask="(*************"
      definitions={{
        '#': /[1-9]/
      }}
      inputRef={ref}
      onAccept={(value) => onChange({ target: { name: props.name, value } })}
      overwrite
    />
  );
});

TextMaskCustom.propTypes = {
  inputRef: PropTypes.func
};

export default function PhoneMaskedField(props) {
  return (
    <>
      <TextField
        error={props.error}
        helperText={props.helperText}
        fullWidth
        label="Phone"
        name={props.name}
        id="formatted-text-mask-input"
        InputProps={{
          inputComponent: TextMaskCustom,
          onChange: props.onChange,
          value: props.value
        }}
      />
    </>
  );
}
