import { Box, Card, Link, styled, Typography } from '@mui/material';
import useAuth from '../../hooks/useAuth';
import packageJson from '../../../package.json';

const FooterWrapper = styled(Card)(
  ({ theme }) => `
        border-radius: 0;
        margin-top: ${theme.spacing(4)};
`
);

function Footer() {
  const { user } = useAuth();

  return (
    <FooterWrapper className="footer-wrapper">
      <Box
        p={4}
        display={{ xs: 'block', md: 'flex' }}
        alignItems="center"
        textAlign={{ xs: 'center', md: 'left' }}
        justifyContent="space-between"
      >
        <Box>
          <Typography variant="subtitle1">
            &copy; {new Date().getFullYear()} - {user.companyName}
          </Typography>
        </Box>
        <Typography
          sx={{
            pt: { xs: 2, md: 0 }
          }}
          variant="subtitle1"
          noWrap
          gutterBottom
        >
          Powered by{' '}
          <Link
            href="https://newgennow.com"
            target="_blank"
            rel="noopener noreferrer"
          >
            ConventionSuite{' '}
            <span role="img" aria-label="ConventionSuite Lighting">
              ⚡
            </span>
          </Link>
          &nbsp;
          <Typography
            fontSize={11}
            fontWeight="lighter"
            component="span"
            variant="subtitle1"
          >
            - v{packageJson.version}
          </Typography>
        </Typography>
      </Box>
    </FooterWrapper>
  );
}

export default Footer;
