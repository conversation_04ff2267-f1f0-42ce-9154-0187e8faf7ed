import React, { useEffect } from 'react';
import PropTypes from 'prop-types';
import { Fill<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>pin<PERSON>, PongSpinner } from 'react-spinners-kit';
import { Backdrop, Container, Grid, Typography } from '@mui/material';
import { makeStyles } from '@mui/styles';
import { useTheme } from '@mui/material/styles';

const useStyles = makeStyles(() => ({
  backdrop: {
    zIndex: 10000,
    color: '#fff'
  }
}));

const BackdropLoader = (props) => {
  const backdropClasses = useStyles();
  const theme = useTheme();

  useEffect(() => {
    console.log('Theme hook:', theme);
  }, [theme]);

  return (
    <Backdrop
      style={{ zIndex: 1000 }}
      className={backdropClasses.backdrop}
      open={props.visible /* true backdrop.visible */}
    >
      <Container>
        <Grid container justifyContent="center">
          <Grid container justifyContent="center">
            <span>
              {props.spinner ? (
                props.waitType === 'create' ? (
                  <FillSpinner
                    backColor="#cfd8dc"
                    color={theme.palette?.primary.main}
                    loading
                    size={30}
                  />
                ) : props.waitType === 'validate' ? (
                  <PongSpinner
                    backColor="#cfd8dc"
                    color={theme.palette?.warning.main}
                    loading
                    size={60}
                  />
                ) : (
                  <GooSpinner
                    color={theme.palette?.primary.main}
                    loading
                    size={60}
                  />
                )
              ) : null}
            </span>
          </Grid>
        </Grid>
        <Grid
          container
          className="text-center"
          justifyContent="center"
          style={{ marginTop: '1.5vh' }}
        >
          <Typography fontSize="1rem">{props.message}</Typography>
        </Grid>
      </Container>
    </Backdrop>
  );
};

BackdropLoader.propTypes = {
  message: PropTypes.oneOfType([PropTypes.string, PropTypes.elementType]),
  spinner: PropTypes.bool.isRequired,
  waitType: PropTypes.string,
  visible: PropTypes.bool
};

export default BackdropLoader;
