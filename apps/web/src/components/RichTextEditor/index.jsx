import ReactQuill from 'react-quill';
import { Box, styled } from '@mui/material';
import PropTypes from 'prop-types';

const EditorWrapper = styled(Box)(
  ({ theme }) => `
    .ql-editor {
      min-height: 100px;
      max-height: 250px;
      font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
    }

    .ql-editor.ql-blank::before {
      color: ${
        theme.palette.mode === 'light'
          ? 'rgba(34, 51, 84, 0.7)'
          : 'rgba(255, 255, 255, 0.7)'
      };
      font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji";
      font-size: 14px;
      font-style: normal;
      font-weight: 400;
    }

    .ql-toolbar.ql-snow {
      border-top-left-radius: ${theme.general.borderRadius};
      border-top-right-radius: ${theme.general.borderRadius};
    }

    .ql-toolbar.ql-snow,
    .ql-container.ql-snow {
      border-color: ${theme.colors.alpha.black[30]};
    }

    .ql-container.ql-snow {
      border-bottom-left-radius: ${theme.general.borderRadius};
      border-bottom-right-radius: ${theme.general.borderRadius};
    }

    ${
      theme.palette.mode === 'dark' &&
      `
      .ql-stroke,
      .ql-fill {
        stroke: white !important;
      }

      .ql-picker-label {
        color: white;
      }
    `
    }

    ${
      theme.palette.mode === 'light' &&
      `
      .ql-picker-label.ql-stroke,
      .ql-stroke,
      .ql-fill {
        stroke: black !important;
      }

      .ql-picker-label {
        color: black;
      }
    `
    }

    .ql-toolbar button:hover {
      .ql-picker-label.ql-stroke,
      .ql-stroke,
      .ql-fill {
        stroke: ${theme.palette.primary.main} !important;
      }
    }

    .ql-picker-label:hover,
    .ql-picker-label.ql-active,
    .ql-picker-item:hover {
      color: ${theme.palette.primary.main} !important;
    }

    &:hover {
      .ql-toolbar.ql-snow,
      .ql-container.ql-snow {
        border-color: ${theme.colors.alpha.black[50]};
      }
    }
`
);

const RichTextEditor = (props) => {
  const { editorRef, error, onChange, placeholder, submitting, value } = props;

  return (
    <EditorWrapper
      disabled={submitting}
      sx={{
        ...(error && {
          '.ql-toolbar.ql-snow, .ql-container.ql-snow': { borderColor: 'red' },
          '.ql-editor.ql-blank::before': { color: 'red' }
        })
      }}
    >
      <ReactQuill
        onChange={onChange}
        placeholder={placeholder}
        ref={editorRef}
        value={value}
      />
    </EditorWrapper>
  );
};

RichTextEditor.propTypes = {
  editorRef: PropTypes.object,
  error: PropTypes.bool,
  onChange: PropTypes.func.isRequired,
  placeholder: PropTypes.string.isRequired,
  submitting: PropTypes.bool,
  value: PropTypes.string.isRequired
};

export default RichTextEditor;
