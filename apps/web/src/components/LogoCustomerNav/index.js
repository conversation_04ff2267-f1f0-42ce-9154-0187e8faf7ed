import {
  Box,
  Card,
  CardActionArea,
  CardMedia,
  Skeleton,
  styled
} from '@mui/material';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { useContext, useLayoutEffect, useState } from 'react';
import { SettingsContext } from '../../contexts/SettingsContext';
import useAuth from '../../hooks/useAuth';
import useHoistedUrls from '../../hooks/useHoistedUrls';

const LogoWrapper = styled(Link)(
  ({ theme }) => `
        color: ${theme.palette.text.primary};
        padding: ${theme.spacing(0, 1, 0, 0)};
        display: flex;
        text-decoration: none;
        font-weight: ${theme.typography.fontWeightBold};
`
);

function LogoCustomerNav() {
  const { user } = useAuth();
  // eslint-disable-next-line no-unused-vars
  const { t } = useTranslation();
  const [settings] = useContext(SettingsContext);
  const [logoUrl, setLogoUrl] = useState('');
  const { getDeploymentUrl } = useHoistedUrls();

  useLayoutEffect(() => {
    let logoUrl = ``;
    let active = true;
    if (active) {
      if (settings && user && getDeploymentUrl?.core_account_url) {
        if (user?.profile?.record?.fields?.custentity_ng_eh_customer_logo_url) {
          logoUrl = `${getDeploymentUrl.core_account_url}${user?.profile?.record?.fields?.custentity_ng_eh_customer_logo_url}`;
        } else if (settings?.custrecord_ng_eh_react_logo_image_url) {
          logoUrl = `${getDeploymentUrl.core_account_url}${settings?.custrecord_ng_eh_react_logo_image_url}`;
        }
      }

      setLogoUrl(logoUrl);
    }

    return () => (active = false);
  }, [settings, user]);

  return (
    <LogoWrapper
      to="/dashboards/exhibit-tasks"
      sx={{
        animationName: 'animate-pop',
        animationDuration: '1s',
        animationIterationCount: 'initial',
        animationDirection: 'vertical',
        textAlign: 'center',
        justifyContent: 'center'
      }}
    >
      <Card>
        <CardActionArea sx={{ p: 1 }}>
          {user?.avatar ? (
            <CardMedia
              src={user?.avatar}
              component="img"
              sx={{ maxWidth: 200, maxHeight: 100 }}
              alt="Company Logo"
            />
          ) : (
            <Skeleton variant="rectangular" width={200} height={50} />
          )}
          {/* <LogoSignWrapper> */}
          {/*    <LogoSign>
                          <LogoSignInner />
                        </LogoSign> */}

          {/* </LogoSignWrapper> */}
        </CardActionArea>
      </Card>
      <Box
        component="span"
        sx={{
          display: { xs: 'none', sm: 'inline-block' }
        }}
      >
        {/*  <LogoTextWrapper>
          <LogoText>Exhibit House</LogoText>
        </LogoTextWrapper> */}
      </Box>
    </LogoWrapper>
  );
}

export default LogoCustomerNav;
