import { useMemo } from 'react';
import {
  Autocomplete,
  Box,
  Button,
  Checkbox,
  Chip,
  FormControl,
  FormControlLabel,
  FormHelperText,
  FormLabel,
  Grid,
  InputLabel,
  Radio,
  Stack,
  TextField,
  Typography
} from '@mui/material';
import { MuiTelInput } from 'mui-tel-input';
import { DatePicker, DateTimePicker, TimePicker } from '@mui/x-date-pickers';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { useTranslation } from 'react-i18next';
import FieldHelpPopper from './FieldHelpPopper.jsx';
import RichTextEditor from '../../components/RichTextEditor/index.jsx';
import { AttachFile } from '@mui/icons-material';
import FileChip from '../../components/FileChip/FileChip.jsx';
import CancelIcon from '@mui/icons-material/Cancel';

const EXCLUDE_TOOLTIP_TYPES = ['checkbox'];

const findFileByName = (fileName, files) =>
  files?.find((file) => file.name === fileName);

const addOrReplaceFile = (file, files, setFieldValue, replaceFile) => {
  const filteredFiles = files.filter(
    (existingFile) =>
      existingFile.name !== replaceFile.name && existingFile.name !== file.name
  );
  setFieldValue('files', [...filteredFiles, file]);
};

const removeFile = (file, files, setFieldValue) => {
  const filteredFiles = files.filter(
    (existingFile) => existingFile.name !== file.name
  );
  setFieldValue('files', filteredFiles);
};

const DynamicFormField = (allProps) => {
  const {
    defaultSelected,
    error,
    fieldHelp,
    files,
    gridProps,
    label,
    selectOptions,
    setFieldValue,
    ...props
  } = allProps;

  switch (props.type) {
    // grouping default text fields here
    case 'email':
    case 'number':
    case 'text':
    case 'url':
      return (
        <TextField {...props} error={!!error} placeholder={`Enter ${label}`} />
      );
    // remaining fields below
    case 'checkbox':
      return (
        <FormControlLabel
          control={
            <Checkbox
              {...props}
              checked={Boolean(props.value)}
              onChange={() => setFieldValue(props.name, !props.value)}
            />
          }
          error={!!error}
          label={fieldHelp || label}
        />
      );
    case 'date':
      // Ensure we have a proper Date object for the DatePicker
      const dateValue = (() => {
        if (!props.value) return null;
        if (props.value instanceof Date) return props.value;
        if (typeof props.value === 'string') {
          try {
            const date = new Date(props.value);
            return isNaN(date.getTime()) ? null : date;
          } catch (e) {
            console.warn(
              'Failed to parse date value in DatePicker:',
              props.value
            );
            return null;
          }
        }
        return null;
      })();

      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DatePicker
            label={props.label}
            onChange={(date) => setFieldValue(props.name, date)}
            renderInput={(params) => <TextField {...params} error={!!error} />}
            value={dateValue}
          />
        </LocalizationProvider>
      );
    case 'datetime':
      // Ensure we have a proper Date object for the DateTimePicker
      const dateTimeValue = (() => {
        if (!props.value) return null;
        if (props.value instanceof Date) return props.value;
        if (typeof props.value === 'string') {
          try {
            const date = new Date(props.value);
            return isNaN(date.getTime()) ? null : date;
          } catch (e) {
            console.warn(
              'Failed to parse datetime value in DateTimePicker:',
              props.value
            );
            return null;
          }
        }
        return null;
      })();

      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <DateTimePicker
            label={props.label}
            onChange={(date) => setFieldValue(props.name, date)}
            renderInput={(params) => <TextField {...params} error={!!error} />}
            views={['year', 'month', 'day', 'hours', 'minutes']}
            value={dateTimeValue}
          />
        </LocalizationProvider>
      );
    case 'file':
      const file = findFileByName(props.value, files);
      return (
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Button
            color="inherit"
            component="label"
            role={undefined}
            startIcon={<AttachFile />}
            tabIndex={-1}
            variant="outlined"
          >
            {file || props.value ? 'Replace' : 'Attach'} File
            <Box
              component="input"
              onChange={(e) => {
                const newFile = e.target.files[0];
                addOrReplaceFile(newFile, files, setFieldValue, file);
                setFieldValue(props.name, newFile.name);
              }}
              sx={{
                clip: 'rect(0 0 0 0)',
                clipPath: 'inset(50%)',
                height: 1,
                overflow: 'hidden',
                position: 'absolute',
                bottom: 0,
                left: 0,
                whiteSpace: 'nowrap',
                width: 1
              }}
              type="file"
            />
          </Button>
          {file && (
            <FileChip
              file={file}
              onDelete={() => {
                removeFile(file, files, setFieldValue);
                setFieldValue(props.name, null);
              }}
            />
          )}
          {!file && props.value && (
            <Stack sx={{ pl: 1 }}>
              <Typography variant="subtitle2">
                File attached - no preview available:
              </Typography>
              <Typography variant="h4">{props.value}</Typography>
            </Stack>
          )}
        </Box>
      );
    case 'phone':
      return (
        <MuiTelInput
          placeholder={`Enter ${label}`}
          onChange={(value) => setFieldValue(props.name, value)}
          error={!!error}
          value={props.value}
        />
      );
    case 'radio':
      return (
        <FormControl component="fieldset" error={!!error}>
          <FormLabel component="legend">{props.label}</FormLabel>
          {selectOptions.map((option) => (
            <FormControlLabel
              key={option.value}
              control={<Radio {...props} value={option.value} />}
              label={option.text}
            />
          ))}
        </FormControl>
      );
    case 'richText':
      return (
        <RichTextEditor
          error={error}
          onChange={(value) => setFieldValue(props.name, value)}
          placeholder={`Enter ${label}`}
          value={props.value}
        />
      );
    case 'select':
      return (
        <FormControl fullWidth error={!!error}>
          <InputLabel>{props.label}</InputLabel>
          <Autocomplete
            value={defaultSelected || (props.multiple ? [] : null)}
            multiple={props.multiple}
            onChange={(_event, value) => {
              Array.isArray(value)
                ? setFieldValue(
                    props.name,
                    value.map((value) => value.value)
                  )
                : setFieldValue(props.name, value?.value);
            }}
            options={selectOptions}
            placeholder={`Select ${label}`}
            renderInput={(params) => (
              <TextField {...params} placeholder={`Select ${label}`} />
            )}
            renderTags={(value, getTagProps) =>
              value.map((option, index) => {
                const { key, ...tagProps } = getTagProps({ index });
                return (
                  <Chip
                    deleteIcon={
                      <CancelIcon
                        sx={(theme) => ({
                          color: `${theme.palette.text.disabled} !important`,
                          fontSize: '20px !important'
                        })}
                      />
                    }
                    variant="filled"
                    label={option.label}
                    key={key}
                    {...tagProps}
                  />
                );
              })
            }
          />
        </FormControl>
      );
    case 'textarea':
      return <TextField {...props} error={!!error} multiline />;
    case 'time':
      // Ensure we have a proper Date object for the TimePicker
      const timeValue = (() => {
        if (!props.value) return null;
        if (props.value instanceof Date) return props.value;
        if (typeof props.value === 'string') {
          try {
            const date = new Date(props.value);
            return isNaN(date.getTime()) ? null : date;
          } catch (e) {
            console.warn(
              'Failed to parse time value in TimePicker:',
              props.value
            );
            return null;
          }
        }
        return null;
      })();

      return (
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <TimePicker
            label={props.label}
            onChange={(date) => setFieldValue(props.name, date)}
            renderInput={(params) => <TextField {...params} error={!!error} />}
            value={timeValue}
          />
        </LocalizationProvider>
      );
    default:
      return null;
  }
};

const DynamicFormikField = (props) => {
  const { error, fieldHelp, gridProps, label, options, useGroupStyles } = props;
  const { t } = useTranslation();

  console.log('Error with field:', error, label);

  // Debug logging for time and select fields
  if (props.type === 'time' || props.type === 'select') {
    console.log(`DynamicFormikField ${props.name} (${props.type}):`, {
      value: props.value,
      valueType: typeof props.value,
      multiple: props.multiple,
      optionsCount: options?.length || 0
    });
  }

  // Select fields are currently uncontrolled, we memoize the default value
  // and options to keep it uncontrolled
  const selectProps = useMemo(() => {
    const selectOptions =
      props.type === 'select'
        ? options.map((option) => ({
            label: option.text,
            value: option.value
          }))
        : null;

    const defaultSelected =
      props.type === 'select'
        ? (() => {
            if (!props.value || !options || options.length === 0) {
              return props.multiple ? [] : null;
            }

            if (Array.isArray(props.value)) {
              // Multi-select: filter options that match the values
              return options
                .filter((option) =>
                  props.value.some(
                    (val) => String(val) === String(option.value)
                  )
                )
                .map((option) => ({
                  label: option.text,
                  value: option.value
                }));
            }
            // Single select: find the matching option
            const matchedOption = options.find(
              (option) => String(option.value) === String(props.value)
            );
            return matchedOption
              ? {
                  label: matchedOption.text,
                  value: matchedOption.value
                }
              : null;
          })()
        : null;

    return { defaultSelected, selectOptions };
  }, [options, props.value, props.multiple]);

  return (
    <Grid item xs={12} md={6} {...gridProps}>
      <FormControl error={!!error} fullWidth>
        <Box
          sx={{
            alignItems: 'center',
            display: 'flex',
            gap: 1,
            pb: 1,
            ...(useGroupStyles && { color: 'text.secondary' })
          }}
        >
          <Typography fontWeight={useGroupStyles ? 600 : 700}>
            {t(label)}
            {props.validations.includes('required') && (
              <Box color="red" component="span">
                {' *'}
              </Box>
            )}
          </Typography>
          {fieldHelp && !EXCLUDE_TOOLTIP_TYPES.includes(props.type) && (
            <FieldHelpPopper label={label} fieldHelp={fieldHelp} />
          )}
        </Box>
        <DynamicFormField
          {...props}
          {...(props.type === 'select' && selectProps)}
        />
        {error && <FormHelperText>{error}</FormHelperText>}
      </FormControl>
    </Grid>
  );
};

export default DynamicFormikField;
