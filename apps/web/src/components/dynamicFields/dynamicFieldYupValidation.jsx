import * as Yup from 'yup';

const getValidationSchema = (validations, translationFn) => {
  const t = translationFn;
  let validationSchema = Yup.string();

  validations.forEach((validation) => {
    switch (validation) {
      case 'boolean':
        validationSchema = Yup.boolean().typeError(t('Indicate true or false'));
        break;
      case 'creditCard':
        validationSchema = validationSchema.matches(
          /^[0-9]{13,19}$/,
          t('Invalid credit card number')
        );
        break;
      case 'email':
        validationSchema = validationSchema.email(t('Invalid email address'));
        break;
      case 'integer':
        validationSchema = Yup.number()
          .typeError(t('Must be an number'))
          .integer(t('Must be an integer'));
        break;
      case 'number':
        validationSchema = Yup.number().typeError(t('Must be a number'));
        break;
      case 'positive':
        validationSchema = Yup.number()
          .typeError(t('Must be a number'))
          .positive(t('Must be a positive number'));
        break;
      case 'required':
        validationSchema = validationSchema.required(
          t('This field is required')
        );
        break;
      case 'url':
        validationSchema = validationSchema.url(t('Invalid URL'));
        break;
      // VALIDATIONS TYPES THAT ARE NOT CURRENTLY CONFIGURED
      case 'date':
      case 'datetime':
      case 'file':
      case 'phone':
      case 'time':
        break;
      default:
        throw new Error(t(`Unknown validation type: ${validation}`));
    }
  });

  return validationSchema;
};

export default getValidationSchema;
