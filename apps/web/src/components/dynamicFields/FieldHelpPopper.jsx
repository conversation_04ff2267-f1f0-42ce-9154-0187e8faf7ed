import { useState } from 'react';
import {
  <PERSON>,
  Button,
  Divider,
  Fade,
  Paper,
  Popper,
  Stack,
  Typography
} from '@mui/material';
import { Close, Help } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';

const desanitize = (htmlString) => {
  const entitiesMap = {
    '&lt;': '<',
    '&gt;': '>',
    '&amp;': '&',
    '&quot;': '"',
    '&apos;': "'"
  };

  return htmlString.replace(
    /&lt;|&gt;|&amp;|&quot;|&apos;/g,
    (match) => entitiesMap[match]
  );
};

const FieldHelpPopper = (props) => {
  const { fieldHelp, label } = props;
  const [anchorEl, setAnchorEl] = useState(null);

  const { t } = useTranslation();

  const handleClick = (event) => {
    setAnchorEl(anchorEl ? null : event.currentTarget);
  };

  return (
    <>
      <Popper
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        placement="top"
        sx={{
          py: 2,
          px: 3,
          zIndex: 9999
        }}
        transition
      >
        {({ TransitionProps }) => (
          <Fade {...TransitionProps} timeout={350}>
            <Paper
              sx={{ boxShadow: '0 0 20px rgba(0, 0, 0, .5)', borderRadius: 1 }}
            >
              <Stack>
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    p: 2,
                    backgroundColor: 'background.default',
                    borderTopRightRadius: 10,
                    borderTopLeftRadius: 10
                  }}
                >
                  <Typography sx={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {t(label)}
                  </Typography>
                  <Button
                    onClick={handleClick}
                    color="inherit"
                    sx={{
                      minWidth: 'unset',
                      width: '24px',
                      height: '24px',
                      borderRadius: '5px'
                    }}
                    size="small"
                  >
                    <Close
                      sx={{ minWidth: 'unset', width: '18px', height: '18px' }}
                    />
                  </Button>
                </Box>
                <Divider />
                <Box
                  component="div"
                  dangerouslySetInnerHTML={{
                    __html: desanitize(fieldHelp)
                  }}
                  sx={{
                    p: 2,
                    width: 'min-content',
                    minWidth: '300px',
                    maxWidth: `calc(100vw - 70px)`,
                    maxHeight: '450px',
                    overflowY: 'auto',
                    '*': {
                      maxWidth: 'calc(100vw - 108px)'
                    }
                  }}
                />
              </Stack>
            </Paper>
          </Fade>
        )}
      </Popper>
      <Help
        onClick={handleClick}
        sx={{ fontSize: '15px', cursor: 'pointer' }}
      />
    </>
  );
};

export default FieldHelpPopper;
