import { Card, CardActionArea, CardMedia, styled } from '@mui/material';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { useContext, useEffect, useState } from 'react';
import { SettingsContext } from '../../contexts/SettingsContext';
import useHoistedUrls from '../../hooks/useHoistedUrls';

const LogoWrapper = styled(Link)(
  ({ theme }) => `
        color: ${theme.palette.text.primary};
        padding: ${theme.spacing(0, 1, 0, 0)};
        display: flex;
        text-decoration: none;
        font-weight: ${theme.typography.fontWeightBold};
`
);

function Logo() {
  const [settings] = useContext(SettingsContext);
  const [logoUrl, setLogoUrl] = useState('');
  const { getDeploymentUrl } = useHoistedUrls();

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      setLogoUrl(settings?.custrecord_ng_eh_react_logo_image_url);
    } else if (settings?.custrecord_ng_eh_react_logo_image_url) {
      setLogoUrl(settings.custrecord_ng_eh_react_logo_image_url);
    }
  }, [settings]);

  let transitions = {
    loaded: {
      opacity: 1,
      y: 0
    },
    loading: {
      opacity: 0,
      y: -6
    }
  };

  return (
    <LogoWrapper
      to="/"
      sx={{
        animationName: 'animate-pop',
        animationDuration: '1s',
        animationIterationCount: 'initial',
        animationDirection: 'vertical',
        textAlign: 'center',
        justifyContent: 'center'
      }}
    >
      <motion.div
        initial={{
          opacity: 0,
          y: -6
        }}
        variants={transitions}
        animate={logoUrl ? 'loaded' : 'loading'}
        transition={{
          delay: 0.5,
          type: 'spring',
          stiffness: 200
        }}
      >
        <Card>
          <CardActionArea sx={{ p: 1 }}>
            <CardMedia
              loading="lazy"
              src={`${getDeploymentUrl?.core_account_url}${logoUrl}`}
              component="img"
              sx={{ maxWidth: 200, maxHeight: 50 }}
              alt="Company Logo"
            />
          </CardActionArea>
        </Card>
      </motion.div>
    </LogoWrapper>
  );
}

export default Logo;
