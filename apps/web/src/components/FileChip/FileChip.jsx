import { alpha, Box, Chip, darken, Tooltip, Typography } from '@mui/material';
import { FileIcon, formatFileName, formatFileSize } from './FileHelpers.jsx';
import PropTypes from 'prop-types';

const TooltipTitle = (props) => {
  const { file } = props;
  const extension = file.name.split('.').at(-1);
  const type = file.type;

  return (
    <>
      <Typography>{file.name}</Typography>
      {type.includes('image') ? (
        <Box
          component="img"
          src={URL.createObjectURL(file)}
          sx={{
            p: 0,
            mt: 1,
            mb: 0.5,
            border: 0,
            borderRadius: 0.5,
            width: '100%'
          }}
        />
      ) : (
        <Box
          sx={(theme) => ({
            alignItems: 'center',
            backgroundColor: alpha(theme.palette.background.paper, 0.9),
            borderRadius: 0.4,
            display: 'flex',
            height: { xs: '60px', sm: '80px' },
            justifyContent: 'center',
            p: 1,
            my: 1,
            width: '100%'
          })}
        >
          <FileIcon
            extension={extension}
            fontSize="large"
            type={type}
            sx={(theme) => ({
              color: theme.palette.mode === 'dark' ? 'white' : 'black'
            })}
          />
        </Box>
      )}
    </>
  );
};

const FileChip = (props) => {
  const { file, onDelete } = props;

  return (
    <Tooltip
      arrow
      placement="top"
      size="sm"
      sx={(theme) => ({
        backgroundColor: theme.palette.background.default
      })}
      title={<TooltipTitle file={file} />}
    >
      <Chip
        sx={{
          display: { xs: 'flex', sm: 'none' },
          fontSize: '12px'
        }}
        size="small"
        label={`${formatFileName(file.name, 15)} - ${formatFileSize(
          file.size
        )}`}
        onDelete={onDelete}
      />
      <Chip
        sx={{ display: { xs: 'none', sm: 'flex' } }}
        label={`${formatFileName(file.name, 15)} - ${formatFileSize(
          file.size
        )}`}
        onDelete={onDelete}
      />
    </Tooltip>
  );
};

TooltipTitle.propTypes = {
  file: PropTypes.instanceOf(File).isRequired
};

FileChip.propTypes = {
  file: PropTypes.instanceOf(File).isRequired,
  onDelete: PropTypes.func.isRequired
};

export default FileChip;
