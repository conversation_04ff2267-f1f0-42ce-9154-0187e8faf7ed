import {
  AudioFileTwoTone,
  BorderAllRounded,
  CodeTwoTone,
  CssTwoTone,
  DataObjectTwoTone,
  DescriptionTwoTone,
  DesignServicesTwoTone,
  DvrTwoTone,
  FolderZipTwoTone,
  GestureTwoTone,
  HtmlTwoTone,
  ImageTwoTone,
  JavascriptTwoTone,
  NewReleasesTwoTone,
  PictureAsPdfTwoTone,
  SdStorageTwoTone,
  SettingsTwoTone,
  SmsTwoTone,
  VideoFileTwoTone
} from '@mui/icons-material';
import PropTypes from 'prop-types';

export const NETSUITE_FILE_TYPES_ICON_MAPPING = {
  APPCACHE: SdStorageTwoTone,
  AUTOCAD: DesignServicesTwoTone,
  BMPIMAGE: ImageTwoTone,
  CERTIFICATE: NewReleasesTwoTone,
  CONFIG: SettingsTwoTone,
  CSV: BorderAllRounded,
  EXCEL: BorderAllRounded,
  FLASH: DescriptionTwoTone,
  FREEMARKER: CodeTwoTone,
  GIFIMAGE: ImageTwoTone,
  GZIP: FolderZipTwoTone,
  HTMLDOC: HtmlTwoTone,
  ICON: ImageTwoTone,
  JAVASCRIPT: JavascriptTwoTone,
  JPGIMAGE: ImageTwoTone,
  JSON: DataObjectTwoTone,
  MESSAGERFC: SmsTwoTone,
  MP3: AudioFileTwoTone,
  MPEGMOVIE: VideoFileTwoTone,
  MSPROJECT: DescriptionTwoTone,
  PDF: PictureAsPdfTwoTone,
  PJPGIMAGE: ImageTwoTone,
  PLAINTEXT: DescriptionTwoTone,
  PNGIMAGE: ImageTwoTone,
  POSTSCRIPT: DescriptionTwoTone,
  POWERPOINT: DvrTwoTone,
  QUICKTIME: VideoFileTwoTone,
  RTF: DescriptionTwoTone,
  SCSS: CssTwoTone,
  SMS: SmsTwoTone,
  STYLESHEET: CssTwoTone,
  SVG: ImageTwoTone,
  TAR: SettingsTwoTone,
  TIFFIMAGE: ImageTwoTone,
  VISIO: GestureTwoTone,
  WEBAPPPAGE: DescriptionTwoTone,
  WEBAPPSCRIPT: DescriptionTwoTone,
  WORD: DescriptionTwoTone,
  XMLDOC: DescriptionTwoTone,
  XSD: DescriptionTwoTone,
  ZIP: FolderZipTwoTone
};

export const MIME_TYPE_MAPPINGS = {
  application: DescriptionTwoTone,
  audio: AudioFileTwoTone,
  example: DescriptionTwoTone,
  font: DescriptionTwoTone,
  haptics: DescriptionTwoTone,
  image: ImageTwoTone,
  message: DescriptionTwoTone,
  model: DataObjectTwoTone,
  multipart: DescriptionTwoTone,
  text: DescriptionTwoTone,
  video: VideoFileTwoTone
};

export const FILE_EXTENSION_MAPPINGS = {
  css: CssTwoTone,
  html: HtmlTwoTone,
  js: JavascriptTwoTone,
  json: DataObjectTwoTone,
  pdf: PictureAsPdfTwoTone,
  txt: DescriptionTwoTone,
  xls: BorderAllRounded,
  xlsx: BorderAllRounded,
  zip: FolderZipTwoTone
};

export const DEFAULT_FILE_ICON = DescriptionTwoTone;

export const NETSUITE_FILE_TYPES = Object.keys(
  NETSUITE_FILE_TYPES_ICON_MAPPING
);

export const formatFileName = (fileName, targetLength) => {
  if (fileName.length > targetLength) {
    const fileParts = fileName.split('.');
    const fileNamePart = fileParts.slice(0, fileParts.length - 1).join('');
    const fileExt = fileParts.at(-1);
    return `${fileNamePart.slice(
      0,
      targetLength - fileExt.length - 1
    )}...${fileExt}`;
  } else {
    return fileName;
  }
};

export const formatFileSize = (bytes) => {
  const units = ['bytes', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size = size / 1024;
    unitIndex++;
  }

  return `${Math.ceil(size)} ${units[unitIndex]}`;
};

export const FileIcon = (props) => {
  const { extension, type, ...rest } = props;

  if (extension) {
    const extensions = Object.keys(FILE_EXTENSION_MAPPINGS);
    const matchingExt = extensions.find((fe) => extension === fe);
    if (matchingExt) {
      const Icon = FILE_EXTENSION_MAPPINGS[matchingExt];
      return <Icon {...rest} />;
    }

    const mimeTypes = Object.keys(MIME_TYPE_MAPPINGS);
    const matchingMimeType = mimeTypes.find((mimeType) =>
      type.includes(mimeType)
    );
    if (matchingMimeType) {
      const Icon = MIME_TYPE_MAPPINGS[matchingMimeType];
      return <Icon {...rest} />;
    }

    const Icon = DEFAULT_FILE_ICON;
    return <Icon {...rest} />;
  }

  const Icon = NETSUITE_FILE_TYPES_ICON_MAPPING[type] || DEFAULT_FILE_ICON;
  return <Icon {...rest} />;
};

FileIcon.propTypes = {
  extension: PropTypes.string,
  type: PropTypes.string.isRequired
};
