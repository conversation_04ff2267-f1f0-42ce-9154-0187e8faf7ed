import { Box, Typography } from '@mui/material';
import { Player } from '@lottiefiles/react-lottie-player';
import { ThreeDotsLoading } from '../../utils/lottieAnimate';
import LogoCustomer from '../LogoCustomer';

function AppInit() {
  return (
    <Box
      sx={{
        position: 'fixed',
        left: 0,
        top: 0,
        bottom: 0,
        right: 0,
        width: '100%',
        height: '100%'
      }}
      display="flex"
      alignItems="center"
      justifyContent="center"
    >
      <Box
        flexDirection="column"
        display="flex"
        alignItems="center"
        justifyContent="center"
      >
        <Player
          src={ThreeDotsLoading()}
          autoplay
          loop
          controls={false}
          style={{ height: '50%', width: '50%', borderRadius: '1rem' }}
        />
        <Box
          pb={3}
          sx={{
            animationName: 'animate-text-fade',
            animationDuration: '2s',
            animationFillMode: 'backwards',
            animationIterationCount: 'infinite',
            animationDirection: 'vertical'
          }}
        >
          <Typography variant="h4" gutterBottom>
            Initializing Page...
          </Typography>
        </Box>
        <LogoCustomer />
      </Box>
    </Box>
  );
}

export default AppInit;
