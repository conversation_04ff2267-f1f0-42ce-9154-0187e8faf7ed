import 'src/mocks';
import 'src/utils/chart';
import 'src/styles/global.css';
import { HelmetProvider } from 'react-helmet-async';
import { HashRouter } from 'react-router-dom';
import ScrollTop from 'src/hooks/useScrollTop';

import '@fontsource/roboto/300.css';
import '@fontsource/roboto/400.css';
import '@fontsource/roboto/500.css';
import '@fontsource/roboto/700.css';
import 'nprogress/nprogress.css';

import axios from 'axios';
import { preload, SWRConfig } from 'swr';
import { Provider } from 'react-redux';
import store from 'src/store';
import App from 'src/App';
import * as ReactDOMClient from 'react-dom/client';
import { SidebarProvider } from 'src/contexts/SidebarContext';
import * as serviceWorker from 'src/serviceWorker';
import UserProvider from './contexts/UserContext';
import { SettingsProvider } from './contexts/SettingsContext';

const axiosFetcher = (url) => axios.get(url).then((res) => res.data);
const container = document.getElementById('root');

const root = ReactDOMClient.createRoot(container);

const fetcher = (url) =>
  axios
    .get(url, {
      headers: { 'Content-Type': 'application/json' },
      ...(process.env.NODE_ENV !== 'development' && {
        auth: {
          consumer_key: process.env.REACT_APP_NS_CONSUMER_KEY,
          consumer_secret: process.env.REACT_APP_NS_CONSUMER_SECRET,
          token: process.env.REACT_APP_NS_TOKEN_ID,
          token_secret: process.env.REACT_APP_NS_TOKEN_SECRET,
          signature_method: 'HMAC-SHA256',
          realm: process.env.REACT_APP_NS_ACCOUNT_ID,
          version: '1.0'
        }
      })
    })
    .then((res) => res.data);

const getHoistedUrls =
  process.env.NODE_ENV === 'development'
    ? process.env.REACT_APP_NS_HOISTED_URL_SUITELET
    : '/app/site/hosting/scriptlet.nl?script=customscript_ng_cs_sl_hoisted_app_urls&deploy=customdeploy_ng_cs_sl_hoisted_app_urls';

preload(getHoistedUrls, fetcher).then((res) => {
  console.log('Preloaded Urls: ', res);
});

root.render(
  <HelmetProvider>
    <SWRConfig
      value={{
        provider: () => new Map(),
        fetcher: (resource, init) => axiosFetcher(resource, init)
      }}
    >
      <SettingsProvider>
        {/*<DeploymentUrlsProvider>*/}
        <Provider store={store}>
          <SidebarProvider>
            <HashRouter>
              <ScrollTop />
              <UserProvider>
                <App />
              </UserProvider>
            </HashRouter>
          </SidebarProvider>
        </Provider>
        {/*</DeploymentUrlsProvider>*/}
      </SettingsProvider>
    </SWRConfig>
  </HelmetProvider>
);

serviceWorker.unregister();
