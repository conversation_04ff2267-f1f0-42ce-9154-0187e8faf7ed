/**
 * Integration tests for NetSuite configuration error UI handling
 * Tests the complete user experience when admin misconfiguration occurs
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import { SnackbarProvider } from 'notistack';
import axios from 'axios';

import UserProvider from '../../contexts/UserContext';
import { SettingsProvider } from '../../contexts/SettingsContext';
import theme from '../../theme';
import { 
  mockNetSuiteConfigurationError, 
  mockUserSuiteletConfigError,
  resetMocks 
} from '../setup/axios-mock';

// Mock AppInit component that shows during loading/error states
const MockAppInit = () => {
  const [error, setError] = React.useState(null);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    // Simulate the app initialization process
    const timer = setTimeout(() => {
      setLoading(false);
    }, 100);

    return () => clearTimeout(timer);
  }, []);

  if (loading) {
    return <div data-testid="app-loading">Initializing application...</div>;
  }

  if (error) {
    return (
      <div data-testid="app-error">
        <h2>Application Error</h2>
        <p data-testid="error-message">{error.message}</p>
        <button data-testid="retry-app" onClick={() => setError(null)}>
          Retry
        </button>
      </div>
    );
  }

  return <div data-testid="app-ready">Application ready</div>;
};

// Mock main App component that would normally render after successful user load
const MockApp = () => {
  const [user, setUser] = React.useState(null);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    // Simulate checking for user data
    const checkUser = async () => {
      try {
        const response = await axios.get(process.env.REACT_APP_GET_USER_SUITELET);
        setUser(response.data.data);
      } catch (err) {
        setError(err);
      }
    };

    checkUser();
  }, []);

  if (error) {
    return (
      <div data-testid="main-app-error">
        <div data-testid="error-banner">
          <h3>Configuration Error</h3>
          <p data-testid="config-error-message">
            {error.response?.data?.details?.message || error.message}
          </p>
          <div data-testid="admin-instructions">
            Please contact your system administrator to resolve this configuration issue.
          </div>
          <button data-testid="refresh-page" onClick={() => window.location.reload()}>
            Refresh Page
          </button>
        </div>
      </div>
    );
  }

  if (!user) {
    return <div data-testid="user-loading">Loading user data...</div>;
  }

  return (
    <div data-testid="main-app">
      <header data-testid="app-header">
        <div data-testid="user-info">Welcome, {user.name}</div>
      </header>
      <main data-testid="app-content">
        Application content
      </main>
    </div>
  );
};

// Test wrapper that simulates the full app structure
const FullAppTestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      <SWRConfig 
        value={{ 
          provider: () => new Map(),
          fetcher: (url) => axios.get(url).then(res => res.data),
          dedupingInterval: 0,
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
        }}
      >
        <SettingsProvider>
          <UserProvider>
            {children}
          </UserProvider>
        </SettingsProvider>
      </SWRConfig>
    </SnackbarProvider>
  </ThemeProvider>
);

describe('NetSuite Configuration Error UI Integration', () => {
  beforeEach(() => {
    resetMocks();
    
    // Mock environment
    process.env.REACT_APP_GET_USER_SUITELET = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1';
    
    // Mock window.location.reload
    Object.defineProperty(window, 'location', {
      value: { reload: jest.fn() },
      writable: true
    });

    // Clear console
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Application Initialization with Configuration Errors', () => {
    test('should display configuration error during app initialization', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockAppInit />
        </FullAppTestWrapper>
      );

      // Should start with loading
      expect(screen.getByTestId('app-loading')).toBeInTheDocument();

      // Should complete initialization
      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });
    });

    test('should show main app error when user data fails to load', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      // Initially loading
      expect(screen.getByTestId('user-loading')).toBeInTheDocument();

      // Should show error after failed load
      await waitFor(() => {
        expect(screen.getByTestId('main-app-error')).toBeInTheDocument();
      });

      // Verify error content
      expect(screen.getByTestId('config-error-message')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
      expect(screen.getByTestId('admin-instructions')).toHaveTextContent(
        'Please contact your system administrator to resolve this configuration issue.'
      );
    });
  });

  describe('User Experience for Configuration Errors', () => {
    test('should provide clear messaging for missing printout templates', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('config-error-message')).toBeInTheDocument();
      });

      const errorMessage = screen.getByTestId('config-error-message');
      expect(errorMessage).toHaveTextContent('❌ Printout templates are required in settings');
      expect(errorMessage).toHaveTextContent('Please configure Sales Order and Invoice printing templates');
    });

    test('should provide refresh functionality for users', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('refresh-page')).toBeInTheDocument();
      });

      // Click refresh button
      fireEvent.click(screen.getByTestId('refresh-page'));

      // Should trigger page reload
      expect(window.location.reload).toHaveBeenCalledTimes(1);
    });

    test('should handle different configuration error types', async () => {
      // Test missing invoice printing template
      const invoiceError = new Error('Error getting user information');
      invoiceError.response = {
        data: {
          id: 'ERR-1750963949169-858',
          message: 'Error getting user information',
          details: {
            message: '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.',
            customMessage: 'Error getting user information'
          }
        },
        status: 500
      };

      axios.get.mockRejectedValue(invoiceError);

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('config-error-message')).toHaveTextContent(
          '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.'
        );
      });
    });
  });

  describe('Error Recovery and Retry Mechanisms', () => {
    test('should allow users to retry after configuration is fixed', async () => {
      // Start with error
      mockUserSuiteletConfigError();

      const { rerender } = render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('main-app-error')).toBeInTheDocument();
      });

      // Simulate configuration being fixed
      const successResponse = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: { record: { fields: {} } }
        }
      };

      axios.get.mockResolvedValue({ data: successResponse });

      // Re-render to simulate retry
      rerender(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      // Should now show successful app load
      await waitFor(() => {
        expect(screen.getByTestId('main-app')).toBeInTheDocument();
      });

      expect(screen.getByTestId('user-info')).toHaveTextContent('Welcome, Test User');
    });

    test('should handle network errors differently from configuration errors', async () => {
      // Mock network error
      const networkError = new Error('Network Error');
      networkError.code = 'NETWORK_ERROR';
      axios.get.mockRejectedValue(networkError);

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('config-error-message')).toHaveTextContent('Network Error');
      });

      // Network errors should still show admin instructions but with different context
      expect(screen.getByTestId('admin-instructions')).toBeInTheDocument();
    });
  });

  describe('Error Logging and Debugging', () => {
    test('should log configuration errors for debugging', async () => {
      const consoleSpy = jest.spyOn(console, 'error');
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('main-app-error')).toBeInTheDocument();
      });

      // Should have logged the error
      expect(consoleSpy).toHaveBeenCalled();
    });

    test('should preserve error ID for support purposes', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('main-app-error')).toBeInTheDocument();
      });

      // Error ID should be available in the error object for logging
      // This would typically be logged to external services
      expect(mockNetSuiteConfigurationError.id).toBe('ERR-1750963949169-856');
    });
  });

  describe('Accessibility and User Experience', () => {
    test('should provide accessible error messages', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-banner')).toBeInTheDocument();
      });

      // Error should have proper heading structure
      expect(screen.getByRole('heading', { level: 3 })).toHaveTextContent('Configuration Error');
      
      // Should have actionable button
      expect(screen.getByRole('button', { name: /refresh page/i })).toBeInTheDocument();
    });

    test('should handle keyboard navigation for error actions', async () => {
      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('refresh-page')).toBeInTheDocument();
      });

      const refreshButton = screen.getByTestId('refresh-page');
      
      // Should be focusable
      refreshButton.focus();
      expect(document.activeElement).toBe(refreshButton);

      // Should respond to Enter key
      fireEvent.keyDown(refreshButton, { key: 'Enter', code: 'Enter' });
      expect(window.location.reload).toHaveBeenCalled();
    });
  });

  describe('Production vs Development Error Handling', () => {
    test('should handle production environment errors', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('config-error-message')).toBeInTheDocument();
      });

      // Should still show configuration error in production
      expect(screen.getByTestId('config-error-message')).toHaveTextContent(
        '❌ Printout templates are required in settings'
      );

      process.env.NODE_ENV = originalEnv;
    });

    test('should handle development environment errors with additional debugging', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      mockUserSuiteletConfigError();

      render(
        <FullAppTestWrapper>
          <MockApp />
        </FullAppTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('config-error-message')).toBeInTheDocument();
      });

      // Should show same error message in development
      expect(screen.getByTestId('config-error-message')).toHaveTextContent(
        '❌ Printout templates are required in settings'
      );

      process.env.NODE_ENV = originalEnv;
    });
  });
});
