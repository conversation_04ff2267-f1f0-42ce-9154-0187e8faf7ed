/**
 * Integration tests for authentication flow
 * Tests complete authentication workflows with API integration
 */

import React from 'react';
import { render, screen, waitFor } from '../setup/test-utils';
import userEvent from '@testing-library/user-event';
import { mockedAxios, mockPostSuccess, mockPostError, mockGetError } from '../setup/axios-mock';
import App from '../../App';

// Mock the deployment URL store
jest.mock('../../store/deploymentUrlStore', () => ({
  useDeploymentUrls: () => ({
    getDeploymentUrl: {
      success: true,
      get_project_form_data_external: 'http://test.com/external/form',
      get_project_form_data_internal: '/internal/form',
    },
  }),
}));

// Mock the user context with initial unauthenticated state
const mockUserContext = {
  isAuthenticated: false,
  user: null,
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  method: 'JWT',
};

jest.mock('../../hooks/useAuth', () => ({
  __esModule: true,
  default: () => mockUserContext,
}));

describe('Authentication Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset auth state
    mockUserContext.isAuthenticated = false;
    mockUserContext.user = null;
  });

  describe('Login Flow', () => {
    it('shows login page when user is not authenticated', async () => {
      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/sign into your account/i)).toBeInTheDocument();
      });
    });

    it('handles successful JWT login', async () => {
      const user = userEvent.setup();

      // Mock successful login response
      mockPostSuccess({
        user: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
        },
        accessToken: 'mock-token',
      }, 200);

      render(<App />);

      // Fill in login form
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'password123');
      await user.click(loginButton);

      // Verify login function was called
      expect(mockUserContext.login).toHaveBeenCalled();
    });

    it('handles login error', async () => {
      const user = userEvent.setup();

      // Mock failed login response
      mockPostError(401, 'Invalid credentials');

      render(<App />);

      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/password/i);
      const loginButton = screen.getByRole('button', { name: /sign in/i });

      await user.type(emailInput, '<EMAIL>');
      await user.type(passwordInput, 'wrongpassword');
      await user.click(loginButton);

      await waitFor(() => {
        expect(screen.getByText(/invalid credentials/i)).toBeInTheDocument();
      });
    });

    it('redirects to dashboard after successful login', async () => {
      // Simulate authenticated state
      mockUserContext.isAuthenticated = true;
      mockUserContext.user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        profile: { jobtitle: 'Developer' },
      };

      render(<App />);

      await waitFor(() => {
        // Should not show login page
        expect(screen.queryByText(/sign into your account/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Logout Flow', () => {
    it('handles logout successfully', async () => {
      const user = userEvent.setup();

      // Start with authenticated state
      mockUserContext.isAuthenticated = true;
      mockUserContext.user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        profile: { jobtitle: 'Developer' },
      };

      // Mock successful logout response
      mockPostSuccess({ message: 'Logged out successfully' }, 200);

      const { rerender } = render(<App />);

      // Simulate logout action
      mockUserContext.logout();

      // Update state to unauthenticated
      mockUserContext.isAuthenticated = false;
      mockUserContext.user = null;

      rerender(<App />);

      await waitFor(() => {
        expect(screen.getByText(/sign into your account/i)).toBeInTheDocument();
      });
    });
  });

  describe('Registration Flow', () => {
    it('handles successful registration', async () => {
      const user = userEvent.setup();

      // Mock successful registration response
      mockPostSuccess({
        user: {
          id: '2',
          name: 'New User',
          email: '<EMAIL>',
        },
        accessToken: 'mock-token',
      }, 201);

      render(<App />);

      // Navigate to registration (if available)
      const registerLink = screen.queryByText(/sign up/i);
      if (registerLink) {
        await user.click(registerLink);

        // Fill registration form
        const nameInput = screen.getByLabelText(/name/i);
        const emailInput = screen.getByLabelText(/email/i);
        const passwordInput = screen.getByLabelText(/password/i);
        const registerButton = screen.getByRole('button', { name: /register/i });

        await user.type(nameInput, 'New User');
        await user.type(emailInput, '<EMAIL>');
        await user.type(passwordInput, 'password123');
        await user.click(registerButton);

        expect(mockUserContext.register).toHaveBeenCalled();
      }
    });
  });

  describe('Auth0 Integration', () => {
    it('handles Auth0 login', async () => {
      const user = userEvent.setup();

      // Update auth method to Auth0
      mockUserContext.method = 'Auth0';

      render(<App />);

      const auth0Button = screen.queryByText(/sign in with.*auth0/i);
      if (auth0Button) {
        await user.click(auth0Button);
        expect(mockUserContext.login).toHaveBeenCalled();
      }
    });
  });

  describe('Firebase Integration', () => {
    it('handles Firebase login', async () => {
      const user = userEvent.setup();

      // Update auth method to Firebase
      mockUserContext.method = 'FirebaseAuth';

      render(<App />);

      const firebaseButton = screen.queryByText(/sign in.*firebase/i);
      if (firebaseButton) {
        await user.click(firebaseButton);
        expect(mockUserContext.login).toHaveBeenCalled();
      }
    });
  });

  describe('Protected Routes', () => {
    it('redirects unauthenticated users to login', async () => {
      // Try to access a protected route
      window.history.pushState({}, 'Dashboard', '/dashboard');

      render(<App />);

      await waitFor(() => {
        expect(screen.getByText(/sign into your account/i)).toBeInTheDocument();
      });
    });

    it('allows authenticated users to access protected routes', async () => {
      // Set authenticated state
      mockUserContext.isAuthenticated = true;
      mockUserContext.user = {
        id: '1',
        name: 'Test User',
        email: '<EMAIL>',
        profile: { jobtitle: 'Developer' },
      };

      window.history.pushState({}, 'Dashboard', '/dashboard');

      render(<App />);

      await waitFor(() => {
        expect(screen.queryByText(/sign into your account/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Session Management', () => {
    it('handles token expiration', async () => {
      // Start authenticated
      mockUserContext.isAuthenticated = true;
      mockUserContext.user = { id: '1', name: 'Test User' };

      // Mock token expiration response
      mockGetError(401, 'Token expired');

      const { rerender } = render(<App />);

      // Simulate token expiration
      mockUserContext.isAuthenticated = false;
      mockUserContext.user = null;

      rerender(<App />);

      await waitFor(() => {
        expect(screen.getByText(/sign into your account/i)).toBeInTheDocument();
      });
    });
  });
});
