/**
 * Example integration test demonstrating Jest-based axios mock usage
 * Shows how to use <PERSON><PERSON>'s built-in mocking capabilities with axios
 */

import React from 'react';
import { render, screen, waitFor } from '../setup/test-utils';
import userEvent from '@testing-library/user-event';
import {
  mockedAxios,
  mockGetSuccess,
  mockPostSuccess,
  mockGetError,
  mockPostError,
  resetMocks,
  clearMocks
} from '../setup/axios-mock';
import axios from 'axios';

// Mock component that makes API calls
const TestComponent = () => {
  const [data, setData] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);

  const fetchData = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get('/api/test-data');
      setData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const createData = async (newData) => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.post('/api/test-data', newData);
      setData(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <button onClick={fetchData} data-testid="fetch-button">
        Fetch Data
      </button>
      <button 
        onClick={() => createData({ name: 'Test Item' })} 
        data-testid="create-button"
      >
        Create Data
      </button>
      
      {loading && <div data-testid="loading">Loading...</div>}
      {error && <div data-testid="error">{error}</div>}
      {data && (
        <div data-testid="data">
          {JSON.stringify(data)}
        </div>
      )}
    </div>
  );
};

describe('Jest-based Axios Mock System Examples', () => {
  beforeEach(() => {
    // Reset mocks before each test
    resetMocks();
  });

  describe('GET Request Mocking', () => {
    it('should mock successful GET requests', async () => {
      const user = userEvent.setup();

      // Mock successful response
      mockGetSuccess({ id: 1, name: 'Test Item', status: 'active' });

      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      await waitFor(() => {
        expect(screen.getByTestId('data')).toBeInTheDocument();
      });

      expect(screen.getByTestId('data')).toHaveTextContent('Test Item');
      expect(screen.queryByTestId('error')).not.toBeInTheDocument();
    });

    it('should mock GET request errors', async () => {
      const user = userEvent.setup();

      // Mock error response
      mockGetError(500, 'Server Error');

      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error')).toHaveTextContent('Server Error');
      expect(screen.queryByTestId('data')).not.toBeInTheDocument();
    });

    it('should mock 404 errors', async () => {
      const user = userEvent.setup();

      // Mock 404 response
      mockGetError(404, 'Not Found');

      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error')).toHaveTextContent('Not Found');
    });
  });

  describe('POST Request Mocking', () => {
    it('should mock successful POST requests', async () => {
      const user = userEvent.setup();

      // Mock successful creation with dynamic response
      mockedAxios.post.mockImplementation((url, data) => {
        return Promise.resolve({
          data: {
            id: 2,
            ...data,
            createdAt: '2023-12-01T00:00:00Z'
          },
          status: 201,
          statusText: 'Created',
          headers: {},
          config: {},
        });
      });

      render(<TestComponent />);

      await user.click(screen.getByTestId('create-button'));

      await waitFor(() => {
        expect(screen.getByTestId('data')).toBeInTheDocument();
      });

      const dataElement = screen.getByTestId('data');
      expect(dataElement).toHaveTextContent('Test Item');
      expect(dataElement).toHaveTextContent('createdAt');
    });

    it('should mock POST request validation errors', async () => {
      const user = userEvent.setup();

      // Mock validation error
      mockPostError(400, 'Validation failed');

      render(<TestComponent />);

      await user.click(screen.getByTestId('create-button'));

      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error')).toHaveTextContent('Validation failed');
    });
  });

  describe('Dynamic Response Mocking', () => {
    it('should handle dynamic responses based on request data', async () => {
      const user = userEvent.setup();

      // Mock dynamic response based on request
      mockedAxios.post.mockImplementation((url, data) => {
        if (data.name === 'Test Item') {
          return Promise.resolve({
            data: { id: 1, name: data.name, type: 'test' },
            status: 201,
            statusText: 'Created',
            headers: {},
            config: {},
          });
        } else {
          const error = new Error('Invalid item name');
          error.response = {
            data: { message: 'Invalid item name' },
            status: 400,
            statusText: 'Bad Request',
            headers: {},
            config: {},
          };
          return Promise.reject(error);
        }
      });

      render(<TestComponent />);

      await user.click(screen.getByTestId('create-button'));

      await waitFor(() => {
        expect(screen.getByTestId('data')).toBeInTheDocument();
      });

      expect(screen.getByTestId('data')).toHaveTextContent('test');
    });
  });

  describe('Advanced Mocking Scenarios', () => {
    it('should handle specific URL patterns', async () => {
      const user = userEvent.setup();

      // Mock specific endpoint
      mockedAxios.get.mockResolvedValue({
        data: { message: 'Specific endpoint matched' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      await waitFor(() => {
        expect(screen.getByTestId('data')).toBeInTheDocument();
      });

      expect(screen.getByTestId('data')).toHaveTextContent('Specific endpoint matched');
    });

    it('should handle conditional responses', async () => {
      // Mock with conditional logic
      mockedAxios.get.mockImplementation((url) => {
        if (url.includes('items')) {
          return Promise.resolve({
            data: { type: 'item', name: 'Test Item' },
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
          });
        }
        return Promise.resolve({
          data: { type: 'default' },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        });
      });

      const response = await axios.get('/api/items/123');
      expect(response.data.type).toBe('item');
      expect(response.data.name).toBe('Test Item');
    });
  });

  describe('Mock Management', () => {
    it('should reset mocks between tests', async () => {
      const user = userEvent.setup();

      // This test should use default handlers since we reset in beforeEach
      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      // Should get default mock response
      await waitFor(() => {
        expect(
          screen.getByTestId('data') || screen.getByTestId('error')
        ).toBeInTheDocument();
      });
    });

    it('should allow clearing all mocks', async () => {
      const user = userEvent.setup();

      // Clear all mocks
      clearMocks();

      render(<TestComponent />);

      await user.click(screen.getByTestId('fetch-button'));

      // Should get network error since no mocks are set
      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });
    });
  });

  describe('Multiple HTTP Methods', () => {
    it('should handle PUT requests', async () => {
      // Mock PUT request
      mockedAxios.put.mockResolvedValue({
        data: { id: 1, name: 'Updated Item' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const response = await axios.put('/api/test-data/1', { name: 'Updated Item' });

      expect(response.status).toBe(200);
      expect(response.data.name).toBe('Updated Item');
    });

    it('should handle DELETE requests', async () => {
      // Mock DELETE request
      mockedAxios.delete.mockResolvedValue({
        data: null,
        status: 204,
        statusText: 'No Content',
        headers: {},
        config: {},
      });

      const response = await axios.delete('/api/test-data/1');

      expect(response.status).toBe(204);
    });

    it('should handle DELETE errors', async () => {
      // Mock DELETE error
      const error = new Error('Forbidden');
      error.response = {
        data: { message: 'Forbidden' },
        status: 403,
        statusText: 'Forbidden',
        headers: {},
        config: {},
      };
      mockedAxios.delete.mockRejectedValue(error);

      try {
        await axios.delete('/api/test-data/1');
        fail('Should have thrown an error');
      } catch (err) {
        expect(err.response.status).toBe(403);
        expect(err.response.data.message).toBe('Forbidden');
      }
    });
  });
});
