/**
 * Integration tests for SWR error handling with NetSuite configuration errors
 * Tests the complete SWR integration and error propagation
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { SWRConfig, mutate } from 'swr';
import axios from 'axios';

import UserProvider, { UserContext } from '../../contexts/UserContext';
import { SettingsProvider } from '../../contexts/SettingsContext';
import { 
  mockNetSuiteConfigurationError, 
  mockUserSuiteletConfigError,
  resetMocks 
} from '../setup/axios-mock';

// Mock hooks
jest.mock('../../hooks/useSettings', () => ({
  __esModule: true,
  default: () => ({
    custrecord_ng_eh_react_logo_image_url: '/test-logo.png'
  })
}));

jest.mock('../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      core_account_url: 'https://tstdrv2149044.app.netsuite.com'
    }
  })
}));

// Test component that uses SWR directly to test error handling
const DirectSWRTestComponent = () => {
  const [manualError, setManualError] = React.useState(null);
  
  const handleManualFetch = async () => {
    try {
      const response = await axios.get(process.env.REACT_APP_GET_USER_SUITELET);
      setManualError(null);
      return response.data;
    } catch (error) {
      setManualError(error);
      throw error;
    }
  };

  return (
    <div>
      <button data-testid="manual-fetch" onClick={handleManualFetch}>
        Manual Fetch
      </button>
      {manualError && (
        <div data-testid="manual-error">
          <div data-testid="manual-error-message">{manualError.message}</div>
          <div data-testid="manual-error-details">
            {manualError.response?.data?.details?.message || 'No details'}
          </div>
        </div>
      )}
    </div>
  );
};

// Component that tests UserContext SWR integration
const UserContextSWRTestComponent = () => {
  const { user, error, userMutate, nsDataValidating } = React.useContext(UserContext);
  
  const handleRetry = () => {
    userMutate();
  };

  return (
    <div>
      <div data-testid="swr-loading">{nsDataValidating ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="swr-user">{user ? JSON.stringify(user) : 'No User'}</div>
      <div data-testid="swr-error">{error ? error.message : 'No Error'}</div>
      <div data-testid="swr-error-details">
        {error?.response?.data?.details?.message || 'No Error Details'}
      </div>
      <button data-testid="swr-retry" onClick={handleRetry}>
        Retry
      </button>
    </div>
  );
};

// Test wrapper with SWR configuration
const SWRTestWrapper = ({ children, swrConfig = {} }) => (
  <SWRConfig 
    value={{ 
      provider: () => new Map(),
      fetcher: (url) => axios.get(url).then(res => res.data),
      dedupingInterval: 0,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      errorRetryCount: 0, // Disable automatic retries for testing
      ...swrConfig
    }}
  >
    <SettingsProvider>
      <UserProvider>
        {children}
      </UserProvider>
    </SettingsProvider>
  </SWRConfig>
);

describe('SWR Error Handling Integration', () => {
  beforeEach(() => {
    resetMocks();
    
    // Set up environment
    process.env.REACT_APP_GET_USER_SUITELET = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1';
    
    // Mock localStorage and sessionStorage
    Storage.prototype.getItem = jest.fn(() => null);
    Storage.prototype.setItem = jest.fn();
    Storage.prototype.removeItem = jest.fn();
    Storage.prototype.clear = jest.fn();

    // Clear console
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('SWR Configuration Error Handling', () => {
    test('should handle NetSuite configuration errors through SWR', async () => {
      mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      // Initially loading
      expect(screen.getByTestId('swr-loading')).toHaveTextContent('Loading');

      // Wait for error to be processed
      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      }, { timeout: 3000 });

      // Verify error details
      expect(screen.getByTestId('swr-error')).toHaveTextContent('Error getting user information');
      expect(screen.getByTestId('swr-error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
      expect(screen.getByTestId('swr-user')).toHaveTextContent('No User');
      expect(screen.getByTestId('swr-loading')).toHaveTextContent('Not Loading');
    });

    test('should handle SWR retry mechanism with configuration errors', async () => {
      mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      // Wait for initial error
      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      });

      // Mock successful response for retry
      const successResponse = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: { record: { fields: {} } }
        }
      };

      axios.get.mockResolvedValue({ data: successResponse });

      // Trigger retry
      act(() => {
        screen.getByTestId('swr-retry').click();
      });

      // Should show loading during retry
      expect(screen.getByTestId('swr-loading')).toHaveTextContent('Loading');

      // Should recover from error
      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).toHaveTextContent('No Error');
      });

      expect(screen.getByTestId('swr-user')).not.toHaveTextContent('No User');
    });

    test('should handle different configuration error types through SWR', async () => {
      // Test missing sales order printing template
      const salesOrderError = new Error('Error getting user information');
      salesOrderError.response = {
        data: {
          id: 'ERR-1750963949169-859',
          message: 'Error getting user information',
          details: {
            message: '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.',
            customMessage: 'Error getting user information'
          }
        },
        status: 500
      };

      axios.get.mockRejectedValue(salesOrderError);

      render(
        <SWRTestWrapper>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('swr-error-details')).toHaveTextContent(
          '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.'
        );
      });
    });
  });

  describe('SWR Cache and Revalidation', () => {
    test('should handle cache invalidation after configuration errors', async () => {
      mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      });

      // Clear the cache and mock successful response
      await act(async () => {
        await mutate(process.env.REACT_APP_GET_USER_SUITELET, undefined, false);
      });

      const successResponse = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: { record: { fields: {} } }
        }
      };

      axios.get.mockResolvedValue({ data: successResponse });

      // Trigger revalidation
      await act(async () => {
        await mutate(process.env.REACT_APP_GET_USER_SUITELET);
      });

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).toHaveTextContent('No Error');
      });
    });

    test('should handle SWR error boundary scenarios', async () => {
      // Mock a severe error that might cause issues
      const severeError = new Error('Critical system failure');
      severeError.response = { status: 500, data: { message: 'System down' } };
      
      axios.get.mockRejectedValue(severeError);

      // Should not crash the component
      expect(() => {
        render(
          <SWRTestWrapper>
            <UserContextSWRTestComponent />
          </SWRTestWrapper>
        );
      }).not.toThrow();

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).toHaveTextContent('Critical system failure');
      });
    });
  });

  describe('Direct Axios Error Handling', () => {
    test('should handle configuration errors in direct axios calls', async () => {
      mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <DirectSWRTestComponent />
        </SWRTestWrapper>
      );

      // Trigger manual fetch
      act(() => {
        screen.getByTestId('manual-fetch').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('manual-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('manual-error-message')).toHaveTextContent('Error getting user information');
      expect(screen.getByTestId('manual-error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
    });

    test('should preserve error structure in direct calls', async () => {
      const configError = mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <DirectSWRTestComponent />
        </SWRTestWrapper>
      );

      act(() => {
        screen.getByTestId('manual-fetch').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('manual-error')).toBeInTheDocument();
      });

      // Verify the error structure is preserved
      expect(configError.response.data).toEqual(mockNetSuiteConfigurationError);
    });
  });

  describe('SWR Configuration Options', () => {
    test('should handle errors with custom SWR configuration', async () => {
      mockUserSuiteletConfigError();

      const customSWRConfig = {
        errorRetryCount: 2,
        errorRetryInterval: 100,
        onError: jest.fn()
      };

      render(
        <SWRTestWrapper swrConfig={customSWRConfig}>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      });

      // Should call custom error handler
      expect(customSWRConfig.onError).toHaveBeenCalled();
    });

    test('should handle SWR fallback data on error', async () => {
      mockUserSuiteletConfigError();

      const fallbackData = {
        success: false,
        data: {
          id: 'fallback',
          name: 'Fallback User',
          email: '<EMAIL>'
        }
      };

      const customSWRConfig = {
        fallbackData
      };

      render(
        <SWRTestWrapper swrConfig={customSWRConfig}>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      // Should initially show fallback data
      expect(screen.getByTestId('swr-user')).toHaveTextContent('Fallback User');

      // Then show error after fetch fails
      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      });
    });
  });

  describe('Error Recovery Patterns', () => {
    test('should handle error recovery with exponential backoff', async () => {
      let callCount = 0;
      
      axios.get.mockImplementation(() => {
        callCount++;
        if (callCount <= 2) {
          return Promise.reject(mockUserSuiteletConfigError());
        }
        return Promise.resolve({
          data: {
            success: true,
            data: { id: '1', name: 'Test User', email: '<EMAIL>' }
          }
        });
      });

      const customSWRConfig = {
        errorRetryCount: 3,
        errorRetryInterval: 50
      };

      render(
        <SWRTestWrapper swrConfig={customSWRConfig}>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      // Should eventually succeed after retries
      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).toHaveTextContent('No Error');
      }, { timeout: 5000 });

      expect(callCount).toBeGreaterThan(2);
    });

    test('should handle partial error recovery', async () => {
      // First call fails with config error
      mockUserSuiteletConfigError();

      render(
        <SWRTestWrapper>
          <UserContextSWRTestComponent />
        </SWRTestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).not.toHaveTextContent('No Error');
      });

      // Second call succeeds but with partial data
      const partialSuccessResponse = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: null // Partial data
        }
      };

      axios.get.mockResolvedValue({ data: partialSuccessResponse });

      // Trigger retry
      act(() => {
        screen.getByTestId('swr-retry').click();
      });

      await waitFor(() => {
        expect(screen.getByTestId('swr-error')).toHaveTextContent('No Error');
      });

      // Should handle partial data gracefully
      expect(screen.getByTestId('swr-user')).toHaveTextContent('Test User');
    });
  });
});
