/**
 * Integration tests for Settings and User context interaction
 * Tests how settings configuration errors affect user data loading and app initialization
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import axios from 'axios';
import { SnackbarProvider } from 'notistack';
import useSWR from 'swr';

import { PureLightTheme } from '../../theme/schemes/PureLightTheme';
import { 
  mockValidSettings,
  mockSettingsWithMissingPrintingTemplates,
  mockMinimalValidSettings,
  mockSettingsSuccess,
  mockSettingsMissingPrintingTemplates,
  mockUserSuiteletConfigError,
  resetMocks 
} from '../setup/axios-mock';

const theme = PureLightTheme;

// Mock app initialization component that loads both settings and user data
const AppInitializationComponent = ({ settingsUrl, userUrl }) => {
  const { data: settings, error: settingsError, mutate: settingsMutate } = useSWR(
    settingsUrl, 
    (url) => axios.get(url).then(res => res.data)
  );
  
  const { data: user, error: userError, mutate: userMutate } = useSWR(
    userUrl, 
    (url) => axios.get(url).then(res => res.data)
  );
  
  // Validate settings
  const validateSettings = (settingsData) => {
    if (!settingsData) return { isValid: false, errors: ['No settings data'] };
    
    const errors = [];
    const requiredFields = [
      'custrecord_ng_eh_default_portal_ordform',
      'custrecord_ng_eh_invoice_printing_temp',
      'custrecord_ng_eh_sales_order_printing_te'
    ];
    
    requiredFields.forEach(field => {
      if (!settingsData[field] || settingsData[field].length === 0) {
        errors.push(`Missing required field: ${field}`);
      }
    });
    
    return { isValid: errors.length === 0, errors };
  };
  
  // Handle errors
  if (settingsError || userError) {
    return (
      <div data-testid="app-initialization-error">
        <div data-testid="error-title">Application Initialization Error</div>
        
        {settingsError && (
          <div data-testid="settings-error-section">
            <div data-testid="settings-error-message">
              Settings Error: {settingsError.message}
            </div>
            <button data-testid="retry-settings" onClick={() => settingsMutate()}>
              Retry Settings
            </button>
          </div>
        )}
        
        {userError && (
          <div data-testid="user-error-section">
            <div data-testid="user-error-message">
              User Error: {userError.message}
            </div>
            <div data-testid="user-error-details">
              {userError.response?.data?.details?.message || 'No user error details'}
            </div>
            <button data-testid="retry-user" onClick={() => userMutate()}>
              Retry User
            </button>
          </div>
        )}
        
        <div data-testid="admin-contact-notice">
          Please contact your system administrator to resolve configuration issues.
        </div>
      </div>
    );
  }
  
  // Handle loading states
  if (!settings || !user) {
    return (
      <div data-testid="app-loading">
        <div data-testid="loading-message">Initializing application...</div>
        <div data-testid="loading-details">
          {!settings && <div>Loading settings...</div>}
          {!user && <div>Loading user data...</div>}
        </div>
      </div>
    );
  }
  
  // Validate settings before proceeding
  const settingsValidation = validateSettings(settings);
  
  if (!settingsValidation.isValid) {
    return (
      <div data-testid="app-configuration-error">
        <div data-testid="configuration-error-title">Configuration Error</div>
        <div data-testid="configuration-errors">
          {settingsValidation.errors.map((error, index) => (
            <div key={index} data-testid={`config-error-${index}`}>
              ❌ {error}
            </div>
          ))}
        </div>
        <div data-testid="configuration-impact">
          The application cannot start due to missing configuration. User data loaded successfully but cannot be used.
        </div>
        <button data-testid="retry-settings" onClick={() => settingsMutate()}>
          Retry Settings
        </button>
      </div>
    );
  }
  
  // Success state
  return (
    <div data-testid="app-ready">
      <div data-testid="app-title">Application Ready</div>
      <div data-testid="user-info">
        Welcome, {user.data?.name || user.name || 'User'}
      </div>
      <div data-testid="settings-info">
        Settings loaded with {Object.keys(settings).length} configuration items
      </div>
      <div data-testid="app-features">
        <div data-testid="portal-feature">
          Portal Orders: {settings.custrecord_ng_eh_default_portal_ordform?.[0]?.text ? 'Available' : 'Unavailable'}
        </div>
        <div data-testid="invoice-feature">
          Invoice Printing: {settings.custrecord_ng_eh_invoice_printing_temp?.[0]?.text ? 'Available' : 'Unavailable'}
        </div>
        <div data-testid="sales-order-feature">
          Sales Order Printing: {settings.custrecord_ng_eh_sales_order_printing_te?.[0]?.text ? 'Available' : 'Unavailable'}
        </div>
      </div>
    </div>
  );
};

// Test wrapper
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      <SWRConfig 
        value={{ 
          provider: () => new Map(),
          fetcher: (url) => axios.get(url).then(res => res.data),
          dedupingInterval: 0,
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
          errorRetryCount: 0,
        }}
      >
        {children}
      </SWRConfig>
    </SnackbarProvider>
  </ThemeProvider>
);

describe('Settings and User Context Integration', () => {
  const settingsUrl = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=918&deploy=1';
  const userUrl = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1';

  beforeEach(() => {
    resetMocks();
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful App Initialization', () => {
    test('should initialize app successfully with valid settings and user data', async () => {
      // Mock both endpoints to succeed
      mockSettingsSuccess(mockValidSettings);
      // User endpoint is mocked by default in setupDefaultMocks

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      // Initially should show loading
      expect(screen.getByTestId('app-loading')).toBeInTheDocument();

      // Wait for app to be ready
      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });

      // Verify all features are available
      expect(screen.getByTestId('portal-feature')).toHaveTextContent('Portal Orders: Available');
      expect(screen.getByTestId('invoice-feature')).toHaveTextContent('Invoice Printing: Available');
      expect(screen.getByTestId('sales-order-feature')).toHaveTextContent('Sales Order Printing: Available');
    });

    test('should initialize app with minimal valid settings', async () => {
      mockSettingsSuccess(mockMinimalValidSettings);

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });

      expect(screen.getByTestId('app-title')).toHaveTextContent('Application Ready');
    });
  });

  describe('Settings Configuration Errors', () => {
    test('should block app initialization when settings are missing required fields', async () => {
      mockSettingsMissingPrintingTemplates();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-configuration-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('configuration-error-title')).toHaveTextContent('Configuration Error');
      expect(screen.getByTestId('config-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_default_portal_ordform');
      expect(screen.getByTestId('config-error-1')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_invoice_printing_temp');
      expect(screen.getByTestId('config-error-2')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_sales_order_printing_te');
      expect(screen.getByTestId('configuration-impact')).toHaveTextContent(
        'The application cannot start due to missing configuration. User data loaded successfully but cannot be used.'
      );
    });

    test('should allow recovery after settings configuration is fixed', async () => {
      // Start with missing configuration
      mockSettingsMissingPrintingTemplates();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-configuration-error')).toBeInTheDocument();
      });

      // Mock fixed configuration
      mockSettingsSuccess(mockValidSettings);

      // Click retry settings
      fireEvent.click(screen.getByTestId('retry-settings'));

      // Should now be ready
      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });
    });
  });

  describe('User Data Errors with Valid Settings', () => {
    test('should handle user data errors when settings are valid', async () => {
      mockSettingsSuccess(mockValidSettings);
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('user-error-message')).toHaveTextContent('User Error: Error getting user information');
      expect(screen.getByTestId('user-error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
      expect(screen.queryByTestId('settings-error-section')).not.toBeInTheDocument(); // Settings should be fine
    });

    test('should allow user data retry when settings are valid', async () => {
      mockSettingsSuccess(mockValidSettings);
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      // Reset mocks to allow user success
      resetMocks();
      mockSettingsSuccess(mockValidSettings); // Keep settings working

      // Click retry user
      fireEvent.click(screen.getByTestId('retry-user'));

      // Should now be ready
      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });
    });
  });

  describe('Combined Settings and User Errors', () => {
    test('should handle both settings and user errors simultaneously', async () => {
      // Import the combined mock function
      const { mockBothSettingsAndUserErrors } = require('../setup/axios-mock');
      mockBothSettingsAndUserErrors();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      // Should show both error sections
      expect(screen.getByTestId('settings-error-section')).toBeInTheDocument();
      expect(screen.getByTestId('user-error-section')).toBeInTheDocument();
      expect(screen.getByTestId('admin-contact-notice')).toHaveTextContent(
        'Please contact your system administrator to resolve configuration issues.'
      );
    });

    test('should handle sequential error recovery', async () => {
      // Start with both errors
      const { mockBothSettingsAndUserErrors } = require('../setup/axios-mock');
      mockBothSettingsAndUserErrors();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      // Fix settings first
      mockSettingsSuccess(mockValidSettings);
      fireEvent.click(screen.getByTestId('retry-settings'));

      // Should still show user error
      await waitFor(() => {
        expect(screen.getByTestId('user-error-section')).toBeInTheDocument();
      });
      expect(screen.queryByTestId('settings-error-section')).not.toBeInTheDocument();

      // Fix user data
      resetMocks();
      mockSettingsSuccess(mockValidSettings);
      fireEvent.click(screen.getByTestId('retry-user'));

      // Should now be ready
      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });
    });
  });

  describe('Loading State Management', () => {
    test('should show appropriate loading states during initialization', async () => {
      // Delay the responses to test loading states
      let settingsResolve, userResolve;
      const settingsPromise = new Promise(resolve => { settingsResolve = resolve; });
      const userPromise = new Promise(resolve => { userResolve = resolve; });

      axios.get.mockImplementation((url) => {
        if (url.includes('script=918')) {
          return settingsPromise;
        }
        if (url.includes('script=902')) {
          return userPromise;
        }
        return Promise.resolve({ data: {} });
      });

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      // Should show loading
      expect(screen.getByTestId('app-loading')).toBeInTheDocument();
      expect(screen.getByTestId('loading-message')).toHaveTextContent('Initializing application...');

      // Resolve settings first
      settingsResolve({ data: mockValidSettings });

      await waitFor(() => {
        expect(screen.getByText('Loading user data...')).toBeInTheDocument();
      });

      // Resolve user data
      userResolve({ 
        data: { 
          success: true, 
          data: { id: '1', name: 'Test User', email: '<EMAIL>' } 
        } 
      });

      await waitFor(() => {
        expect(screen.getByTestId('app-ready')).toBeInTheDocument();
      });
    });
  });

  describe('Error Recovery Patterns', () => {
    test('should maintain user data when only settings fail', async () => {
      // Settings fail, user succeeds
      const settingsError = new Error('Settings network error');
      axios.get.mockImplementation((url) => {
        if (url.includes('script=918')) {
          return Promise.reject(settingsError);
        }
        if (url.includes('script=902')) {
          return Promise.resolve({ 
            data: { 
              success: true, 
              data: { id: '1', name: 'Test User', email: '<EMAIL>' } 
            } 
          });
        }
        return Promise.resolve({ data: {} });
      });

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      // Should show settings error but not user error
      expect(screen.getByTestId('settings-error-section')).toBeInTheDocument();
      expect(screen.queryByTestId('user-error-section')).not.toBeInTheDocument();
    });

    test('should maintain settings data when only user fails', async () => {
      mockSettingsSuccess(mockValidSettings);
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <AppInitializationComponent settingsUrl={settingsUrl} userUrl={userUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('app-initialization-error')).toBeInTheDocument();
      });

      // Should show user error but not settings error
      expect(screen.getByTestId('user-error-section')).toBeInTheDocument();
      expect(screen.queryByTestId('settings-error-section')).not.toBeInTheDocument();
    });
  });
});
