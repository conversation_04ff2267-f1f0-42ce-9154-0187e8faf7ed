/**
 * Simple integration tests for NetSuite configuration error handling
 * Tests the core error handling functionality without complex context dependencies
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import axios from 'axios';
import { SnackbarProvider } from 'notistack';
import useSWR from 'swr';

import { PureLightTheme } from '../../theme/schemes/PureLightTheme';

const theme = PureLightTheme;
import { 
  mockNetSuiteConfigurationError, 
  mockUserSuiteletConfigError,
  resetMocks 
} from '../setup/axios-mock';

// Simple test component that uses SWR to fetch user data
const UserDataComponent = ({ url }) => {
  const { data, error, mutate } = useSWR(url, (url) => axios.get(url).then(res => res.data));
  
  if (error) {
    return (
      <div data-testid="error-container">
        <div data-testid="error-message">Error: {error.message}</div>
        <div data-testid="error-details">
          {error.response?.data?.details?.message || 'No error details'}
        </div>
        <div data-testid="error-id">
          {error.response?.data?.id || 'No error ID'}
        </div>
        <button data-testid="retry-button" onClick={() => mutate()}>
          Retry
        </button>
      </div>
    );
  }
  
  if (!data) {
    return <div data-testid="loading">Loading...</div>;
  }
  
  return (
    <div data-testid="user-data">
      <div data-testid="user-name">{data.data?.name || 'No name'}</div>
      <div data-testid="user-email">{data.data?.email || 'No email'}</div>
    </div>
  );
};

// Test wrapper
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      <SWRConfig 
        value={{ 
          provider: () => new Map(),
          fetcher: (url) => axios.get(url).then(res => res.data),
          dedupingInterval: 0,
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
          errorRetryCount: 0,
        }}
      >
        {children}
      </SWRConfig>
    </SnackbarProvider>
  </ThemeProvider>
);

describe('NetSuite Configuration Error Handling - Simple', () => {
  const testUrl = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1';

  beforeEach(() => {
    resetMocks();
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Configuration Error Display', () => {
    test('should display printout template configuration error', async () => {
      // Mock the configuration error
      const configError = new Error('Error getting user information');
      configError.response = {
        data: mockNetSuiteConfigurationError,
        status: 500,
        statusText: 'Internal Server Error'
      };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      // Initially should show loading
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Wait for error to be displayed
      await waitFor(() => {
        expect(screen.getByTestId('error-container')).toBeInTheDocument();
      });

      // Verify error details
      expect(screen.getByTestId('error-message')).toHaveTextContent('Error: Error getting user information');
      expect(screen.getByTestId('error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
      expect(screen.getByTestId('error-id')).toHaveTextContent('ERR-1750963949169-856');
    });

    test('should display missing invoice printing template error', async () => {
      const invoiceError = new Error('Error getting user information');
      invoiceError.response = {
        data: {
          id: 'ERR-1750963949169-858',
          message: 'Error getting user information',
          details: {
            message: '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.',
            customMessage: 'Error getting user information'
          }
        },
        status: 500
      };

      axios.get.mockRejectedValue(invoiceError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.'
        );
      });
    });

    test('should display sales order printing template error', async () => {
      const salesOrderError = new Error('Error getting user information');
      salesOrderError.response = {
        data: {
          id: 'ERR-1750963949169-859',
          details: {
            message: '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.',
          }
        },
        status: 500
      };

      axios.get.mockRejectedValue(salesOrderError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.'
        );
      });
    });

    test('should display default portal order form error', async () => {
      const portalFormError = new Error('Error getting user information');
      portalFormError.response = {
        data: {
          id: 'ERR-1750963949169-860',
          details: {
            message: '❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.',
          }
        },
        status: 500
      };

      axios.get.mockRejectedValue(portalFormError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.'
        );
      });
    });
  });

  describe('Error Recovery', () => {
    test('should allow retry after configuration error', async () => {
      // Start with error
      const configError = new Error('Error getting user information');
      configError.response = {
        data: mockNetSuiteConfigurationError,
        status: 500
      };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-container')).toBeInTheDocument();
      });

      // Mock successful response for retry
      const successResponse = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>'
        }
      };

      axios.get.mockResolvedValue({ data: successResponse });

      // Click retry button
      fireEvent.click(screen.getByTestId('retry-button'));

      // Wait for retry to complete (SWR might not show loading state immediately)
      await waitFor(() => {
        expect(screen.queryByTestId('error-container')).not.toBeInTheDocument();
      });

      // Should show success after retry
      await waitFor(() => {
        expect(screen.getByTestId('user-data')).toBeInTheDocument();
      });

      expect(screen.getByTestId('user-name')).toHaveTextContent('Test User');
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
    });

    test('should handle network errors differently', async () => {
      const networkError = new Error('Network Error');
      networkError.code = 'NETWORK_ERROR';
      
      axios.get.mockRejectedValue(networkError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Error: Network Error');
      });

      expect(screen.getByTestId('error-details')).toHaveTextContent('No error details');
    });

    test('should handle timeout errors', async () => {
      const timeoutError = new Error('timeout of 5000ms exceeded');
      timeoutError.code = 'ECONNABORTED';
      
      axios.get.mockRejectedValue(timeoutError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Error: timeout of 5000ms exceeded');
      });
    });
  });

  describe('Error Structure Validation', () => {
    test('should preserve complete error structure', async () => {
      const configError = new Error('Error getting user information');
      configError.response = {
        data: mockNetSuiteConfigurationError,
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {}
      };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-container')).toBeInTheDocument();
      });

      // Verify the complete error structure is preserved
      expect(configError.response.data).toEqual(mockNetSuiteConfigurationError);
      expect(configError.response.data.id).toBe('ERR-1750963949169-856');
      expect(configError.response.data.message).toBe('Error getting user information');
      expect(configError.response.data.details.customMessage).toBe('Error getting user information');
      expect(configError.response.data.details.stack).toContain('ng_cs_sl_get_current_user.js:676:15');
    });

    test('should handle malformed error responses', async () => {
      const malformedError = new Error('Malformed response');
      malformedError.response = {
        data: 'Invalid JSON response',
        status: 500
      };

      axios.get.mockRejectedValue(malformedError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toHaveTextContent('Error: Malformed response');
      });

      expect(screen.getByTestId('error-details')).toHaveTextContent('No error details');
      expect(screen.getByTestId('error-id')).toHaveTextContent('No error ID');
    });
  });

  describe('User Experience', () => {
    test('should provide clear configuration error messaging', async () => {
      const configError = new Error('Error getting user information');
      configError.response = {
        data: mockNetSuiteConfigurationError,
        status: 500
      };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toBeInTheDocument();
      });

      const errorDetails = screen.getByTestId('error-details');
      
      // Verify the error message is user-friendly and actionable
      expect(errorDetails).toHaveTextContent('❌ Printout templates are required in settings');
      expect(errorDetails).toHaveTextContent('Please configure Sales Order and Invoice printing templates');
    });

    test('should provide retry functionality', async () => {
      const configError = new Error('Error getting user information');
      configError.response = { data: mockNetSuiteConfigurationError, status: 500 };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('retry-button')).toBeInTheDocument();
      });

      // Retry button should be accessible
      const retryButton = screen.getByTestId('retry-button');
      expect(retryButton).toHaveTextContent('Retry');
      expect(retryButton).toBeEnabled();
    });
  });

  describe('API Call Patterns', () => {
    test('should handle the exact NetSuite user suitelet endpoint', async () => {
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-container')).toBeInTheDocument();
      });

      // Verify the correct endpoint was called
      expect(axios.get).toHaveBeenCalledWith(testUrl);
    });

    test('should handle successful user data response', async () => {
      const successResponse = {
        success: true,
        timestamp: new Date().toISOString(),
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          companyName: 'Test Company',
          profile: {
            news: [],
            record: { fields: {} },
            recentOrders: [],
            recentInvoices: [],
            event: { booths: { sizes: [] } },
            projects: { tasks: [], list: [], jobs: [] }
          }
        }
      };

      axios.get.mockResolvedValue({ data: successResponse });

      render(
        <TestWrapper>
          <UserDataComponent url={testUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-data')).toBeInTheDocument();
      });

      expect(screen.getByTestId('user-name')).toHaveTextContent('Test User');
      expect(screen.getByTestId('user-email')).toHaveTextContent('<EMAIL>');
    });
  });
});
