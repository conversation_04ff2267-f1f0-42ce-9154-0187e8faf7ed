/**
 * Integration tests for project management functionality
 * Tests complete project CRUD workflows with API integration
 */

import React from 'react';
import { render, screen, waitFor } from '../setup/test-utils';
import userEvent from '@testing-library/user-event';
import {
  mockedAxios,
  mockGetSuccess,
  mockGetError,
  mockPostError,
  mockPutError,
  mockDeleteError
} from '../setup/axios-mock';
import ProjectDetail from '../../content/management/Projects/single';
import ManagementProjects from '../../content/management/Projects';

// Mock dependencies
jest.mock('../../hooks/useAuth', () => ({
  __esModule: true,
  default: () => ({
    isAuthenticated: true,
    user: {
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      profile: { jobtitle: 'Developer' },
    },
  }),
}));

jest.mock('../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      get_project_form_data_external: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1188&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQr4q1aTMvTUD4DkrSv281stoI8ovOBrJN6RgoycOJ56w',
      get_project_form_data_internal: 'https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1188&deploy=1&compid=TSTDRV2149044',
      get_item_order_history_external: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1120&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQYxVEA1QZel-WYV8Gnmr_ZRIe-tjnr2ytSGFJhBfpsyk',
      get_item_order_history_internal: 'https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1120&deploy=1&compid=TSTDRV2149044',
    },
  }),
}));

jest.mock('../../hooks/useSettings', () => ({
  __esModule: true,
  default: () => ({
    theme: 'light',
    language: 'en',
  }),
}));

// Mock react-router-dom
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useParams: () => ({ projectId: '1' }),
  useNavigate: () => jest.fn(),
}));

describe('Project Management Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Project List', () => {
    it('loads and displays projects', async () => {
      render(<ManagementProjects />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
        expect(screen.getByText('Test Project 2')).toBeInTheDocument();
      });
    });

    it('handles loading state', () => {
      // Mock delayed response
      mockedAxios.get.mockImplementation(() => {
        return new Promise((resolve) => {
          setTimeout(() => {
            resolve({
              data: [],
              status: 200,
              statusText: 'OK',
              headers: {},
              config: {},
            });
          }, 1000);
        });
      });

      render(<ManagementProjects />);

      expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
    });

    it('handles error state', async () => {
      mockGetError(500, 'Server error');

      render(<ManagementProjects />);

      await waitFor(() => {
        expect(screen.getByText(/error loading projects/i)).toBeInTheDocument();
      });
    });

    it('filters projects by search term', async () => {
      const user = userEvent.setup();

      render(<ManagementProjects />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(/search projects/i);
      await user.type(searchInput, 'Project 1');

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
        expect(screen.queryByText('Test Project 2')).not.toBeInTheDocument();
      });
    });
  });

  describe('Project Detail', () => {
    it('loads and displays project details', async () => {
      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
        expect(screen.getByText('First test project')).toBeInTheDocument();
      });
    });

    it('displays project tabs', async () => {
      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /tasks/i })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /items/i })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /files/i })).toBeInTheDocument();
        expect(screen.getByRole('tab', { name: /cases/i })).toBeInTheDocument();
      });
    });

    it('switches between tabs', async () => {
      const user = userEvent.setup();

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /tasks/i })).toBeInTheDocument();
      });

      const itemsTab = screen.getByRole('tab', { name: /items/i });
      await user.click(itemsTab);

      expect(itemsTab).toHaveAttribute('aria-selected', 'true');
    });

    it('handles project not found', async () => {
      mockGetError(404, 'Project not found');

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText(/project not found/i)).toBeInTheDocument();
      });
    });
  });

  describe('Project Creation', () => {
    it('creates a new project successfully', async () => {
      const user = userEvent.setup();

      render(<ManagementProjects />);

      const createButton = screen.getByRole('button', { name: /create project/i });
      await user.click(createButton);

      // Fill out project form
      const nameInput = screen.getByLabelText(/project name/i);
      const descriptionInput = screen.getByLabelText(/description/i);

      await user.type(nameInput, 'New Test Project');
      await user.type(descriptionInput, 'This is a new test project');

      const submitButton = screen.getByRole('button', { name: /create/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText('Project created successfully')).toBeInTheDocument();
      });
    });

    it('handles project creation error', async () => {
      const user = userEvent.setup();

      mockPostError(400, 'Validation error');

      render(<ManagementProjects />);

      const createButton = screen.getByRole('button', { name: /create project/i });
      await user.click(createButton);

      const nameInput = screen.getByLabelText(/project name/i);
      await user.type(nameInput, 'Invalid Project');

      const submitButton = screen.getByRole('button', { name: /create/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/validation error/i)).toBeInTheDocument();
      });
    });

    it('validates required fields', async () => {
      const user = userEvent.setup();

      render(<ManagementProjects />);

      const createButton = screen.getByRole('button', { name: /create project/i });
      await user.click(createButton);

      const submitButton = screen.getByRole('button', { name: /create/i });
      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/project name is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Project Editing', () => {
    it('updates project successfully', async () => {
      const user = userEvent.setup();

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      });

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      const nameInput = screen.getByDisplayValue('Test Project 1');
      await user.clear(nameInput);
      await user.type(nameInput, 'Updated Project Name');

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText('Project updated successfully')).toBeInTheDocument();
      });
    });

    it('handles update error', async () => {
      const user = userEvent.setup();

      mockPutError(500, 'Update failed');

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      });

      const editButton = screen.getByRole('button', { name: /edit/i });
      await user.click(editButton);

      const saveButton = screen.getByRole('button', { name: /save/i });
      await user.click(saveButton);

      await waitFor(() => {
        expect(screen.getByText(/update failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Project Deletion', () => {
    it('deletes project successfully', async () => {
      const user = userEvent.setup();

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      });

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      // Confirm deletion
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.getByText('Project deleted successfully')).toBeInTheDocument();
      });
    });

    it('handles deletion error', async () => {
      const user = userEvent.setup();

      mockDeleteError(500, 'Deletion failed');

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByText('Test Project 1')).toBeInTheDocument();
      });

      const deleteButton = screen.getByRole('button', { name: /delete/i });
      await user.click(deleteButton);

      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);

      await waitFor(() => {
        expect(screen.getByText(/deletion failed/i)).toBeInTheDocument();
      });
    });
  });

  describe('Project Tasks', () => {
    it('displays project tasks', async () => {
      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /tasks/i })).toBeInTheDocument();
      });

      const tasksTab = screen.getByRole('tab', { name: /tasks/i });
      expect(tasksTab).toHaveAttribute('aria-selected', 'true');
    });

    it('adds new task to project', async () => {
      const user = userEvent.setup();

      render(<ProjectDetail />);

      await waitFor(() => {
        expect(screen.getByRole('tab', { name: /tasks/i })).toBeInTheDocument();
      });

      const addTaskButton = screen.getByRole('button', { name: /add task/i });
      await user.click(addTaskButton);

      const taskNameInput = screen.getByLabelText(/task name/i);
      await user.type(taskNameInput, 'New Task');

      const saveTaskButton = screen.getByRole('button', { name: /save task/i });
      await user.click(saveTaskButton);

      await waitFor(() => {
        expect(screen.getByText('Task added successfully')).toBeInTheDocument();
      });
    });
  });
});
