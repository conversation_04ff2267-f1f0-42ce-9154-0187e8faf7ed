/**
 * Integration tests for NetSuite deployment URLs
 * Tests the comprehensive NetSuite deployment URL mock data
 */

import React from 'react';
import { render, screen, waitFor } from '../setup/test-utils';
import { mockGetSuccess, mockedAxios, mockDeploymentUrls } from '../setup/axios-mock';
import axios from 'axios';

// Mock component that uses deployment URLs
const DeploymentUrlsComponent = () => {
  const [deploymentUrls, setDeploymentUrls] = React.useState(null);
  const [loading, setLoading] = React.useState(false);
  const [error, setError] = React.useState(null);

  const fetchDeploymentUrls = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await axios.get('/app/site/hosting/scriptlet.nl');
      setDeploymentUrls(response.data);
    } catch (err) {
      setError(err.response?.data?.message || 'Failed to fetch deployment URLs');
    } finally {
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchDeploymentUrls();
  }, []);

  if (loading) return <div data-testid="loading">Loading...</div>;
  if (error) return <div data-testid="error">{error}</div>;
  if (!deploymentUrls) return <div data-testid="no-data">No data</div>;

  return (
    <div data-testid="deployment-urls">
      <div data-testid="success-status">{deploymentUrls.success ? 'Success' : 'Failed'}</div>
      <div data-testid="core-account-url">{deploymentUrls.core_account_url}</div>
      <div data-testid="logout-url">{deploymentUrls.logout_url}</div>
      <div data-testid="project-form-internal">{deploymentUrls.get_project_form_data_internal}</div>
      <div data-testid="project-form-external">{deploymentUrls.get_project_form_data_external}</div>
      <div data-testid="order-history-internal">{deploymentUrls.get_item_order_history_internal}</div>
      <div data-testid="order-history-external">{deploymentUrls.get_item_order_history_external}</div>
      <div data-testid="cs-chat-url">{deploymentUrls.get_cs_chat_url}</div>
    </div>
  );
};

describe('NetSuite Deployment URLs Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Deployment URLs Fetching', () => {
    it('should fetch and display comprehensive NetSuite deployment URLs', async () => {
      render(<DeploymentUrlsComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('deployment-urls')).toBeInTheDocument();
      });

      // Verify success status
      expect(screen.getByTestId('success-status')).toHaveTextContent('Success');

      // Verify core NetSuite URLs
      expect(screen.getByTestId('core-account-url')).toHaveTextContent('https://tstdrv2149044.app.netsuite.com');
      expect(screen.getByTestId('logout-url')).toHaveTextContent('https://tstdrv2149044.app.netsuite.com/pages/nllogoutnoback.jsp');

      // Verify project form URLs
      expect(screen.getByTestId('project-form-internal')).toHaveTextContent('script=1188');
      expect(screen.getByTestId('project-form-external')).toHaveTextContent('extforms.netsuite.com');
      expect(screen.getByTestId('project-form-external')).toHaveTextContent('ns-at=');

      // Verify order history URLs
      expect(screen.getByTestId('order-history-internal')).toHaveTextContent('script=1120');
      expect(screen.getByTestId('order-history-external')).toHaveTextContent('extforms.netsuite.com');

      // Verify customer service chat URL
      expect(screen.getByTestId('cs-chat-url')).toHaveTextContent('core/media/media.nl');
    });

    it('should include all required NetSuite endpoint URLs', async () => {
      const response = await axios.get('/app/site/hosting/scriptlet.nl');
      const urls = response.data;

      // Verify all required URLs are present
      expect(urls.success).toBe(true);
      expect(urls.core_account_url).toContain('tstdrv2149044.app.netsuite.com');
      expect(urls.logout_url).toContain('nllogoutnoback.jsp');

      // Project task metrics
      expect(urls.get_project_task_metrics).toContain('script=916');
      expect(urls.get_project_task_metrics_external).toContain('extforms.netsuite.com');

      // Record operations
      expect(urls.record_operation_internal).toContain('script=920');
      expect(urls.record_operation_external).toContain('script=920');

      // Project details
      expect(urls.get_project_details_internal).toContain('script=897');
      expect(urls.get_project_details_external).toContain('script=897');

      // Reports
      expect(urls.get_reports_internal).toContain('script=1145');
      expect(urls.get_reports_external).toContain('script=1145');

      // Web orders
      expect(urls.post_web_order_internal).toContain('script=1037');
      expect(urls.post_web_order_external).toContain('script=1037');

      // Customer cases
      expect(urls.get_customer_cases_internal).toContain('script=1028');
      expect(urls.get_customer_cases_external).toContain('script=1028');
      expect(urls.get_customer_case_internal).toContain('script=1029');
      expect(urls.get_customer_case_external).toContain('script=1029');

      // Customer items
      expect(urls.get_customer_items_internal).toContain('script=1036');
      expect(urls.get_customer_items_external).toContain('script=1036');

      // Item rental inventory
      expect(urls.get_item_rental_inventory_internal).toContain('script=890');
      expect(urls.get_item_rental_inventory_external).toContain('script=890');

      // Calendar events
      expect(urls.get_calendar_events_internal).toContain('script=1119');
      expect(urls.get_calendar_events_external).toContain('script=1119');

      // Item order history
      expect(urls.get_item_order_history_internal).toContain('script=1120');
      expect(urls.get_item_order_history_external).toContain('script=1120');

      // Location info
      expect(urls.get_location_info_internal).toContain('script=1168');
      expect(urls.get_location_info_external).toContain('script=1168');

      // Project form data
      expect(urls.get_project_form_data_internal).toContain('script=1188');
      expect(urls.get_project_form_data_external).toContain('script=1188');

      // Customer service chat
      expect(urls.get_cs_chat_url).toContain('core/media/media.nl');
    });

    it('should verify external URLs contain authentication tokens', async () => {
      const response = await axios.get('/app/site/hosting/scriptlet.nl');
      const urls = response.data;

      // All external URLs should contain ns-at parameter for authentication
      const externalUrls = [
        urls.get_project_task_metrics_external,
        urls.record_operation_external,
        urls.get_project_details_external,
        urls.get_reports_external,
        urls.post_web_order_external,
        urls.get_customer_cases_external,
        urls.get_customer_case_external,
        urls.get_customer_items_external,
        urls.get_item_rental_inventory_external,
        urls.get_calendar_events_external,
        urls.get_item_order_history_external,
        urls.get_location_info_external,
        urls.get_project_form_data_external,
      ];

      externalUrls.forEach((url) => {
        expect(url).toContain('extforms.netsuite.com');
        expect(url).toContain('ns-at=');
        expect(url).toContain('AAEJ7tMQ'); // Token prefix
      });
    });

    it('should verify internal URLs use correct domain', async () => {
      const response = await axios.get('/app/site/hosting/scriptlet.nl');
      const urls = response.data;

      // All internal URLs should use the main NetSuite domain
      const internalUrls = [
        urls.core_account_url,
        urls.logout_url,
        urls.get_project_task_metrics,
        urls.record_operation_internal,
        urls.get_project_details_internal,
        urls.get_reports_internal,
        urls.post_web_order_internal,
        urls.get_customer_cases_internal,
        urls.get_customer_case_internal,
        urls.get_customer_items_internal,
        urls.get_item_rental_inventory_internal,
        urls.get_calendar_events_internal,
        urls.get_item_order_history_internal,
        urls.get_location_info_internal,
        urls.get_project_form_data_internal,
        urls.get_cs_chat_url,
      ];

      internalUrls.forEach((url) => {
        expect(url).toContain('tstdrv2149044.app.netsuite.com');
      });
    });
  });

  describe('NetSuite Endpoint Testing', () => {
    it('should handle project form data requests', async () => {
      const response = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1188&deploy=1&compid=TSTDRV2149044');
      
      expect(response.status).toBe(200);
      expect(response.data.formSelected).toBeDefined();
      expect(response.data.formFields).toBeDefined();
      expect(Array.isArray(response.data.formFields)).toBe(true);
    });

    it('should handle item order history requests', async () => {
      const response = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1120&deploy=1&compid=TSTDRV2149044&productId=123');
      
      expect(response.status).toBe(200);
      expect(response.data.rows).toBeDefined();
      expect(response.data.columns).toBeDefined();
      expect(Array.isArray(response.data.rows)).toBe(true);
    });

    it('should handle project task metrics requests', async () => {
      const response = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=916&deploy=1&compid=TSTDRV2149044');
      
      expect(response.status).toBe(200);
      expect(response.data.metrics).toBeDefined();
      expect(Array.isArray(response.data.metrics)).toBe(true);
    });

    it('should handle web order submissions', async () => {
      const orderData = {
        items: [{ id: '1', quantity: 2, price: 100 }],
        total: 200,
        customerInfo: { name: 'Test Customer', email: '<EMAIL>' },
      };

      const response = await axios.post('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1037&deploy=1&compid=TSTDRV2149044', orderData);
      
      expect(response.status).toBe(201);
      expect(response.data.orderId).toBeDefined();
      expect(response.data.orderNumber).toBeDefined();
      expect(response.data.status).toBe('Submitted');
    });

    it('should handle record operations', async () => {
      const recordData = {
        recordType: 'customer',
        operation: 'create',
        fields: { name: 'Test Customer', email: '<EMAIL>' },
      };

      const response = await axios.post('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=920&deploy=1&compid=TSTDRV2149044', recordData);
      
      expect(response.status).toBe(200);
      expect(response.data.recordId).toBeDefined();
      expect(response.data.status).toBe('success');
    });
  });

  describe('Error Handling', () => {
    it('should handle deployment URL fetch errors', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      render(<DeploymentUrlsComponent />);

      await waitFor(() => {
        expect(screen.getByTestId('error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error')).toHaveTextContent('Network error');
    });
  });
});
