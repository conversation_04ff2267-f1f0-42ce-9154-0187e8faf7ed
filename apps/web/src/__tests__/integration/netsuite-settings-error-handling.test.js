/**
 * Integration tests for NetSuite settings endpoint error handling
 * Tests proper handling of REACT_APP_SUITELET_SETTINGS endpoint failures and missing configuration
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import axios from 'axios';
import { SnackbarProvider } from 'notistack';
import useSWR from 'swr';

import { PureLightTheme } from '../../theme/schemes/PureLightTheme';
import { 
  mockValidSettings,
  mockSettingsWithMissingPrintingTemplates,
  mockSettingsWithMissingPortalForm,
  mockSettingsWithMissingInvoiceTemplate,
  mockSettingsWithMissingSalesOrderTemplate,
  mockMinimalValidSettings,
  mockSettingsNetworkError,
  mockSettingsServerError,
  mockSettingsSuccess,
  mockSettingsNetworkFailure,
  mockSettingsServerFailure,
  mockSettingsMissingPrintingTemplates,
  mockSettingsMissingSpecificField,
  mockSettingsMinimalValid,
  resetMocks 
} from '../setup/axios-mock';

const theme = PureLightTheme;

// Test component that simulates settings loading during app initialization
const SettingsTestComponent = ({ settingsUrl }) => {
  const { data: settings, error, mutate } = useSWR(
    settingsUrl, 
    (url) => axios.get(url).then(res => res.data)
  );
  
  // Simulate settings validation logic
  const validateSettings = (settingsData) => {
    if (!settingsData) return { isValid: false, errors: ['No settings data'] };
    
    const errors = [];
    const requiredFields = [
      'custrecord_ng_eh_default_portal_ordform',
      'custrecord_ng_eh_invoice_printing_temp',
      'custrecord_ng_eh_sales_order_printing_te'
    ];
    
    requiredFields.forEach(field => {
      if (!settingsData[field] || settingsData[field].length === 0) {
        errors.push(`Missing required field: ${field}`);
      }
    });
    
    return { isValid: errors.length === 0, errors };
  };
  
  if (error) {
    return (
      <div data-testid="settings-error-container">
        <div data-testid="settings-error-message">Settings Error: {error.message}</div>
        <div data-testid="settings-error-type">
          {error.code === 'NETWORK_ERROR' ? 'Network Error' : 'Server Error'}
        </div>
        <div data-testid="settings-error-details">
          {error.response?.data?.message || 'No error details'}
        </div>
        <button data-testid="settings-retry-button" onClick={() => mutate()}>
          Retry Settings
        </button>
      </div>
    );
  }
  
  if (!settings) {
    return <div data-testid="settings-loading">Loading settings...</div>;
  }
  
  const validation = validateSettings(settings);
  
  if (!validation.isValid) {
    return (
      <div data-testid="settings-validation-error">
        <div data-testid="validation-error-title">Configuration Error</div>
        <div data-testid="validation-errors">
          {validation.errors.map((error, index) => (
            <div key={index} data-testid={`validation-error-${index}`}>
              ❌ {error}
            </div>
          ))}
        </div>
        <div data-testid="admin-notice">
          Please contact your administrator to configure the missing settings.
        </div>
        <button data-testid="settings-retry-button" onClick={() => mutate()}>
          Retry Settings
        </button>
      </div>
    );
  }
  
  return (
    <div data-testid="settings-success">
      <div data-testid="settings-loaded">Settings loaded successfully</div>
      <div data-testid="portal-form-configured">
        Portal Form: {settings.custrecord_ng_eh_default_portal_ordform?.[0]?.text || 'Not configured'}
      </div>
      <div data-testid="invoice-template-configured">
        Invoice Template: {settings.custrecord_ng_eh_invoice_printing_temp?.[0]?.text || 'Not configured'}
      </div>
      <div data-testid="sales-order-template-configured">
        Sales Order Template: {settings.custrecord_ng_eh_sales_order_printing_te?.[0]?.text || 'Not configured'}
      </div>
    </div>
  );
};

// Test wrapper
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      <SWRConfig 
        value={{ 
          provider: () => new Map(),
          fetcher: (url) => axios.get(url).then(res => res.data),
          dedupingInterval: 0,
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
          errorRetryCount: 0,
        }}
      >
        {children}
      </SWRConfig>
    </SnackbarProvider>
  </ThemeProvider>
);

describe('NetSuite Settings Endpoint Error Handling', () => {
  const settingsUrl = 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=918&deploy=1';
  const restletUrl = 'https://tstdrv2149044.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=918&deploy=1';

  beforeEach(() => {
    resetMocks();
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful Settings Loading', () => {
    test('should load valid settings successfully', async () => {
      mockSettingsSuccess(mockValidSettings);

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      // Initially should show loading
      expect(screen.getByTestId('settings-loading')).toBeInTheDocument();

      // Wait for settings to load
      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });

      // Verify settings are properly configured
      expect(screen.getByTestId('portal-form-configured')).toHaveTextContent('Portal Form: Default Portal Order Form');
      expect(screen.getByTestId('invoice-template-configured')).toHaveTextContent('Invoice Template: Invoice Printing Template');
      expect(screen.getByTestId('sales-order-template-configured')).toHaveTextContent('Sales Order Template: Sales Order Printing Template');
    });

    test('should load minimal valid settings for app initialization', async () => {
      mockSettingsMinimalValid();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });

      expect(screen.getByTestId('settings-loaded')).toHaveTextContent('Settings loaded successfully');
    });
  });

  describe('Network and Server Errors', () => {
    test('should handle network errors when fetching settings', async () => {
      mockSettingsNetworkFailure();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-error-container')).toBeInTheDocument();
      });

      expect(screen.getByTestId('settings-error-message')).toHaveTextContent('Settings Error: Network Error');
      expect(screen.getByTestId('settings-error-type')).toHaveTextContent('Network Error');
      expect(screen.getByTestId('settings-retry-button')).toBeInTheDocument();
    });

    test('should handle server errors when fetching settings', async () => {
      mockSettingsServerFailure();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-error-container')).toBeInTheDocument();
      });

      expect(screen.getByTestId('settings-error-message')).toHaveTextContent('Settings Error: Internal Server Error');
      expect(screen.getByTestId('settings-error-type')).toHaveTextContent('Server Error');
      expect(screen.getByTestId('settings-error-details')).toHaveTextContent('Internal server error while retrieving settings');
    });

    test('should allow retry after network failure', async () => {
      // Start with network error
      mockSettingsNetworkFailure();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-error-container')).toBeInTheDocument();
      });

      // Mock successful response for retry
      mockSettingsSuccess(mockValidSettings);

      // Click retry button
      fireEvent.click(screen.getByTestId('settings-retry-button'));

      // Should show success after retry
      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });
    });
  });

  describe('Missing Configuration Fields', () => {
    test('should detect missing printing templates configuration', async () => {
      mockSettingsMissingPrintingTemplates();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('validation-error-title')).toHaveTextContent('Configuration Error');
      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_default_portal_ordform');
      expect(screen.getByTestId('validation-error-1')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_invoice_printing_temp');
      expect(screen.getByTestId('validation-error-2')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_sales_order_printing_te');
      expect(screen.getByTestId('admin-notice')).toHaveTextContent('Please contact your administrator to configure the missing settings.');
    });

    test('should detect missing portal order form configuration', async () => {
      mockSettingsMissingSpecificField('portal');

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_default_portal_ordform');
      expect(screen.queryByTestId('validation-error-1')).not.toBeInTheDocument(); // Only one error
    });

    test('should detect missing invoice printing template configuration', async () => {
      mockSettingsMissingSpecificField('invoice');

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_invoice_printing_temp');
    });

    test('should detect missing sales order printing template configuration', async () => {
      mockSettingsMissingSpecificField('salesorder');

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_sales_order_printing_te');
    });
  });

  describe('Settings Recovery and Retry', () => {
    test('should allow retry after configuration error', async () => {
      // Start with missing configuration
      mockSettingsMissingPrintingTemplates();

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      // Mock fixed configuration for retry
      mockSettingsSuccess(mockValidSettings);

      // Click retry button
      fireEvent.click(screen.getByTestId('settings-retry-button'));

      // Should show success after configuration is fixed
      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });
    });

    test('should handle partial configuration fixes', async () => {
      // Test with only portal form missing (simpler scenario)
      const partiallyFixedSettings = {
        ...mockValidSettings,
        custrecord_ng_eh_default_portal_ordform: [], // Only this field missing
      };

      axios.get.mockImplementation((url) => {
        if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
          return Promise.resolve({
            data: partiallyFixedSettings,
            status: 200,
            statusText: 'OK',
            headers: {},
            config: {},
          });
        }
        return Promise.resolve({ data: {}, status: 200 });
      });

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      // Should only show one error for the missing portal form
      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_default_portal_ordform');
      expect(screen.queryByTestId('validation-error-1')).not.toBeInTheDocument();
      expect(screen.queryByTestId('validation-error-2')).not.toBeInTheDocument();
    });
  });

  describe('Environment-Specific URL Handling', () => {
    test('should handle development environment restlet URL', async () => {
      mockSettingsSuccess(mockValidSettings);

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={restletUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });

      // Verify the correct URL was called
      expect(axios.get).toHaveBeenCalledWith(restletUrl);
    });

    test('should handle production environment suitelet URL', async () => {
      mockSettingsSuccess(mockValidSettings);

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-success')).toBeInTheDocument();
      });

      // Verify the correct URL was called
      expect(axios.get).toHaveBeenCalledWith(settingsUrl);
    });
  });

  describe('Settings Data Structure Validation', () => {
    test('should handle empty settings response', async () => {
      mockSettingsSuccess({});

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      // Should show all required fields as missing
      expect(screen.getByTestId('validation-error-0')).toBeInTheDocument();
      expect(screen.getByTestId('validation-error-1')).toBeInTheDocument();
      expect(screen.getByTestId('validation-error-2')).toBeInTheDocument();
    });

    test('should handle malformed settings response', async () => {
      // Mock malformed response - string instead of object
      axios.get.mockResolvedValue({ data: 'invalid json string' });

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      // String data should be treated as invalid and show missing field errors
      expect(screen.getByTestId('validation-error-0')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_default_portal_ordform');
      expect(screen.getByTestId('validation-error-1')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_invoice_printing_temp');
      expect(screen.getByTestId('validation-error-2')).toHaveTextContent('❌ Missing required field: custrecord_ng_eh_sales_order_printing_te');
    });

    test('should validate settings structure with null values', async () => {
      const settingsWithNulls = {
        ...mockValidSettings,
        custrecord_ng_eh_default_portal_ordform: null,
        custrecord_ng_eh_invoice_printing_temp: null,
        custrecord_ng_eh_sales_order_printing_te: null,
      };

      mockSettingsSuccess(settingsWithNulls);

      render(
        <TestWrapper>
          <SettingsTestComponent settingsUrl={settingsUrl} />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('settings-validation-error')).toBeInTheDocument();
      });

      // Should detect null values as missing configuration
      expect(screen.getByTestId('validation-error-0')).toBeInTheDocument();
      expect(screen.getByTestId('validation-error-1')).toBeInTheDocument();
      expect(screen.getByTestId('validation-error-2')).toBeInTheDocument();
    });
  });
});
