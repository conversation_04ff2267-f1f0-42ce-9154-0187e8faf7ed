/**
 * Integration tests for UserContext error handling
 * Tests proper handling of NetSuite backend admin misconfiguration errors
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { SWRConfig } from 'swr';
import axios from 'axios';
import { SnackbarProvider } from 'notistack';

// Mock all the dependencies before importing
jest.mock('../../contexts/SettingsContext', () => ({
  SettingsProvider: ({ children }) => children,
  SettingsContext: {
    Provider: ({ children }) => children
  }
}));

jest.mock('../../hooks/useSettings', () => ({
  __esModule: true,
  default: () => ({
    custrecord_ng_eh_react_logo_image_url: '/test-logo.png'
  })
}));

jest.mock('../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      core_account_url: 'https://tstdrv2149044.app.netsuite.com'
    }
  })
}));

// Now import the components
import UserProvider, { UserContext } from '../../contexts/UserContext';
import theme from '../../theme';
import { 
  mockNetSuiteConfigurationError, 
  mockUserSuiteletConfigError,
  resetMocks 
} from '../setup/axios-mock';

// Mock environment variables
const mockEnv = {
  NODE_ENV: 'test',
  REACT_APP_GET_USER_SUITELET: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQ_6O6Qi6v82zb_t3wEqAUyUJzdxLZu2OWJScnehTXlhM',
  REACT_APP_SUITELET_SETTINGS: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=918&deploy=1',
  REACT_APP_RESTLET_SETTINGS: 'https://tstdrv2149044.restlets.api.netsuite.com/app/site/hosting/restlet.nl?script=918&deploy=1'
};

// Mock window.env for production environment simulation
Object.defineProperty(window, 'env', {
  writable: true,
  value: mockEnv,
});

// Mock process.env for development environment
process.env.REACT_APP_GET_USER_SUITELET = mockEnv.REACT_APP_GET_USER_SUITELET;
process.env.REACT_APP_RESTLET_SETTINGS = mockEnv.REACT_APP_RESTLET_SETTINGS;

// Mock nsRequest function used by SettingsContext
jest.mock('../../utils/customFunctions', () => ({
  nsRequest: jest.fn(() => Promise.resolve({
    custrecord_ng_eh_react_logo_image_url: '/test-logo.png'
  }))
}));

// Test component that consumes UserContext
const TestUserComponent = () => {
  const { user, error } = React.useContext(UserContext);
  
  if (error) {
    return (
      <div>
        <div data-testid="error-message">Error: {error.message}</div>
        <div data-testid="error-details">
          {error.response?.data?.details?.message || 'No error details'}
        </div>
      </div>
    );
  }
  
  if (!user) {
    return <div data-testid="loading">Loading...</div>;
  }
  
  return (
    <div>
      <div data-testid="user-name">{user.name}</div>
      <div data-testid="user-email">{user.email}</div>
    </div>
  );
};

// Test wrapper component
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      <SWRConfig 
        value={{ 
          provider: () => new Map(),
          fetcher: (url) => axios.get(url).then(res => res.data),
          dedupingInterval: 0,
          revalidateOnFocus: false,
          revalidateOnReconnect: false,
        }}
      >
        <UserProvider>
          {children}
        </UserProvider>
      </SWRConfig>
    </SnackbarProvider>
  </ThemeProvider>
);

describe('UserContext Error Handling', () => {
  beforeEach(() => {
    resetMocks();
    // Clear console errors for clean test output
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('NetSuite Admin Misconfiguration Errors', () => {
    test('should handle missing printout template configuration error', async () => {
      // Mock the user suitelet to return configuration error
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      // Initially should show loading
      expect(screen.getByTestId('loading')).toBeInTheDocument();

      // Wait for error to be displayed
      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      }, { timeout: 3000 });

      // Verify error message is displayed
      expect(screen.getByTestId('error-message')).toHaveTextContent('Error: Error getting user information');
      
      // Verify detailed error message about configuration
      expect(screen.getByTestId('error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
    });

    test('should handle error response structure correctly', async () => {
      // Mock specific error response
      const configError = mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      // Verify the error structure matches expected format
      expect(configError.response.data).toEqual(mockNetSuiteConfigurationError);
      expect(configError.response.data.id).toBe('ERR-1750963949169-856');
      expect(configError.response.data.message).toBe('Error getting user information');
      expect(configError.response.data.details.customMessage).toBe('Error getting user information');
    });

    test('should log configuration errors for debugging', async () => {
      const consoleSpy = jest.spyOn(console, 'error');
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      // Verify error was logged to console
      expect(consoleSpy).toHaveBeenCalledWith(
        'User data error:', 
        expect.objectContaining({
          message: 'Error getting user information'
        })
      );
    });
  });

  describe('Missing Configuration Fields', () => {
    test('should handle missing custrecord_ng_eh_default_portal_ordform', async () => {
      // Create error for missing default portal order form
      const error = new Error('Error getting user information');
      error.response = {
        data: {
          id: "ERR-1750963949169-857",
          message: "Error getting user information",
          details: {
            id: "ERR-1750963949169-857",
            message: "❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.",
            stack: "Error: ❌ Default portal order form is required in settings...",
            name: "Error",
            customMessage: "Error getting user information"
          }
        },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {},
      };

      axios.get.mockRejectedValue(error);

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.'
        );
      });
    });

    test('should handle missing custrecord_ng_eh_invoice_printing_temp', async () => {
      // Create error for missing invoice printing template
      const error = new Error('Error getting user information');
      error.response = {
        data: {
          id: "ERR-1750963949169-858",
          message: "Error getting user information",
          details: {
            id: "ERR-1750963949169-858",
            message: "❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.",
            stack: "Error: ❌ Invoice printing template is required in settings...",
            name: "Error",
            customMessage: "Error getting user information"
          }
        },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {},
      };

      axios.get.mockRejectedValue(error);

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.'
        );
      });
    });

    test('should handle missing custrecord_ng_eh_sales_order_printing_te', async () => {
      // Create error for missing sales order printing template
      const error = new Error('Error getting user information');
      error.response = {
        data: {
          id: "ERR-1750963949169-859",
          message: "Error getting user information",
          details: {
            id: "ERR-1750963949169-859",
            message: "❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.",
            stack: "Error: ❌ Sales order printing template is required in settings...",
            name: "Error",
            customMessage: "Error getting user information"
          }
        },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {},
      };

      axios.get.mockRejectedValue(error);

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toHaveTextContent(
          '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.'
        );
      });
    });
  });

  describe('Error Recovery and User Experience', () => {
    test('should provide clear messaging about admin configuration issues', async () => {
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-details')).toBeInTheDocument();
      });

      const errorDetails = screen.getByTestId('error-details');
      
      // Verify the error message is user-friendly and actionable
      expect(errorDetails).toHaveTextContent('❌ Printout templates are required in settings');
      expect(errorDetails).toHaveTextContent('Please configure Sales Order and Invoice printing templates');
    });

    test('should handle network errors gracefully', async () => {
      // Mock network error
      const networkError = new Error('Network Error');
      networkError.code = 'NETWORK_ERROR';
      axios.get.mockRejectedValue(networkError);

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error-message')).toHaveTextContent('Error: Network Error');
    });

    test('should handle timeout errors appropriately', async () => {
      // Mock timeout error
      const timeoutError = new Error('timeout of 5000ms exceeded');
      timeoutError.code = 'ECONNABORTED';
      axios.get.mockRejectedValue(timeoutError);

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      expect(screen.getByTestId('error-message')).toHaveTextContent('Error: timeout of 5000ms exceeded');
    });
  });

  describe('Development vs Production Environment', () => {
    test('should handle development environment configuration', async () => {
      // Set development environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      // Restore original environment
      process.env.NODE_ENV = originalEnv;

      expect(screen.getByTestId('error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings'
      );
    });

    test('should handle production environment configuration', async () => {
      // Set production environment
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-message')).toBeInTheDocument();
      });

      // Restore original environment
      process.env.NODE_ENV = originalEnv;

      expect(screen.getByTestId('error-details')).toHaveTextContent(
        '❌ Printout templates are required in settings'
      );
    });
  });
});
