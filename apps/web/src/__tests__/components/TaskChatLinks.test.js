import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import TaskStatus from '../../content/management/Projects/single/TaskStatus';
import Results from '../../content/dashboards/Exhibit Tasks/Tasks/Results';
import { render } from '../setup/test-utils';
import Cookies from 'js-cookie';

// Mock js-cookie
jest.mock('js-cookie');

// Mock useNavigate hook
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate
}));

// Mock useHoistedUrls hook for TaskStatus component
jest.mock('../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      get_cs_chat_url: 'https://example.com/chat'
    }
  })
}));

// Mock useDeploymentUrls hook for Results component
jest.mock('../../store/deploymentUrlStore', () => ({
  useDeploymentUrls: () => ({
    getDeploymentUrl: {
      get_cs_chat_url: 'https://example.com/chat'
    }
  })
}));

// Mock snackbar for Results component
jest.mock('notistack', () => ({
  useSnackbar: () => ({
    enqueueSnackbar: jest.fn()
  })
}));

// Mock useAuth hook for Results component
jest.mock('../../hooks/useAuth', () => ({
  __esModule: true,
  default: () => ({
    currentProject: null,
    setProject: jest.fn()
  })
}));

// Mock i18n
jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

describe('TaskStatus Component - Chat Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NODE_ENV = 'production'; // Default to production
  });

  test('sets cookies when chat button is clicked with enabled chat', () => {
    const project = {
      tasks: [
        {
          internalid: '123',
          name: 'Test Task',
          status: 'In Progress',
          startdate: '2023-01-01',
          enddate: '2023-12-31',
          progress: '50',
          company: 'Company Name: Details',
          externalChat: true // Chat is enabled
        }
      ]
    };

    render(<TaskStatus project={project} />);

    // Find and click the chat button
    const chatButton = screen
      .getByTestId('ChatBubbleOutlineTwoToneIcon')
      .closest('button');
    fireEvent.click(chatButton);

    // Verify cookies were set
    expect(Cookies.set).toHaveBeenCalledWith('ept-taskChatId', '123', {
      expires: 7
    });
    expect(Cookies.set).toHaveBeenCalledWith('ept-internalChat', 'F', {
      expires: 7
    });

    // Verify navigation
    expect(mockNavigate).toHaveBeenCalledWith('/view-chat', {
      state: {
        chatUrl: 'https://example.com/chat?taskId=123&mode=view',
        taskName: 'Test Task'
      }
    });
  });

  test('does not set cookies or navigate when chat button is disabled', () => {
    const project = {
      tasks: [
        {
          internalid: '123',
          name: 'Test Task',
          status: 'In Progress',
          startdate: '2023-01-01',
          enddate: '2023-12-31',
          progress: '50',
          company: 'Company Name: Details',
          externalChat: false // Chat is disabled
        }
      ]
    };

    render(<TaskStatus project={project} />);

    // Find and click the chat button
    const chatButton = screen
      .getByTestId('ChatBubbleOutlineTwoToneIcon')
      .closest('button');
    fireEvent.click(chatButton);

    // Verify cookies were NOT set
    expect(Cookies.set).not.toHaveBeenCalled();

    // Verify navigation was NOT called
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});

describe('Results Component - Chat Functionality', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    process.env.NODE_ENV = 'production'; // Default to production
  });

  test('sets cookies when chat button is clicked with enabled chat', () => {
    const projects = [
      {
        internalid: '456',
        name: 'Test Project Task',
        status: 'In Progress',
        startdate: '2023-01-01',
        enddate: '2023-12-31',
        progress: '75',
        company: 'Company Name: Details',
        externalChat: true // Chat is enabled
      }
    ];

    render(<Results projects={projects} />);

    // Find and click the chat button in the table
    const chatButton = screen
      .getByTestId('ChatBubbleOutlineTwoToneIcon')
      .closest('button');
    fireEvent.click(chatButton);

    // Verify cookies were set
    expect(Cookies.set).toHaveBeenCalledWith('ept-taskChatId', '456', {
      expires: 7
    });
    expect(Cookies.set).toHaveBeenCalledWith('ept-internalChat', 'F', {
      expires: 7
    });

    // Verify navigation
    expect(mockNavigate).toHaveBeenCalledWith('/view-chat', {
      state: {
        chatUrl: 'https://example.com/chat?taskId=456&mode=view',
        taskName: 'Test Project Task'
      }
    });
  });

  test('does not set cookies or navigate when chat button is disabled', () => {
    const projects = [
      {
        internalid: '456',
        name: 'Test Project Task',
        status: 'In Progress',
        startdate: '2023-01-01',
        enddate: '2023-12-31',
        progress: '75',
        company: 'Company Name: Details',
        externalChat: false // Chat is disabled
      }
    ];

    render(<Results projects={projects} />);

    // Find and click the chat button
    const chatButton = screen
      .getByTestId('ChatBubbleOutlineTwoToneIcon')
      .closest('button');
    fireEvent.click(chatButton);

    // Verify cookies were NOT set
    expect(Cookies.set).not.toHaveBeenCalled();

    // Verify navigation was NOT called
    expect(mockNavigate).not.toHaveBeenCalled();
  });
});
