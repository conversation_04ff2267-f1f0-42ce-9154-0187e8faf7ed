import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import { render } from '../setup/test-utils';
import ProjectFormReadOnlyView from '../../content/management/Projects/single/ProjectFormReadOnlyView';
import axios from 'axios';

// Mock dependencies
jest.mock('axios');
jest.mock('swr', () => ({
  __esModule: true,
  default: (key, fetcher) => {
    if (key) {
      return {
        data: mockFormData,
        error: null,
        isLoading: false
      };
    }
    return { data: null, error: null, isLoading: true };
  }
}));

jest.mock('../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      get_project_form_data_external: 'http://test.com/external',
      get_project_form_data_internal: 'http://test.com/internal'
    }
  })
}));

jest.mock('react-i18next', () => ({
  useTranslation: () => ({
    t: (key) => key
  })
}));

// Mock form data with descriptions
const mockFormData = {
  formSelected: {
    name: 'Test Form'
  },
  formLayout: 'stack',
  formGroups: [
    {
      title: 'Event Information',
      columns: '2',
      description:
        'This section contains information about the event details and requirements.'
    },
    {
      title: 'Contact Details',
      columns: '1',
      description: 'Contact information for the project stakeholders.'
    },
    {
      title: 'No Description Group',
      columns: '1'
      // No description property
    }
  ],
  formFields: [
    {
      name: 'event_name',
      label: 'Event Name',
      type: 'text',
      value: 'Test Event',
      group: 'Event Information'
    },
    {
      name: 'event_date',
      label: 'Event Date',
      type: 'date',
      value: '2023-12-01',
      group: 'Event Information'
    },
    {
      name: 'contact_name',
      label: 'Contact Name',
      type: 'text',
      value: 'John Doe',
      group: 'Contact Details'
    },
    {
      name: 'no_desc_field',
      label: 'Field in No Description Group',
      type: 'text',
      value: 'Test Value',
      group: 'No Description Group'
    },
    {
      name: 'ungrouped_field',
      label: 'Ungrouped Field',
      type: 'text',
      value: 'Ungrouped Value'
      // No group property
    }
  ]
};

// Using custom render from test-utils which includes theme

describe('ProjectFormReadOnlyView', () => {
  beforeEach(() => {
    axios.get.mockResolvedValue({ data: mockFormData });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  test('renders form with field group descriptions', async () => {
    render(<ProjectFormReadOnlyView projectId="123" formId="456" />);

    await waitFor(() => {
      expect(screen.getByText('Test Form')).toBeInTheDocument();
    });

    // Check that descriptions are displayed
    expect(
      screen.getByText(
        'This section contains information about the event details and requirements.'
      )
    ).toBeInTheDocument();
    expect(
      screen.getByText('Contact information for the project stakeholders.')
    ).toBeInTheDocument();
  });

  test('does not render description when not provided', async () => {
    render(<ProjectFormReadOnlyView projectId="123" formId="456" />);

    await waitFor(() => {
      expect(screen.getByText('Test Form')).toBeInTheDocument();
    });

    // Check that group without description doesn't show any description text
    expect(screen.getByText('No Description Group')).toBeInTheDocument();
    // The description should not be present since it's not provided
    expect(
      screen.queryByText('No description available')
    ).not.toBeInTheDocument();
  });

  test('renders fields correctly with their groups', async () => {
    render(<ProjectFormReadOnlyView projectId="123" formId="456" />);

    await waitFor(() => {
      expect(screen.getByText('Test Form')).toBeInTheDocument();
    });

    // Check that fields are rendered
    expect(screen.getByText('Event Name')).toBeInTheDocument();
    expect(screen.getByText('Test Event')).toBeInTheDocument();
    expect(screen.getByText('Contact Name')).toBeInTheDocument();
    expect(screen.getByText('John Doe')).toBeInTheDocument();
    expect(screen.getByText('Ungrouped Field')).toBeInTheDocument();
    expect(screen.getByText('Ungrouped Value')).toBeInTheDocument();
  });

  test('handles tab layout with descriptions', async () => {
    const tabFormData = {
      ...mockFormData,
      formLayout: 'tab'
    };

    axios.get.mockResolvedValue({ data: tabFormData });

    render(<ProjectFormReadOnlyView projectId="123" formId="456" />);

    await waitFor(() => {
      expect(screen.getByText('Test Form')).toBeInTheDocument();
    });

    // Check that tabs are rendered
    expect(screen.getByRole('tablist')).toBeInTheDocument();

    // Check that the first tab's description is visible (since first tab should be selected)
    expect(
      screen.getByText(
        'This section contains information about the event details and requirements.'
      )
    ).toBeInTheDocument();
  });

  test('handles loading state', () => {
    render(<ProjectFormReadOnlyView projectId={null} formId={null} />);

    // Should show loading skeletons
    expect(screen.getAllByTestId('skeleton')).toBeTruthy();
  });

  test('handles error state', async () => {
    axios.get.mockRejectedValue(new Error('API Error'));

    render(<ProjectFormReadOnlyView projectId="123" formId="456" />);

    await waitFor(() => {
      expect(
        screen.getByText('Failed to load form data. Please try again.')
      ).toBeInTheDocument();
    });
  });
});
