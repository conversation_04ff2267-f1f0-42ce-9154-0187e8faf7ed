/**
 * Unit tests for custom utility functions
 * Tests utility functions used throughout the application
 */

// Mock the utility functions since we don't have access to the actual implementation
// In a real scenario, you would import the actual functions

// Mock implementation of common utility functions
const mockUtils = {
  formatDate: (date, format = 'MM/dd/yyyy') => {
    if (!date) return '';
    const d = new Date(date);
    if (isNaN(d.getTime())) return '';

    // Use UTC methods to avoid timezone issues in tests
    const month = String(d.getUTCMonth() + 1).padStart(2, '0');
    const day = String(d.getUTCDate()).padStart(2, '0');
    const year = d.getUTCFullYear();

    switch (format) {
      case 'MM/dd/yyyy':
        return `${month}/${day}/${year}`;
      case 'yyyy-MM-dd':
        return `${year}-${month}-${day}`;
      case 'dd/MM/yyyy':
        return `${day}/${month}/${year}`;
      default:
        return `${month}/${day}/${year}`;
    }
  },

  formatCurrency: (amount, currency = 'USD') => {
    if (typeof amount !== 'number') return '$0.00';
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount);
  },

  truncateText: (text, maxLength = 100) => {
    if (!text || typeof text !== 'string') return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  },

  generateId: () => {
    return Math.random().toString(36).substr(2, 9);
  },

  validateEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  debounce: (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  },

  deepClone: (obj) => {
    if (obj === null || typeof obj !== 'object') return obj;
    if (obj instanceof Date) return new Date(obj.getTime());
    if (obj instanceof Array) return obj.map(item => mockUtils.deepClone(item));
    if (typeof obj === 'object') {
      const clonedObj = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          clonedObj[key] = mockUtils.deepClone(obj[key]);
        }
      }
      return clonedObj;
    }
  },

  arrayToObject: (array, keyField) => {
    return array.reduce((obj, item) => {
      obj[item[keyField]] = item;
      return obj;
    }, {});
  },

  groupBy: (array, key) => {
    return array.reduce((groups, item) => {
      const group = item[key];
      if (!groups[group]) {
        groups[group] = [];
      }
      groups[group].push(item);
      return groups;
    }, {});
  },

  sortBy: (array, key, direction = 'asc') => {
    return [...array].sort((a, b) => {
      const aVal = a[key];
      const bVal = b[key];
      
      if (direction === 'desc') {
        return bVal > aVal ? 1 : bVal < aVal ? -1 : 0;
      }
      return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
    });
  },
};

describe('Custom Utility Functions', () => {
  describe('formatDate', () => {
    it('formats date in MM/dd/yyyy format by default', () => {
      // Use UTC date to avoid timezone issues in tests
      const date = new Date('2023-12-01T00:00:00.000Z');
      expect(mockUtils.formatDate(date)).toBe('12/01/2023');
    });

    it('formats date in yyyy-MM-dd format', () => {
      // Use UTC date to avoid timezone issues in tests
      const date = new Date('2023-12-01T00:00:00.000Z');
      expect(mockUtils.formatDate(date, 'yyyy-MM-dd')).toBe('2023-12-01');
    });

    it('formats date in dd/MM/yyyy format', () => {
      // Use UTC date to avoid timezone issues in tests
      const date = new Date('2023-12-01T00:00:00.000Z');
      expect(mockUtils.formatDate(date, 'dd/MM/yyyy')).toBe('01/12/2023');
    });

    it('handles invalid dates', () => {
      expect(mockUtils.formatDate('invalid')).toBe('');
      expect(mockUtils.formatDate(null)).toBe('');
      expect(mockUtils.formatDate(undefined)).toBe('');
    });
  });

  describe('formatCurrency', () => {
    it('formats currency in USD by default', () => {
      expect(mockUtils.formatCurrency(1234.56)).toBe('$1,234.56');
    });

    it('formats currency with different currency codes', () => {
      expect(mockUtils.formatCurrency(1234.56, 'EUR')).toBe('€1,234.56');
    });

    it('handles invalid amounts', () => {
      expect(mockUtils.formatCurrency('invalid')).toBe('$0.00');
      expect(mockUtils.formatCurrency(null)).toBe('$0.00');
      expect(mockUtils.formatCurrency(undefined)).toBe('$0.00');
    });

    it('handles zero amount', () => {
      expect(mockUtils.formatCurrency(0)).toBe('$0.00');
    });
  });

  describe('truncateText', () => {
    it('truncates text longer than max length', () => {
      const longText = 'This is a very long text that should be truncated';
      expect(mockUtils.truncateText(longText, 20)).toBe('This is a very long ...');
    });

    it('returns original text if shorter than max length', () => {
      const shortText = 'Short text';
      expect(mockUtils.truncateText(shortText, 20)).toBe('Short text');
    });

    it('handles invalid input', () => {
      expect(mockUtils.truncateText(null)).toBe('');
      expect(mockUtils.truncateText(undefined)).toBe('');
      expect(mockUtils.truncateText(123)).toBe('');
    });

    it('uses default max length of 100', () => {
      const text = 'a'.repeat(150);
      const result = mockUtils.truncateText(text);
      expect(result).toHaveLength(103); // 100 + '...'
    });
  });

  describe('generateId', () => {
    it('generates a string ID', () => {
      const id = mockUtils.generateId();
      expect(typeof id).toBe('string');
      expect(id.length).toBeGreaterThan(0);
    });

    it('generates unique IDs', () => {
      const id1 = mockUtils.generateId();
      const id2 = mockUtils.generateId();
      expect(id1).not.toBe(id2);
    });
  });

  describe('validateEmail', () => {
    it('validates correct email addresses', () => {
      expect(mockUtils.validateEmail('<EMAIL>')).toBe(true);
      expect(mockUtils.validateEmail('<EMAIL>')).toBe(true);
      expect(mockUtils.validateEmail('<EMAIL>')).toBe(true);
    });

    it('rejects invalid email addresses', () => {
      expect(mockUtils.validateEmail('invalid-email')).toBe(false);
      expect(mockUtils.validateEmail('test@')).toBe(false);
      expect(mockUtils.validateEmail('@example.com')).toBe(false);
      expect(mockUtils.validateEmail('test.example.com')).toBe(false);
      expect(mockUtils.validateEmail('')).toBe(false);
    });
  });

  describe('debounce', () => {
    beforeEach(() => {
      jest.useFakeTimers();
    });

    afterEach(() => {
      jest.useRealTimers();
    });

    it('delays function execution', () => {
      const mockFn = jest.fn();
      const debouncedFn = mockUtils.debounce(mockFn, 100);

      debouncedFn();
      expect(mockFn).not.toHaveBeenCalled();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });

    it('cancels previous calls', () => {
      const mockFn = jest.fn();
      const debouncedFn = mockUtils.debounce(mockFn, 100);

      debouncedFn();
      debouncedFn();
      debouncedFn();

      jest.advanceTimersByTime(100);
      expect(mockFn).toHaveBeenCalledTimes(1);
    });
  });

  describe('deepClone', () => {
    it('clones primitive values', () => {
      expect(mockUtils.deepClone(42)).toBe(42);
      expect(mockUtils.deepClone('string')).toBe('string');
      expect(mockUtils.deepClone(true)).toBe(true);
      expect(mockUtils.deepClone(null)).toBe(null);
    });

    it('clones arrays', () => {
      const original = [1, 2, { a: 3 }];
      const cloned = mockUtils.deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned[2]).not.toBe(original[2]);
    });

    it('clones objects', () => {
      const original = { a: 1, b: { c: 2 } };
      const cloned = mockUtils.deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
      expect(cloned.b).not.toBe(original.b);
    });

    it('clones dates', () => {
      const original = new Date('2023-12-01');
      const cloned = mockUtils.deepClone(original);
      
      expect(cloned).toEqual(original);
      expect(cloned).not.toBe(original);
    });
  });

  describe('arrayToObject', () => {
    it('converts array to object using key field', () => {
      const array = [
        { id: 1, name: 'John' },
        { id: 2, name: 'Jane' },
      ];
      
      const result = mockUtils.arrayToObject(array, 'id');
      
      expect(result).toEqual({
        1: { id: 1, name: 'John' },
        2: { id: 2, name: 'Jane' },
      });
    });
  });

  describe('groupBy', () => {
    it('groups array items by key', () => {
      const array = [
        { category: 'A', value: 1 },
        { category: 'B', value: 2 },
        { category: 'A', value: 3 },
      ];
      
      const result = mockUtils.groupBy(array, 'category');
      
      expect(result).toEqual({
        A: [
          { category: 'A', value: 1 },
          { category: 'A', value: 3 },
        ],
        B: [
          { category: 'B', value: 2 },
        ],
      });
    });
  });

  describe('sortBy', () => {
    it('sorts array by key in ascending order', () => {
      const array = [
        { name: 'Charlie', age: 30 },
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 35 },
      ];
      
      const result = mockUtils.sortBy(array, 'name');
      
      expect(result[0].name).toBe('Alice');
      expect(result[1].name).toBe('Bob');
      expect(result[2].name).toBe('Charlie');
    });

    it('sorts array by key in descending order', () => {
      const array = [
        { name: 'Charlie', age: 30 },
        { name: 'Alice', age: 25 },
        { name: 'Bob', age: 35 },
      ];
      
      const result = mockUtils.sortBy(array, 'age', 'desc');
      
      expect(result[0].age).toBe(35);
      expect(result[1].age).toBe(30);
      expect(result[2].age).toBe(25);
    });
  });
});
