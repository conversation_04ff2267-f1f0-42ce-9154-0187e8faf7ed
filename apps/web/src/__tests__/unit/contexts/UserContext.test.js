/**
 * Unit tests for UserContext
 * Tests SWR integration and error handling for NetSuite user suitelet
 */

import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import { SWRConfig } from 'swr';
import axios from 'axios';

import UserProvider, { UserContext } from '../../../contexts/UserContext';
import { SettingsProvider } from '../../../contexts/SettingsContext';
import { 
  mockNetSuiteConfigurationError, 
  mockUserSuiteletConfigError,
  resetMocks 
} from '../../setup/axios-mock';

// Mock environment variables
const mockEnv = {
  NODE_ENV: 'test',
  REACT_APP_GET_USER_SUITELET: 'https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=902&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQ_6O6Qi6v82zb_t3wEqAUyUJzdxLZu2OWJScnehTXlhM'
};

// Mock window.env
Object.defineProperty(window, 'env', {
  writable: true,
  value: mockEnv,
});

// Mock useSettings hook
jest.mock('../../../hooks/useSettings', () => ({
  __esModule: true,
  default: () => ({
    custrecord_ng_eh_react_logo_image_url: '/test-logo.png'
  })
}));

// Mock useHoistedUrls hook
jest.mock('../../../hooks/useHoistedUrls', () => ({
  __esModule: true,
  default: () => ({
    getDeploymentUrl: {
      core_account_url: 'https://tstdrv2149044.app.netsuite.com'
    }
  })
}));

// Test component that uses UserContext
const TestUserContextConsumer = () => {
  const userContext = React.useContext(UserContext);
  
  return (
    <div>
      <div data-testid="user-loading">{userContext.nsDataValidating ? 'Loading' : 'Not Loading'}</div>
      <div data-testid="user-data">{userContext.user ? JSON.stringify(userContext.user) : 'No User'}</div>
      <div data-testid="user-error">{userContext.error ? userContext.error.message : 'No Error'}</div>
      <div data-testid="table-loading">{userContext.tableLoading ? 'Table Loading' : 'Table Not Loading'}</div>
    </div>
  );
};

// Test wrapper
const TestWrapper = ({ children, swrConfig = {} }) => (
  <SWRConfig 
    value={{ 
      provider: () => new Map(),
      fetcher: (url) => axios.get(url).then(res => res.data),
      dedupingInterval: 0,
      revalidateOnFocus: false,
      revalidateOnReconnect: false,
      ...swrConfig
    }}
  >
    <SettingsProvider>
      <UserProvider>
        {children}
      </UserProvider>
    </SettingsProvider>
  </SWRConfig>
);

describe('UserContext', () => {
  beforeEach(() => {
    resetMocks();
    // Mock localStorage
    Storage.prototype.getItem = jest.fn(() => null);
    Storage.prototype.setItem = jest.fn();
    Storage.prototype.removeItem = jest.fn();
    Storage.prototype.clear = jest.fn();
    
    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: {
        getItem: jest.fn(() => null),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn(),
      },
      writable: true
    });

    // Clear console errors
    jest.spyOn(console, 'error').mockImplementation(() => {});
    jest.spyOn(console, 'log').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Successful User Data Loading', () => {
    test('should load user data successfully', async () => {
      // Mock successful response
      const mockUserData = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: {
            record: {
              fields: {
                custentity_ng_eh_customer_logo_url: '/test-logo.png'
              }
            }
          }
        }
      };

      axios.get.mockResolvedValue({ data: mockUserData });

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Initially should be loading
      expect(screen.getByTestId('user-loading')).toHaveTextContent('Loading');

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByTestId('user-loading')).toHaveTextContent('Not Loading');
      });

      // Verify user data is set
      const userData = screen.getByTestId('user-data');
      expect(userData).not.toHaveTextContent('No User');
      expect(screen.getByTestId('user-error')).toHaveTextContent('No Error');
    });
  });

  describe('Configuration Error Handling', () => {
    test('should handle NetSuite configuration errors properly', async () => {
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Wait for error to be processed
      await waitFor(() => {
        expect(screen.getByTestId('user-error')).not.toHaveTextContent('No Error');
      }, { timeout: 3000 });

      // Verify error is captured
      expect(screen.getByTestId('user-error')).toHaveTextContent('Error getting user information');
      expect(screen.getByTestId('user-data')).toHaveTextContent('No User');
    });

    test('should log configuration errors to console', async () => {
      const consoleSpy = jest.spyOn(console, 'error');
      mockUserSuiteletConfigError();

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          'User data error:', 
          expect.objectContaining({
            message: 'Error getting user information'
          })
        );
      });
    });

    test('should handle missing printout templates error', async () => {
      const configError = new Error('Error getting user information');
      configError.response = {
        data: mockNetSuiteConfigurationError,
        status: 500,
        statusText: 'Internal Server Error'
      };

      axios.get.mockRejectedValue(configError);

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-error')).toHaveTextContent('Error getting user information');
      });

      // Verify the error structure is preserved
      expect(configError.response.data.details.message).toContain('❌ Printout templates are required in settings');
    });
  });

  describe('Environment Configuration', () => {
    test('should use development environment URL', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Verify the correct URL is being used
      await waitFor(() => {
        expect(axios.get).toHaveBeenCalledWith(
          expect.stringContaining('script=902')
        );
      });

      process.env.NODE_ENV = originalEnv;
    });

    test('should use production environment URL from window.env', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Should use window.env in production
      await waitFor(() => {
        expect(axios.get).toHaveBeenCalled();
      });

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('SWR Integration', () => {
    test('should handle SWR revalidation', async () => {
      const mockUserData = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: { record: { fields: {} } }
        }
      };

      axios.get.mockResolvedValue({ data: mockUserData });

      const { rerender } = render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-loading')).toHaveTextContent('Not Loading');
      });

      // Trigger revalidation by rerendering
      rerender(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Should handle revalidation gracefully
      expect(screen.getByTestId('user-error')).toHaveTextContent('No Error');
    });

    test('should handle SWR error recovery', async () => {
      // First call fails
      mockUserSuiteletConfigError();

      const { rerender } = render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-error')).not.toHaveTextContent('No Error');
      });

      // Second call succeeds
      const mockUserData = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: { record: { fields: {} } }
        }
      };

      axios.get.mockResolvedValue({ data: mockUserData });

      rerender(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Should recover from error
      await waitFor(() => {
        expect(screen.getByTestId('user-error')).toHaveTextContent('No Error');
      });
    });
  });

  describe('Data Processing', () => {
    test('should handle string response for logout scenario', async () => {
      // Mock string response (logout scenario)
      axios.get.mockResolvedValue({ data: 'logout' });

      // Mock window.location.reload
      Object.defineProperty(window, 'location', {
        value: { reload: jest.fn() },
        writable: true
      });

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      // Should trigger page reload after timeout
      await waitFor(() => {
        expect(screen.getByTestId('user-data')).toHaveTextContent('No User');
      });

      // Verify reload is scheduled (we can't easily test the timeout)
      expect(window.location.reload).not.toHaveBeenCalled(); // Not called immediately
    });

    test('should process user data with logo URL', async () => {
      const mockUserData = {
        success: true,
        data: {
          id: '1',
          name: 'Test User',
          email: '<EMAIL>',
          profile: {
            record: {
              fields: {
                custentity_ng_eh_customer_logo_url: '/custom-logo.png'
              }
            }
          }
        }
      };

      axios.get.mockResolvedValue({ data: mockUserData });

      render(
        <TestWrapper>
          <TestUserContextConsumer />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('user-loading')).toHaveTextContent('Not Loading');
      });

      // Verify user data processing
      const userData = JSON.parse(screen.getByTestId('user-data').textContent);
      expect(userData.logoUrl).toContain('/custom-logo.png');
    });
  });

  describe('Error Boundary Integration', () => {
    test('should not crash the application on error', async () => {
      // Mock a severe error
      const severeError = new Error('Critical system error');
      severeError.response = { status: 500 };
      
      axios.get.mockRejectedValue(severeError);

      // Should not throw and crash the app
      expect(() => {
        render(
          <TestWrapper>
            <TestUserContextConsumer />
          </TestWrapper>
        );
      }).not.toThrow();

      await waitFor(() => {
        expect(screen.getByTestId('user-error')).toHaveTextContent('Critical system error');
      });
    });
  });
});
