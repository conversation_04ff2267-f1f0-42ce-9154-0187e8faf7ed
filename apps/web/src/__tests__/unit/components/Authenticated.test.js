/**
 * Unit tests for Authenticated component
 * Tests authentication guard functionality
 */

import React from 'react';
import { renderWithoutRouter, screen } from '../../setup/test-utils';
import { MemoryRouter } from 'react-router-dom';
import Authenticated from '../../../components/Authenticated';
import * as useAuthModule from '../../../hooks/useAuth';

// Mock the useAuth hook
jest.mock('../../../hooks/useAuth');
const mockUseAuth = useAuthModule.default;

// Mock the Login component
jest.mock('../../../content/pages/Auth/Login/Cover', () => {
  return function MockLogin() {
    return <div data-testid="login-page">Login Page</div>;
  };
});

describe('Authenticated Component', () => {
  const TestChild = () => (
    <div data-testid="protected-content">Protected Content</div>
  );

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders children when user is authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' }
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.queryByTestId('login-page')).not.toBeInTheDocument();
  });

  it('renders login page when user is not authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('login-page')).toBeInTheDocument();
    expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
  });

  it('stores requested location when not authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/protected-route']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('login-page')).toBeInTheDocument();
  });

  it('handles authentication state changes', () => {
    // Start unauthenticated
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null
    });

    const { rerender } = renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('login-page')).toBeInTheDocument();

    // Simulate authentication
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' }
    });

    rerender(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
    expect(screen.queryByTestId('login-page')).not.toBeInTheDocument();
  });

  it('renders multiple children when authenticated', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' }
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <div data-testid="child-1">Child 1</div>
          <div data-testid="child-2">Child 2</div>
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('child-1')).toBeInTheDocument();
    expect(screen.getByTestId('child-2')).toBeInTheDocument();
  });

  it('handles null children gracefully', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' }
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>{null}</Authenticated>
      </MemoryRouter>
    );

    // Should not crash and should not render login
    expect(screen.queryByTestId('login-page')).not.toBeInTheDocument();
  });

  it('handles undefined user when authenticated is true', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: true,
      user: undefined
    });

    renderWithoutRouter(
      <MemoryRouter initialEntries={['/dashboard']}>
        <Authenticated>
          <TestChild />
        </Authenticated>
      </MemoryRouter>
    );

    expect(screen.getByTestId('protected-content')).toBeInTheDocument();
  });

  it('handles different route paths', () => {
    mockUseAuth.mockReturnValue({
      isAuthenticated: false,
      user: null
    });

    const routes = ['/dashboard', '/projects', '/settings', '/profile'];

    routes.forEach((route) => {
      const { unmount } = renderWithoutRouter(
        <MemoryRouter initialEntries={[route]}>
          <Authenticated>
            <TestChild />
          </Authenticated>
        </MemoryRouter>
      );

      expect(screen.getByTestId('login-page')).toBeInTheDocument();
      unmount();
    });
  });
});
