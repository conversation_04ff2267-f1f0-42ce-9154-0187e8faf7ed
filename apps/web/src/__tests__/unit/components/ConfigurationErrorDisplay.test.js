/**
 * Unit tests for ConfigurationErrorDisplay component
 * Tests proper display of NetSuite admin configuration errors
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { <PERSON><PERSON>kbarProvider } from 'notistack';
import { PureLightTheme } from '../../../theme/schemes/PureLightTheme';

const theme = PureLightTheme;

// Mock the ConfigurationErrorDisplay component since it might not exist yet
const ConfigurationErrorDisplay = ({ error, onRetry, onDismiss }) => {
  if (!error) return null;

  const isConfigurationError = error.response?.data?.details?.message?.includes('❌');
  const errorMessage = error.response?.data?.details?.message || error.message || 'An error occurred';
  const errorId = error.response?.data?.id || 'Unknown';

  return (
    <div data-testid="configuration-error-display">
      <div data-testid="error-type">
        {isConfigurationError ? 'Configuration Error' : 'General Error'}
      </div>
      <div data-testid="error-id">Error ID: {errorId}</div>
      <div data-testid="error-message">{errorMessage}</div>
      
      {isConfigurationError && (
        <div data-testid="admin-notice">
          This error requires administrator attention to resolve configuration issues.
        </div>
      )}
      
      <div data-testid="error-actions">
        {onRetry && (
          <button data-testid="retry-button" onClick={onRetry}>
            Retry
          </button>
        )}
        {onDismiss && (
          <button data-testid="dismiss-button" onClick={onDismiss}>
            Dismiss
          </button>
        )}
      </div>
    </div>
  );
};

// Test wrapper
const TestWrapper = ({ children }) => (
  <ThemeProvider theme={theme}>
    <SnackbarProvider>
      {children}
    </SnackbarProvider>
  </ThemeProvider>
);

describe('ConfigurationErrorDisplay', () => {
  const mockRetry = jest.fn();
  const mockDismiss = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('NetSuite Configuration Errors', () => {
    test('should display printout template configuration error', () => {
      const configError = {
        message: 'Error getting user information',
        response: {
          data: {
            id: 'ERR-1750963949169-856',
            message: 'Error getting user information',
            details: {
              id: 'ERR-1750963949169-856',
              message: '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.',
              stack: 'Error: ❌ Printout templates are required in settings...',
              name: 'Error',
              customMessage: 'Error getting user information'
            }
          },
          status: 500,
          statusText: 'Internal Server Error'
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay 
            error={configError} 
            onRetry={mockRetry} 
            onDismiss={mockDismiss} 
          />
        </TestWrapper>
      );

      expect(screen.getByTestId('configuration-error-display')).toBeInTheDocument();
      expect(screen.getByTestId('error-type')).toHaveTextContent('Configuration Error');
      expect(screen.getByTestId('error-id')).toHaveTextContent('Error ID: ERR-1750963949169-856');
      expect(screen.getByTestId('error-message')).toHaveTextContent(
        '❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.'
      );
      expect(screen.getByTestId('admin-notice')).toHaveTextContent(
        'This error requires administrator attention to resolve configuration issues.'
      );
    });

    test('should display default portal order form configuration error', () => {
      const configError = {
        message: 'Error getting user information',
        response: {
          data: {
            id: 'ERR-1750963949169-857',
            message: 'Error getting user information',
            details: {
              message: '❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.',
              customMessage: 'Error getting user information'
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={configError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-message')).toHaveTextContent(
        '❌ Default portal order form is required in settings. Please configure custrecord_ng_eh_default_portal_ordform.'
      );
      expect(screen.getByTestId('admin-notice')).toBeInTheDocument();
    });

    test('should display invoice printing template configuration error', () => {
      const configError = {
        message: 'Error getting user information',
        response: {
          data: {
            id: 'ERR-1750963949169-858',
            details: {
              message: '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.',
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={configError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-message')).toHaveTextContent(
        '❌ Invoice printing template is required in settings. Please configure custrecord_ng_eh_invoice_printing_temp.'
      );
    });

    test('should display sales order printing template configuration error', () => {
      const configError = {
        message: 'Error getting user information',
        response: {
          data: {
            id: 'ERR-1750963949169-859',
            details: {
              message: '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.',
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={configError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-message')).toHaveTextContent(
        '❌ Sales order printing template is required in settings. Please configure custrecord_ng_eh_sales_order_printing_te.'
      );
    });
  });

  describe('General Error Handling', () => {
    test('should display general network errors', () => {
      const networkError = {
        message: 'Network Error',
        code: 'NETWORK_ERROR'
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={networkError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-type')).toHaveTextContent('General Error');
      expect(screen.getByTestId('error-message')).toHaveTextContent('Network Error');
      expect(screen.queryByTestId('admin-notice')).not.toBeInTheDocument();
    });

    test('should display timeout errors', () => {
      const timeoutError = {
        message: 'timeout of 5000ms exceeded',
        code: 'ECONNABORTED'
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={timeoutError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-message')).toHaveTextContent('timeout of 5000ms exceeded');
    });

    test('should handle errors without response data', () => {
      const simpleError = {
        message: 'Something went wrong'
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={simpleError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-message')).toHaveTextContent('Something went wrong');
      expect(screen.getByTestId('error-id')).toHaveTextContent('Error ID: Unknown');
    });
  });

  describe('User Interactions', () => {
    test('should call onRetry when retry button is clicked', () => {
      const error = { message: 'Test error' };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay 
            error={error} 
            onRetry={mockRetry} 
            onDismiss={mockDismiss} 
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByTestId('retry-button'));
      expect(mockRetry).toHaveBeenCalledTimes(1);
    });

    test('should call onDismiss when dismiss button is clicked', () => {
      const error = { message: 'Test error' };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay 
            error={error} 
            onRetry={mockRetry} 
            onDismiss={mockDismiss} 
          />
        </TestWrapper>
      );

      fireEvent.click(screen.getByTestId('dismiss-button'));
      expect(mockDismiss).toHaveBeenCalledTimes(1);
    });

    test('should not show action buttons when callbacks are not provided', () => {
      const error = { message: 'Test error' };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={error} />
        </TestWrapper>
      );

      expect(screen.queryByTestId('retry-button')).not.toBeInTheDocument();
      expect(screen.queryByTestId('dismiss-button')).not.toBeInTheDocument();
    });
  });

  describe('Error Classification', () => {
    test('should correctly identify configuration errors by emoji prefix', () => {
      const configError = {
        response: {
          data: {
            details: {
              message: '❌ This is a configuration error'
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={configError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-type')).toHaveTextContent('Configuration Error');
      expect(screen.getByTestId('admin-notice')).toBeInTheDocument();
    });

    test('should classify errors without emoji as general errors', () => {
      const generalError = {
        response: {
          data: {
            details: {
              message: 'This is a general error without emoji'
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={generalError} />
        </TestWrapper>
      );

      expect(screen.getByTestId('error-type')).toHaveTextContent('General Error');
      expect(screen.queryByTestId('admin-notice')).not.toBeInTheDocument();
    });
  });

  describe('Edge Cases', () => {
    test('should not render when error is null', () => {
      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={null} />
        </TestWrapper>
      );

      expect(screen.queryByTestId('configuration-error-display')).not.toBeInTheDocument();
    });

    test('should not render when error is undefined', () => {
      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={undefined} />
        </TestWrapper>
      );

      expect(screen.queryByTestId('configuration-error-display')).not.toBeInTheDocument();
    });

    test('should handle deeply nested error structures', () => {
      const complexError = {
        response: {
          data: {
            error: {
              details: {
                id: 'NESTED-ERROR-123',
                message: '❌ Deeply nested configuration error'
              }
            }
          }
        }
      };

      render(
        <TestWrapper>
          <ConfigurationErrorDisplay error={complexError} />
        </TestWrapper>
      );

      // Should still display the error even if structure is different
      expect(screen.getByTestId('configuration-error-display')).toBeInTheDocument();
    });
  });
});
