/**
 * Unit tests for useAuth hook
 * Tests authentication hook functionality
 */

import { renderHook } from '@testing-library/react';
import React from 'react';
import useAuth from '../../../hooks/useAuth';
import { UserContext } from '../../../contexts/UserContext';

// Mock UserContext
const mockUserContextValue = {
  isAuthenticated: true,
  user: {
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin',
  },
  login: jest.fn(),
  logout: jest.fn(),
  register: jest.fn(),
  method: 'JWT',
};

const createWrapper = (contextValue = mockUserContextValue) => {
  return ({ children }) => (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

describe('useAuth Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('returns authentication context value', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(result.current).toEqual(mockUserContextValue);
  });

  it('returns authenticated user data', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(result.current.isAuthenticated).toBe(true);
    expect(result.current.user).toEqual({
      id: '1',
      name: 'Test User',
      email: '<EMAIL>',
      role: 'admin',
    });
  });

  it('returns unauthenticated state', () => {
    const unauthenticatedContext = {
      ...mockUserContextValue,
      isAuthenticated: false,
      user: null,
    };

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(unauthenticatedContext),
    });

    expect(result.current.isAuthenticated).toBe(false);
    expect(result.current.user).toBeNull();
  });

  it('provides login function', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.login).toBe('function');
    expect(result.current.login).toBe(mockUserContextValue.login);
  });

  it('provides logout function', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.logout).toBe('function');
    expect(result.current.logout).toBe(mockUserContextValue.logout);
  });

  it('provides register function', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(typeof result.current.register).toBe('function');
    expect(result.current.register).toBe(mockUserContextValue.register);
  });

  it('returns authentication method', () => {
    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    expect(result.current.method).toBe('JWT');
  });

  it('handles different authentication methods', () => {
    const auth0Context = {
      ...mockUserContextValue,
      method: 'Auth0',
    };

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(auth0Context),
    });

    expect(result.current.method).toBe('Auth0');
  });

  it('handles context updates', () => {
    let contextValue = {
      ...mockUserContextValue,
      isAuthenticated: false,
      user: null,
    };

    const { result, rerender } = renderHook(() => useAuth(), {
      wrapper: createWrapper(contextValue),
    });

    expect(result.current.isAuthenticated).toBe(false);

    // Update context value
    contextValue = {
      ...mockUserContextValue,
      isAuthenticated: true,
      user: { id: '1', name: 'Test User' },
    };

    rerender();

    // Note: In a real scenario, the context would be updated by the provider
    // This test demonstrates the hook's ability to reflect context changes
  });

  it('handles missing context gracefully', () => {
    // Test without provider (should throw or handle gracefully)
    expect(() => {
      renderHook(() => useAuth());
    }).toThrow(); // useContext will throw if no provider is found
  });

  it('handles undefined user data', () => {
    const contextWithUndefinedUser = {
      ...mockUserContextValue,
      user: undefined,
    };

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(contextWithUndefinedUser),
    });

    expect(result.current.user).toBeUndefined();
  });

  it('handles partial user data', () => {
    const contextWithPartialUser = {
      ...mockUserContextValue,
      user: {
        id: '1',
        name: 'Test User',
        // Missing email and role
      },
    };

    const { result } = renderHook(() => useAuth(), {
      wrapper: createWrapper(contextWithPartialUser),
    });

    expect(result.current.user.id).toBe('1');
    expect(result.current.user.name).toBe('Test User');
    expect(result.current.user.email).toBeUndefined();
    expect(result.current.user.role).toBeUndefined();
  });

  it('maintains function references', () => {
    const { result, rerender } = renderHook(() => useAuth(), {
      wrapper: createWrapper(),
    });

    const initialLogin = result.current.login;
    const initialLogout = result.current.logout;
    const initialRegister = result.current.register;

    rerender();

    expect(result.current.login).toBe(initialLogin);
    expect(result.current.logout).toBe(initialLogout);
    expect(result.current.register).toBe(initialRegister);
  });
});
