/**
 * Unit tests for useDeploymentUrls hook
 * Tests the hook that manages NetSuite deployment URLs
 */

import { renderHook, waitFor } from '@testing-library/react';
import { mockGetSuccess, mockGetError, mockDeploymentUrls } from '../../setup/axios-mock';

// Mock the useDeploymentUrls hook since we don't have access to the actual implementation
// In a real scenario, you would import the actual hook
const mockUseDeploymentUrls = () => {
  const [deploymentUrls, setDeploymentUrls] = React.useState(null);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState(null);

  React.useEffect(() => {
    const fetchUrls = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Simulate API call to fetch deployment URLs
        const response = await fetch('/app/site/hosting/scriptlet.nl');
        const data = await response.json();
        
        setDeploymentUrls(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchUrls();
  }, []);

  const getDeploymentUrl = (key) => {
    return deploymentUrls?.[key] || null;
  };

  return {
    deploymentUrls,
    loading,
    error,
    getDeploymentUrl,
  };
};

// Mock React since we're using it in the mock hook
const React = {
  useState: jest.fn(),
  useEffect: jest.fn(),
};

describe('useDeploymentUrls Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockGetSuccess(mockDeploymentUrls);
  });

  describe('Hook Functionality', () => {
    it('should return deployment URLs structure', () => {
      // Mock React hooks
      let stateValue = null;
      let loadingValue = true;
      let errorValue = null;

      React.useState
        .mockReturnValueOnce([stateValue, jest.fn()])
        .mockReturnValueOnce([loadingValue, jest.fn()])
        .mockReturnValueOnce([errorValue, jest.fn()]);

      React.useEffect.mockImplementation((callback) => callback());

      const result = mockUseDeploymentUrls();

      expect(result).toHaveProperty('deploymentUrls');
      expect(result).toHaveProperty('loading');
      expect(result).toHaveProperty('error');
      expect(result).toHaveProperty('getDeploymentUrl');
      expect(typeof result.getDeploymentUrl).toBe('function');
    });

    it('should provide getDeploymentUrl function', () => {
      const mockUrls = mockDeploymentUrls;
      
      React.useState
        .mockReturnValueOnce([mockUrls, jest.fn()])
        .mockReturnValueOnce([false, jest.fn()])
        .mockReturnValueOnce([null, jest.fn()]);

      React.useEffect.mockImplementation((callback) => callback());

      const result = mockUseDeploymentUrls();
      
      expect(result.getDeploymentUrl('core_account_url')).toBe(mockUrls.core_account_url);
      expect(result.getDeploymentUrl('get_project_form_data_external')).toBe(mockUrls.get_project_form_data_external);
      expect(result.getDeploymentUrl('nonexistent_key')).toBeNull();
    });
  });

  describe('NetSuite URL Validation', () => {
    it('should contain all required NetSuite deployment URLs', () => {
      const urls = mockDeploymentUrls;

      // Core URLs
      expect(urls.success).toBe(true);
      expect(urls.core_account_url).toContain('tstdrv2149044.app.netsuite.com');
      expect(urls.logout_url).toContain('nllogoutnoback.jsp');

      // Project task metrics
      expect(urls.get_project_task_metrics).toContain('script=916');
      expect(urls.get_project_task_metrics_external).toContain('script=916');

      // Record operations
      expect(urls.record_operation_internal).toContain('script=920');
      expect(urls.record_operation_external).toContain('script=920');

      // Project details
      expect(urls.get_project_details_internal).toContain('script=897');
      expect(urls.get_project_details_external).toContain('script=897');

      // Reports
      expect(urls.get_reports_internal).toContain('script=1145');
      expect(urls.get_reports_external).toContain('script=1145');

      // Web orders
      expect(urls.post_web_order_internal).toContain('script=1037');
      expect(urls.post_web_order_external).toContain('script=1037');

      // Customer cases
      expect(urls.get_customer_cases_internal).toContain('script=1028');
      expect(urls.get_customer_cases_external).toContain('script=1028');
      expect(urls.get_customer_case_internal).toContain('script=1029');
      expect(urls.get_customer_case_external).toContain('script=1029');

      // Customer items
      expect(urls.get_customer_items_internal).toContain('script=1036');
      expect(urls.get_customer_items_external).toContain('script=1036');

      // Item rental inventory
      expect(urls.get_item_rental_inventory_internal).toContain('script=890');
      expect(urls.get_item_rental_inventory_external).toContain('script=890');

      // Calendar events
      expect(urls.get_calendar_events_internal).toContain('script=1119');
      expect(urls.get_calendar_events_external).toContain('script=1119');

      // Item order history
      expect(urls.get_item_order_history_internal).toContain('script=1120');
      expect(urls.get_item_order_history_external).toContain('script=1120');

      // Location info
      expect(urls.get_location_info_internal).toContain('script=1168');
      expect(urls.get_location_info_external).toContain('script=1168');

      // Project form data
      expect(urls.get_project_form_data_internal).toContain('script=1188');
      expect(urls.get_project_form_data_external).toContain('script=1188');

      // Customer service chat
      expect(urls.get_cs_chat_url).toContain('core/media/media.nl');
    });

    it('should validate internal vs external URL patterns', () => {
      const urls = mockDeploymentUrls;

      // Internal URLs should use app.netsuite.com domain
      const internalUrls = [
        'get_project_task_metrics',
        'record_operation_internal',
        'get_project_details_internal',
        'get_reports_internal',
        'post_web_order_internal',
        'get_customer_cases_internal',
        'get_customer_case_internal',
        'get_customer_items_internal',
        'get_item_rental_inventory_internal',
        'get_calendar_events_internal',
        'get_item_order_history_internal',
        'get_location_info_internal',
        'get_project_form_data_internal',
      ];

      internalUrls.forEach((key) => {
        expect(urls[key]).toContain('tstdrv2149044.app.netsuite.com');
        expect(urls[key]).not.toContain('extforms.netsuite.com');
        expect(urls[key]).not.toContain('ns-at=');
      });

      // External URLs should use extforms.netsuite.com domain and contain authentication tokens
      const externalUrls = [
        'get_project_task_metrics_external',
        'record_operation_external',
        'get_project_details_external',
        'get_reports_external',
        'post_web_order_external',
        'get_customer_cases_external',
        'get_customer_case_external',
        'get_customer_items_external',
        'get_item_rental_inventory_external',
        'get_calendar_events_external',
        'get_item_order_history_external',
        'get_location_info_external',
        'get_project_form_data_external',
      ];

      externalUrls.forEach((key) => {
        expect(urls[key]).toContain('extforms.netsuite.com');
        expect(urls[key]).toContain('ns-at=');
        expect(urls[key]).toContain('AAEJ7tMQ'); // Authentication token prefix
      });
    });

    it('should validate script IDs are correct', () => {
      const urls = mockDeploymentUrls;

      // Verify specific script IDs for each endpoint
      const scriptMappings = {
        get_project_task_metrics: '916',
        record_operation_internal: '920',
        get_project_details_internal: '897',
        get_reports_internal: '1145',
        post_web_order_internal: '1037',
        get_customer_cases_internal: '1028',
        get_customer_case_internal: '1029',
        get_customer_items_internal: '1036',
        get_item_rental_inventory_internal: '890',
        get_calendar_events_internal: '1119',
        get_item_order_history_internal: '1120',
        get_location_info_internal: '1168',
        get_project_form_data_internal: '1188',
      };

      Object.entries(scriptMappings).forEach(([key, scriptId]) => {
        expect(urls[key]).toContain(`script=${scriptId}`);
      });
    });

    it('should validate all URLs contain required NetSuite parameters', () => {
      const urls = mockDeploymentUrls;

      // Get all scriptlet URLs (excluding core_account_url, logout_url, and get_cs_chat_url)
      const scriptletUrls = Object.entries(urls)
        .filter(([key]) => !['success', 'core_account_url', 'logout_url', 'get_cs_chat_url'].includes(key))
        .map(([, url]) => url);

      scriptletUrls.forEach((url) => {
        expect(url).toContain('scriptlet.nl');
        expect(url).toContain('deploy=1');
        expect(url).toContain('compid=TSTDRV2149044');
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle deployment URL fetch errors', () => {
      mockGetError(500, 'Failed to fetch deployment URLs');

      React.useState
        .mockReturnValueOnce([null, jest.fn()])
        .mockReturnValueOnce([false, jest.fn()])
        .mockReturnValueOnce(['Failed to fetch deployment URLs', jest.fn()]);

      React.useEffect.mockImplementation((callback) => callback());

      const result = mockUseDeploymentUrls();

      expect(result.error).toBe('Failed to fetch deployment URLs');
      expect(result.deploymentUrls).toBeNull();
    });

    it('should handle network errors gracefully', () => {
      mockGetError(0, 'Network Error');

      React.useState
        .mockReturnValueOnce([null, jest.fn()])
        .mockReturnValueOnce([false, jest.fn()])
        .mockReturnValueOnce(['Network Error', jest.fn()]);

      React.useEffect.mockImplementation((callback) => callback());

      const result = mockUseDeploymentUrls();

      expect(result.error).toBe('Network Error');
      expect(result.deploymentUrls).toBeNull();
    });
  });

  describe('URL Utility Functions', () => {
    it('should provide helper to get specific deployment URLs', () => {
      const urls = mockDeploymentUrls;

      React.useState
        .mockReturnValueOnce([urls, jest.fn()])
        .mockReturnValueOnce([false, jest.fn()])
        .mockReturnValueOnce([null, jest.fn()]);

      React.useEffect.mockImplementation((callback) => callback());

      const result = mockUseDeploymentUrls();

      // Test getting specific URLs
      expect(result.getDeploymentUrl('core_account_url')).toBe(urls.core_account_url);
      expect(result.getDeploymentUrl('get_project_form_data_external')).toBe(urls.get_project_form_data_external);
      expect(result.getDeploymentUrl('get_item_order_history_internal')).toBe(urls.get_item_order_history_internal);
      
      // Test non-existent key
      expect(result.getDeploymentUrl('non_existent_url')).toBeNull();
    });
  });
});
