/**
 * Unit tests for useRefMounted hook
 * Tests component mount state tracking functionality
 */
import React from 'react';
import { renderHook } from '@testing-library/react';
import useRefMounted from '../../../hooks/useRefMounted';

describe('useRefMounted Hook', () => {
  it('returns a ref object', () => {
    const { result } = renderHook(() => useRefMounted());

    expect(result.current).toHaveProperty('current');
    expect(typeof result.current).toBe('object');
  });

  it('initializes with current set to true', () => {
    const { result } = renderHook(() => useRefMounted());

    expect(result.current.current).toBe(true);
  });

  it('maintains true value while component is mounted', () => {
    const { result, rerender } = renderHook(() => useRefMounted());

    expect(result.current.current).toBe(true);

    // Trigger re-render
    rerender();

    expect(result.current.current).toBe(true);
  });

  it('sets current to false when component unmounts', () => {
    const { result, unmount } = renderHook(() => useRefMounted());

    expect(result.current.current).toBe(true);

    // Unmount the component
    unmount();

    // After unmount, the ref should be set to false
    expect(result.current.current).toBe(false);
  });

  it('returns the same ref object across re-renders', () => {
    const { result, rerender } = renderHook(() => useRefMounted());

    const initialRef = result.current;

    rerender();

    expect(result.current).toBe(initialRef);
  });

  it('can be used to prevent state updates after unmount', () => {
    let mockSetState = jest.fn();

    const { result, unmount } = renderHook(() => {
      const isMountedRef = useRefMounted();

      const safeSetState = (newState) => {
        if (isMountedRef.current) {
          mockSetState(newState);
        }
      };

      return { isMountedRef, safeSetState };
    });

    // Component is mounted, should allow state updates
    result.current.safeSetState('test');
    expect(mockSetState).toHaveBeenCalledWith('test');

    mockSetState.mockClear();

    // Unmount component
    unmount();

    // After unmount, should not allow state updates
    result.current.safeSetState('test2');
    expect(mockSetState).not.toHaveBeenCalled();
  });

  it('works with multiple instances', () => {
    const { result: result1 } = renderHook(() => useRefMounted());
    const { result: result2, unmount: unmount2 } = renderHook(() =>
      useRefMounted()
    );

    expect(result1.current.current).toBe(true);
    expect(result2.current.current).toBe(true);

    // Unmount only the second instance
    unmount2();

    expect(result1.current.current).toBe(true);
    expect(result2.current.current).toBe(false);
  });

  it('handles rapid mount/unmount cycles', () => {
    const { result, unmount, rerender } = renderHook(() => useRefMounted());

    expect(result.current.current).toBe(true);

    rerender();
    expect(result.current.current).toBe(true);

    unmount();
    expect(result.current.current).toBe(false);
  });

  it('cleanup function is called on unmount', () => {
    const cleanupSpy = jest.fn();

    const { unmount } = renderHook(() => {
      const isMountedRef = useRefMounted();

      // Simulate a useEffect that depends on the mounted ref
      React.useEffect(() => {
        return cleanupSpy;
      }, []);

      return isMountedRef;
    });

    unmount();

    expect(cleanupSpy).toHaveBeenCalled();
  });

  it('can be used in async operations', async () => {
    let resolvePromise;
    const asyncOperation = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    const { result, unmount } = renderHook(() => {
      const isMountedRef = useRefMounted();

      const performAsyncOperation = async () => {
        await asyncOperation;
        return isMountedRef.current;
      };

      return { isMountedRef, performAsyncOperation };
    });

    const operationPromise = result.current.performAsyncOperation();

    // Unmount before async operation completes
    unmount();

    // Resolve the async operation
    resolvePromise();

    const isMountedAfterAsync = await operationPromise;
    expect(isMountedAfterAsync).toBe(false);
  });
});
