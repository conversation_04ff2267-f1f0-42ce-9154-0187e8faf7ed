/**
 * Jest-based axios mocking setup
 * Uses <PERSON><PERSON>'s built-in mocking capabilities to mock axios HTTP requests
 */

import axios from 'axios';

// Mock axios module
jest.mock('axios');

// <PERSON><PERSON> typed mock for better TypeScript support
const mockedAxios = axios;

// Mock data
export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'admin',
  avatar: '/test-avatar.jpg',
  profile: {
    jobtitle: 'Test Developer',
    location: 'Test City',
    username: 'testuser',
    posts: '10',
    followers: '100',
    description: 'Test user description',
  },
};

export const mockProjects = [
  {
    id: '1',
    name: 'Test Project 1',
    description: 'First test project',
    status: 'active',
    createdAt: '2023-01-01T00:00:00Z',
    tasks: [],
    files: [],
    cases: [],
  },
  {
    id: '2',
    name: 'Test Project 2',
    description: 'Second test project',
    status: 'completed',
    createdAt: '2023-01-02T00:00:00Z',
    tasks: [],
    files: [],
    cases: [],
  },
];

export const mockFormData = {
  formSelected: {
    name: 'Test Form',
    id: '1',
  },
  formLayout: 'stack',
  formGroups: [
    {
      title: 'Basic Information',
      columns: '2',
      description: 'Basic project information',
    },
    {
      title: 'Contact Details',
      columns: '1',
      description: 'Contact information',
    },
  ],
  formFields: [
    {
      name: 'project_name',
      label: 'Project Name',
      type: 'text',
      value: 'Test Project',
      group: 'Basic Information',
    },
    {
      name: 'project_description',
      label: 'Project Description',
      type: 'textarea',
      value: 'Test description',
      group: 'Basic Information',
    },
    {
      name: 'contact_email',
      label: 'Contact Email',
      type: 'email',
      value: '<EMAIL>',
      group: 'Contact Details',
    },
  ],
};

export const mockDeploymentUrls = {
  success: true,
  core_account_url: "https://tstdrv2149044.app.netsuite.com",
  logout_url: "https://tstdrv2149044.app.netsuite.com/pages/nllogoutnoback.jsp",
  get_project_task_metrics: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=916&deploy=1&compid=TSTDRV2149044",
  get_project_task_metrics_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=916&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQs9vB4ZBXxYTB2aWbgS1cazXvGi8-dVqXlJDDvGfmb54",
  record_operation_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=920&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQNsxu2NFaVInGsJO_3dbbsZCJMPKxQMD12kh5L_jFLJ4",
  record_operation_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=920&deploy=1&compid=TSTDRV2149044",
  get_project_details_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=897&deploy=1&compid=TSTDRV2149044",
  get_project_details_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=897&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQfe7zGd2EkxAwRPbYQEToWBVT8y27ipPLa3pe6-0i2eE",
  get_reports_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1145&deploy=1&compid=TSTDRV2149044",
  get_reports_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1145&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQQfywOHeRbvlDLyu4O2vyRqjtUTRKP1TxSy8ow5Izzeg",
  post_web_order_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1037&deploy=1&compid=TSTDRV2149044",
  post_web_order_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1037&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQOzawgeFyqOc54qjSTc1JyDtCO4-KDL1gDlFbqzmPPk4",
  get_customer_cases_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1028&deploy=1&compid=TSTDRV2149044",
  get_customer_cases_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1028&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQ27qzr5ednwS0vcxdz6aVXpEnYDDWT5UpVPHMK8btx3I",
  get_customer_case_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1029&deploy=1&compid=TSTDRV2149044",
  get_customer_case_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1029&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQ0nhbM5Jpw0f69EzZrB82rKDeQ-jWcaVaKSGxhHn8x0U",
  get_customer_items_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1036&deploy=1&compid=TSTDRV2149044",
  get_customer_items_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1036&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQSdTpd1U56lEzG4z9Y3kHfqPwwDwmkYmwfuiZG2viahw",
  get_item_rental_inventory_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=890&deploy=1&compid=TSTDRV2149044",
  get_item_rental_inventory_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=890&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQlZvFy8vyrttyARlS5NayXZMEUd0OTXLbQLN4Ner6U0Q",
  get_calendar_events_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1119&deploy=1&compid=TSTDRV2149044",
  get_calendar_events_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1119&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQhVcl7b_hS1HaUoKZUaIdGI-nrwGy-NJPXZrjaT5oxCU",
  get_item_order_history_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1120&deploy=1&compid=TSTDRV2149044",
  get_item_order_history_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1120&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQYxVEA1QZel-WYV8Gnmr_ZRIe-tjnr2ytSGFJhBfpsyk",
  get_location_info_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1168&deploy=1&compid=TSTDRV2149044",
  get_location_info_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1168&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQMvZjHOqDUOJ1e0ZX0NEIha-3NEEgz4_Vqa9vdPu_rCg",
  get_project_form_data_internal: "https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1188&deploy=1&compid=TSTDRV2149044",
  get_project_form_data_external: "https://tstdrv2149044.extforms.netsuite.com/app/site/hosting/scriptlet.nl?script=1188&deploy=1&compid=TSTDRV2149044&ns-at=AAEJ7tMQr4q1aTMvTUD4DkrSv281stoI8ovOBrJN6RgoycOJ56w",
  get_cs_chat_url: "https://tstdrv2149044.app.netsuite.com/core/media/media.nl?id=31804&c=TSTDRV2149044&h=VIqaunZMXIU--WhUURr3AkBKIBUvuUEtQg-LMi6IjG_SelZs&mv=marerl68&_xt=.html"
};

// Helper functions for setting up mocks
export const mockAxiosSuccess = (method, data, status = 200) => {
  const response = {
    data,
    status,
    statusText: 'OK',
    headers: {},
    config: {},
  };

  switch (method.toLowerCase()) {
    case 'get':
      mockedAxios.get.mockResolvedValue(response);
      break;
    case 'post':
      mockedAxios.post.mockResolvedValue(response);
      break;
    case 'put':
      mockedAxios.put.mockResolvedValue(response);
      break;
    case 'delete':
      mockedAxios.delete.mockResolvedValue(response);
      break;
    case 'patch':
      mockedAxios.patch.mockResolvedValue(response);
      break;
    default:
      throw new Error(`Unsupported HTTP method: ${method}`);
  }

  return response;
};

export const mockAxiosError = (method, status = 500, message = 'Server Error') => {
  const error = new Error(message);
  error.response = {
    data: { message },
    status,
    statusText: status === 404 ? 'Not Found' : 'Error',
    headers: {},
    config: {},
  };

  switch (method.toLowerCase()) {
    case 'get':
      mockedAxios.get.mockRejectedValue(error);
      break;
    case 'post':
      mockedAxios.post.mockRejectedValue(error);
      break;
    case 'put':
      mockedAxios.put.mockRejectedValue(error);
      break;
    case 'delete':
      mockedAxios.delete.mockRejectedValue(error);
      break;
    case 'patch':
      mockedAxios.patch.mockRejectedValue(error);
      break;
    default:
      throw new Error(`Unsupported HTTP method: ${method}`);
  }

  return error;
};

// Convenience functions for common scenarios
export const mockGetSuccess = (data, status = 200) => mockAxiosSuccess('get', data, status);
export const mockPostSuccess = (data, status = 201) => mockAxiosSuccess('post', data, status);
export const mockPutSuccess = (data, status = 200) => mockAxiosSuccess('put', data, status);
export const mockDeleteSuccess = (data = null, status = 204) => mockAxiosSuccess('delete', data, status);

export const mockGetError = (status = 500, message = 'Server Error') => mockAxiosError('get', status, message);
export const mockPostError = (status = 400, message = 'Bad Request') => mockAxiosError('post', status, message);
export const mockPutError = (status = 400, message = 'Bad Request') => mockAxiosError('put', status, message);
export const mockDeleteError = (status = 404, message = 'Not Found') => mockAxiosError('delete', status, message);

// Mock error responses for NetSuite admin misconfiguration
export const mockNetSuiteConfigurationError = {
  id: "ERR-1750963949169-856",
  message: "Error getting user information",
  details: {
    id: "ERR-1750963949169-856",
    message: "❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.",
    stack: "Error: ❌ Printout templates are required in settings. Please configure Sales Order and Invoice printing templates.\n    at Object.onRequest (/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/suitelets/web/ng_cs_sl_get_current_user.js:676:15)",
    name: "Error",
    customMessage: "Error getting user information"
  }
};

// Mock settings response structures
export const mockValidSettings = {
  // Core EH Settings
  custrecord_ng_eh_default_portal_ordform: [{ value: '123', text: 'Default Portal Order Form' }],
  custrecord_ng_eh_invoice_printing_temp: [{ value: '456', text: 'Invoice Printing Template' }],
  custrecord_ng_eh_sales_order_printing_te: [{ value: '789', text: 'Sales Order Printing Template' }],
  custrecord_ng_eh_primary_web_color: '#1976d2',
  custrecord_ng_eh_secondary_web_color: '#dc004e',
  custrecord_ng_eh_web_dark_brand: 'F',
  custrecord_ng_eh_react_logo_image_url: '/test-logo.png',
  custrecord_ng_eh_default_portal_blurb: 'Welcome to the portal',
  custrecord_ng_eh_react_fallback_cover: '/fallback-cover.jpg',

  // Additional settings data
  project_statuses: [
    { id: '1', name: 'In Progress' },
    { id: '2', name: 'Completed' },
    { id: '3', name: 'On Hold' }
  ],
  exhibitSpaceList: [
    { id: '1', name: 'Booth Space A' },
    { id: '2', name: 'Booth Space B' }
  ],
  showServicesOrderedByList: [
    { id: '1', name: 'Service Order 1' },
    { id: '2', name: 'Service Order 2' }
  ],
  eventNameList: [
    { id: '1', name: 'Trade Show 2024' },
    { id: '2', name: 'Conference 2024' }
  ],
  cardForShowServicesList: [
    { id: '1', name: 'Card Service 1' },
    { id: '2', name: 'Card Service 2' }
  ],
  shipToAddressList: [
    { id: '1', name: 'Warehouse A' },
    { id: '2', name: 'Warehouse B' }
  ]
};

// Mock settings with missing required printing template fields
export const mockSettingsWithMissingPrintingTemplates = {
  ...mockValidSettings,
  custrecord_ng_eh_default_portal_ordform: [], // Missing
  custrecord_ng_eh_invoice_printing_temp: [], // Missing
  custrecord_ng_eh_sales_order_printing_te: [], // Missing
};

// Mock settings with missing individual fields
export const mockSettingsWithMissingPortalForm = {
  ...mockValidSettings,
  custrecord_ng_eh_default_portal_ordform: [], // Missing only this field
};

export const mockSettingsWithMissingInvoiceTemplate = {
  ...mockValidSettings,
  custrecord_ng_eh_invoice_printing_temp: [], // Missing only this field
};

export const mockSettingsWithMissingSalesOrderTemplate = {
  ...mockValidSettings,
  custrecord_ng_eh_sales_order_printing_te: [], // Missing only this field
};

// Mock minimal valid settings for successful app loading
export const mockMinimalValidSettings = {
  custrecord_ng_eh_default_portal_ordform: [{ value: '123', text: 'Default Portal Order Form' }],
  custrecord_ng_eh_invoice_printing_temp: [{ value: '456', text: 'Invoice Printing Template' }],
  custrecord_ng_eh_sales_order_printing_te: [{ value: '789', text: 'Sales Order Printing Template' }],
  custrecord_ng_eh_primary_web_color: '#1976d2',
  custrecord_ng_eh_secondary_web_color: '#dc004e',
  custrecord_ng_eh_web_dark_brand: 'F',
  project_statuses: [],
  exhibitSpaceList: [],
  showServicesOrderedByList: [],
  eventNameList: [],
  cardForShowServicesList: [],
  shipToAddressList: []
};

// Settings endpoint error responses
export const mockSettingsNetworkError = {
  error: 'NETWORK_ERROR',
  message: 'Failed to connect to NetSuite settings endpoint'
};

export const mockSettingsServerError = {
  error: 'SETTINGS_ERROR',
  message: 'Internal server error while retrieving settings'
};

export const mockSettingsConfigurationError = {
  error: 'CONFIGURATION_ERROR',
  message: 'Required configuration fields are missing',
  details: {
    missingFields: [
      'custrecord_ng_eh_default_portal_ordform',
      'custrecord_ng_eh_invoice_printing_temp',
      'custrecord_ng_eh_sales_order_printing_te'
    ]
  }
};

// Helper function to mock NetSuite user suitelet configuration errors
export const mockUserSuiteletConfigError = () => {
  const error = new Error('Error getting user information');
  error.response = {
    data: mockNetSuiteConfigurationError,
    status: 500,
    statusText: 'Internal Server Error',
    headers: {},
    config: {},
  };

  // Store the current mock implementation to preserve other mocks
  const currentMock = mockedAxios.get.getMockImplementation();

  // Mock the specific user suitelet endpoint while preserving other mocks
  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=902') || url.includes('REACT_APP_GET_USER_SUITELET')) {
      return Promise.reject(error);
    }
    // Check if there's an existing mock implementation for other URLs
    if (currentMock) {
      return currentMock(url);
    }
    // Fall back to default implementation for other URLs
    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  return error;
};

// Combined mock function for both settings and user errors
export const mockBothSettingsAndUserErrors = () => {
  const userError = new Error('Error getting user information');
  userError.response = {
    data: mockNetSuiteConfigurationError,
    status: 500,
    statusText: 'Internal Server Error',
    headers: {},
    config: {},
  };

  const settingsError = new Error('Error getting settings');
  settingsError.response = {
    data: { error: 'Settings configuration error' },
    status: 500,
    statusText: 'Internal Server Error',
    headers: {},
    config: {},
  };

  mockedAxios.get.mockImplementation((url) => {
    // Mock user suitelet endpoint to fail
    if (url.includes('script=902') || url.includes('REACT_APP_GET_USER_SUITELET')) {
      return Promise.reject(userError);
    }

    // Mock settings endpoint to also fail
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.reject(settingsError);
    }

    // Default response for other URLs
    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  return { userError, settingsError };
};

// Helper functions for mocking NetSuite settings endpoint scenarios

/**
 * Mock successful settings endpoint response
 * @param {Object} settingsData - Settings data to return (defaults to mockValidSettings)
 */
export const mockSettingsSuccess = (settingsData = mockValidSettings) => {
  mockedAxios.get.mockImplementation((url) => {
    // Handle settings endpoint (REACT_APP_SUITELET_SETTINGS or REACT_APP_RESTLET_SETTINGS)
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.resolve({
        data: settingsData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Fall back to default implementation for other URLs
    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });
};

/**
 * Mock settings endpoint network error
 */
export const mockSettingsNetworkFailure = () => {
  const error = new Error('Network Error');
  error.code = 'NETWORK_ERROR';

  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.reject(error);
    }

    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  return error;
};

/**
 * Mock settings endpoint server error
 */
export const mockSettingsServerFailure = () => {
  const error = new Error('Internal Server Error');
  error.response = {
    data: mockSettingsServerError,
    status: 500,
    statusText: 'Internal Server Error',
    headers: {},
    config: {},
  };

  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.reject(error);
    }

    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  return error;
};

/**
 * Mock settings endpoint with missing printing templates
 */
export const mockSettingsMissingPrintingTemplates = () => {
  // Store the current mock implementation to preserve other mocks
  const currentMock = mockedAxios.get.getMockImplementation();

  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.resolve({
        data: mockSettingsWithMissingPrintingTemplates,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Check if there's an existing mock implementation for other URLs
    if (currentMock) {
      return currentMock(url);
    }

    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });
};

/**
 * Mock settings endpoint with missing specific fields
 * @param {string} missingField - Which field to mock as missing ('portal', 'invoice', 'salesorder')
 */
export const mockSettingsMissingSpecificField = (missingField) => {
  let settingsData;

  switch (missingField) {
    case 'portal':
      settingsData = mockSettingsWithMissingPortalForm;
      break;
    case 'invoice':
      settingsData = mockSettingsWithMissingInvoiceTemplate;
      break;
    case 'salesorder':
      settingsData = mockSettingsWithMissingSalesOrderTemplate;
      break;
    default:
      settingsData = mockSettingsWithMissingPrintingTemplates;
  }

  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.resolve({
        data: settingsData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });
};

/**
 * Mock minimal valid settings for successful app loading
 */
export const mockSettingsMinimalValid = () => {
  mockedAxios.get.mockImplementation((url) => {
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.resolve({
        data: mockMinimalValidSettings,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });
};

// Setup default mocks for common endpoints
export const setupDefaultMocks = () => {
  // Authentication endpoints
  mockedAxios.post.mockImplementation((url, data) => {
    if (url.includes('/api/auth/login')) {
      return Promise.resolve({
        data: {
          user: mockUser,
          accessToken: 'mock-access-token',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    if (url.includes('/api/auth/logout')) {
      return Promise.resolve({
        data: { message: 'Logged out successfully' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    if (url.includes('/api/auth/register')) {
      return Promise.resolve({
        data: {
          user: { id: '2', name: 'New User', email: '<EMAIL>' },
          accessToken: 'mock-token',
        },
        status: 201,
        statusText: 'Created',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite web order endpoints
    if (url.includes('script=1037')) {
      return Promise.resolve({
        data: {
          orderId: '12345',
          orderNumber: 'WO-001',
          status: 'Submitted',
          total: data.total || 0,
          items: data.items || [],
          message: 'Order submitted successfully',
        },
        status: 201,
        statusText: 'Created',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite record operations
    if (url.includes('script=920')) {
      return Promise.resolve({
        data: {
          recordId: '98765',
          type: data.recordType || 'custom',
          operation: data.operation || 'create',
          status: 'success',
          message: 'Record operation completed successfully',
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Default POST response
    return Promise.resolve({
      data: { id: '3', ...data },
      status: 201,
      statusText: 'Created',
      headers: {},
      config: {},
    });
  });

  // GET endpoints
  mockedAxios.get.mockImplementation((url) => {
    // Handle NetSuite settings endpoint (REACT_APP_SUITELET_SETTINGS or REACT_APP_RESTLET_SETTINGS)
    if (url.includes('script=918') || url.includes('REACT_APP_SUITELET_SETTINGS') || url.includes('REACT_APP_RESTLET_SETTINGS')) {
      return Promise.resolve({
        data: mockValidSettings,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite user suitelet endpoint (REACT_APP_GET_USER_SUITELET)
    if (url.includes('script=902') || url.includes('REACT_APP_GET_USER_SUITELET')) {
      return Promise.resolve({
        data: {
          success: true,
          timestamp: new Date().toISOString(),
          data: {
            ...mockUser,
            companyName: 'Test Company',
            profile: {
              news: [],
              record: { fields: {} },
              recentOrders: [],
              recentInvoices: [],
              event: { booths: { sizes: [] } },
              projects: { tasks: [], list: [], jobs: [] },
            },
          },
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    if (url.includes('/api/account/personal')) {
      return Promise.resolve({
        data: { user: mockUser },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    if (url.includes('/api/projects')) {
      if (url.includes('/api/projects/1')) {
        return Promise.resolve({
          data: mockProjects[0],
          status: 200,
          statusText: 'OK',
          headers: {},
          config: {},
        });
      }
      return Promise.resolve({
        data: mockProjects,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite form data endpoints
    if (url.includes('script=1188') || url.includes('external/form') || url.includes('internal/form')) {
      return Promise.resolve({
        data: mockFormData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite deployment URLs endpoint
    if (url.includes('scriptlet.nl') && !url.includes('script=')) {
      return Promise.resolve({
        data: mockDeploymentUrls,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite item order history endpoints
    if (url.includes('script=1120') || url.includes('external/orders')) {
      return Promise.resolve({
        data: {
          productId: new URL(url, 'http://localhost').searchParams.get('productId'),
          rows: [
            {
              id: '1',
              orderNumber: 'ORD-001',
              date: '2023-01-01',
              quantity: 5,
              price: 100,
            },
          ],
          columns: [
            { field: 'orderNumber', headerName: 'Order Number', width: 150 },
            { field: 'date', headerName: 'Date', width: 120 },
            { field: 'quantity', headerName: 'Quantity', width: 100 },
            { field: 'price', headerName: 'Price', width: 100 },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite project task metrics endpoints
    if (url.includes('script=916')) {
      return Promise.resolve({
        data: {
          metrics: [
            { taskId: '1', name: 'Setup', progress: 75, status: 'In Progress' },
            { taskId: '2', name: 'Testing', progress: 25, status: 'Not Started' },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite project details endpoints
    if (url.includes('script=897')) {
      return Promise.resolve({
        data: {
          projectId: '1',
          name: 'Test Project',
          status: 'Active',
          startDate: '2023-01-01',
          endDate: '2023-12-31',
          tasks: [],
          files: [],
          cases: [],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite customer cases endpoints
    if (url.includes('script=1028')) {
      return Promise.resolve({
        data: {
          cases: [
            { id: '1', title: 'Test Case 1', status: 'Open', priority: 'High' },
            { id: '2', title: 'Test Case 2', status: 'Closed', priority: 'Medium' },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite customer items endpoints
    if (url.includes('script=1036')) {
      return Promise.resolve({
        data: {
          items: [
            { id: '1', name: 'Test Item 1', quantity: 10, price: 100 },
            { id: '2', name: 'Test Item 2', quantity: 5, price: 200 },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite calendar events endpoints
    if (url.includes('script=1119')) {
      return Promise.resolve({
        data: {
          events: [
            { id: '1', title: 'Meeting', date: '2023-12-01', time: '10:00 AM' },
            { id: '2', title: 'Deadline', date: '2023-12-15', time: '5:00 PM' },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite location info endpoints
    if (url.includes('script=1168')) {
      return Promise.resolve({
        data: {
          locations: [
            { id: '1', name: 'Main Office', address: '123 Main St', city: 'Test City' },
            { id: '2', name: 'Warehouse', address: '456 Storage Ave', city: 'Test City' },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Handle NetSuite reports endpoints
    if (url.includes('script=1145')) {
      return Promise.resolve({
        data: {
          reports: [
            { id: '1', name: 'Sales Report', type: 'financial', lastUpdated: '2023-12-01' },
            { id: '2', name: 'Inventory Report', type: 'operational', lastUpdated: '2023-12-01' },
          ],
        },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    // Default GET response
    return Promise.resolve({
      data: {},
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  // PUT endpoints
  mockedAxios.put.mockImplementation((url, data) => {
    if (url.includes('/api/projects/')) {
      const id = url.split('/').pop();
      const project = mockProjects.find((p) => p.id === id);
      return Promise.resolve({
        data: { ...project, ...data },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });
    }

    return Promise.resolve({
      data: { ...data },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });

  // DELETE endpoints
  mockedAxios.delete.mockImplementation(() => {
    return Promise.resolve({
      data: { message: 'Deleted successfully' },
      status: 200,
      statusText: 'OK',
      headers: {},
      config: {},
    });
  });
};

// Reset all mocks to default state
export const resetMocks = () => {
  jest.clearAllMocks();
  setupDefaultMocks();
};

// Clear all mocks completely
export const clearMocks = () => {
  jest.clearAllMocks();
};

// Export the mocked axios for direct access
export { mockedAxios };
export default mockedAxios;
