/**
 * Unit tests for the Jest-based axios mocking system
 * Verifies that <PERSON><PERSON>'s built-in mocking works correctly with axios
 */

import axios from 'axios';
import { 
  mockedAxios, 
  mockGetSuccess, 
  mockPostSuccess, 
  mockGetError, 
  mockPostError,
  resetMocks,
  clearMocks 
} from './axios-mock';

describe('Jest-based Axios Mock System', () => {
  beforeEach(() => {
    resetMocks();
  });

  describe('Basic Mocking', () => {
    it('should mock GET requests with mockGetSuccess', async () => {
      const testData = { id: 1, name: 'Test Item' };
      mockGetSuccess(testData);

      const response = await axios.get('/api/test');
      
      expect(response.status).toBe(200);
      expect(response.data).toEqual(testData);
    });

    it('should mock POST requests with mockPostSuccess', async () => {
      const testData = { id: 1, name: 'Created Item' };
      mockPostSuccess(testData, 201);

      const response = await axios.post('/api/test', { name: 'New Item' });
      
      expect(response.status).toBe(201);
      expect(response.data).toEqual(testData);
    });

    it('should mock GET errors with mockGetError', async () => {
      mockGetError(404, 'Not Found');

      try {
        await axios.get('/api/test');
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(404);
        expect(error.response.data.message).toBe('Not Found');
      }
    });

    it('should mock POST errors with mockPostError', async () => {
      mockPostError(400, 'Bad Request');

      try {
        await axios.post('/api/test', {});
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toBe('Bad Request');
      }
    });
  });

  describe('Direct Jest Mock Usage', () => {
    it('should work with mockResolvedValue', async () => {
      const testData = { message: 'Direct mock' };
      
      mockedAxios.get.mockResolvedValue({
        data: testData,
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const response = await axios.get('/api/direct');
      
      expect(response.data).toEqual(testData);
      expect(response.status).toBe(200);
    });

    it('should work with mockRejectedValue', async () => {
      const error = new Error('Direct error');
      error.response = {
        data: { message: 'Direct error' },
        status: 500,
        statusText: 'Internal Server Error',
        headers: {},
        config: {},
      };

      mockedAxios.get.mockRejectedValue(error);

      try {
        await axios.get('/api/direct-error');
        fail('Should have thrown an error');
      } catch (err) {
        expect(err.message).toBe('Direct error');
        expect(err.response.status).toBe(500);
      }
    });

    it('should work with mockImplementation for dynamic responses', async () => {
      mockedAxios.post.mockImplementation((url, data) => {
        if (data.name === 'valid') {
          return Promise.resolve({
            data: { id: 1, name: data.name },
            status: 201,
            statusText: 'Created',
            headers: {},
            config: {},
          });
        } else {
          const error = new Error('Invalid data');
          error.response = {
            data: { message: 'Invalid data' },
            status: 400,
            statusText: 'Bad Request',
            headers: {},
            config: {},
          };
          return Promise.reject(error);
        }
      });

      // Test valid data
      const validResponse = await axios.post('/api/dynamic', { name: 'valid' });
      expect(validResponse.status).toBe(201);
      expect(validResponse.data.name).toBe('valid');

      // Test invalid data
      try {
        await axios.post('/api/dynamic', { name: 'invalid' });
        fail('Should have thrown an error');
      } catch (error) {
        expect(error.response.status).toBe(400);
        expect(error.response.data.message).toBe('Invalid data');
      }
    });
  });

  describe('Multiple HTTP Methods', () => {
    it('should mock PUT requests', async () => {
      mockedAxios.put.mockResolvedValue({
        data: { id: 1, name: 'Updated' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const response = await axios.put('/api/test/1', { name: 'Updated' });
      
      expect(response.status).toBe(200);
      expect(response.data.name).toBe('Updated');
    });

    it('should mock DELETE requests', async () => {
      mockedAxios.delete.mockResolvedValue({
        data: null,
        status: 204,
        statusText: 'No Content',
        headers: {},
        config: {},
      });

      const response = await axios.delete('/api/test/1');
      
      expect(response.status).toBe(204);
    });

    it('should mock PATCH requests', async () => {
      mockedAxios.patch.mockResolvedValue({
        data: { id: 1, name: 'Patched' },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      const response = await axios.patch('/api/test/1', { name: 'Patched' });
      
      expect(response.status).toBe(200);
      expect(response.data.name).toBe('Patched');
    });
  });

  describe('Mock Management', () => {
    it('should reset mocks to default state', async () => {
      // Set a custom mock
      mockedAxios.get.mockResolvedValue({
        data: { custom: true },
        status: 200,
        statusText: 'OK',
        headers: {},
        config: {},
      });

      // Reset should restore default behavior
      resetMocks();

      // Should now use default implementation
      const response = await axios.get('/api/test');
      expect(response.status).toBe(200);
      // Should not have the custom property
      expect(response.data.custom).toBeUndefined();
    });

    it('should clear all mocks', () => {
      clearMocks();

      // All mock functions should be cleared
      expect(mockedAxios.get).toHaveBeenCalledTimes(0);
      expect(mockedAxios.post).toHaveBeenCalledTimes(0);
      expect(mockedAxios.put).toHaveBeenCalledTimes(0);
      expect(mockedAxios.delete).toHaveBeenCalledTimes(0);
    });

    it('should track mock calls', async () => {
      mockGetSuccess({ test: true });

      await axios.get('/api/test');
      await axios.get('/api/test2');

      expect(mockedAxios.get).toHaveBeenCalledTimes(2);
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/test');
      expect(mockedAxios.get).toHaveBeenCalledWith('/api/test2');
    });
  });

  describe('Default Handlers', () => {
    it('should have working default auth handlers', async () => {
      const response = await axios.post('/api/auth/login', {
        email: '<EMAIL>',
        password: 'password',
      });

      expect(response.status).toBe(200);
      expect(response.data.user).toBeDefined();
      expect(response.data.accessToken).toBeDefined();
    });

    it('should have working default project handlers', async () => {
      const response = await axios.get('/api/projects');

      expect(response.status).toBe(200);
      expect(Array.isArray(response.data)).toBe(true);
      expect(response.data.length).toBeGreaterThan(0);
    });

    it('should handle project by ID', async () => {
      const response = await axios.get('/api/projects/1');

      expect(response.status).toBe(200);
      expect(response.data.id).toBe('1');
      expect(response.data.name).toBeDefined();
    });

    it('should have comprehensive NetSuite deployment URLs', async () => {
      const response = await axios.get('/app/site/hosting/scriptlet.nl');

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
      expect(response.data.core_account_url).toContain('tstdrv2149044.app.netsuite.com');
      expect(response.data.get_project_form_data_internal).toContain('script=1188');
      expect(response.data.get_item_order_history_external).toContain('extforms.netsuite.com');
      expect(response.data.get_cs_chat_url).toContain('core/media/media.nl');
    });

    it('should handle NetSuite scriptlet endpoints', async () => {
      // Test project form data endpoint
      const formResponse = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1188');
      expect(formResponse.status).toBe(200);
      expect(formResponse.data.formFields).toBeDefined();

      // Test order history endpoint
      const orderResponse = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=1120');
      expect(orderResponse.status).toBe(200);
      expect(orderResponse.data.rows).toBeDefined();

      // Test project metrics endpoint
      const metricsResponse = await axios.get('https://tstdrv2149044.app.netsuite.com/app/site/hosting/scriptlet.nl?script=916');
      expect(metricsResponse.status).toBe(200);
      expect(metricsResponse.data.metrics).toBeDefined();
    });
  });

  describe('Error Scenarios', () => {
    it('should handle different error status codes', async () => {
      const errorCodes = [400, 401, 403, 404, 500];

      for (const code of errorCodes) {
        mockGetError(code, `Error ${code}`);

        try {
          await axios.get('/api/test');
          fail(`Should have thrown error for status ${code}`);
        } catch (error) {
          expect(error.response.status).toBe(code);
          expect(error.response.data.message).toBe(`Error ${code}`);
        }

        // Reset for next iteration
        resetMocks();
      }
    });

    it('should handle network errors', async () => {
      const networkError = new Error('Network Error');
      mockedAxios.get.mockRejectedValue(networkError);

      try {
        await axios.get('/api/test');
        fail('Should have thrown a network error');
      } catch (error) {
        expect(error.message).toBe('Network Error');
      }
    });
  });
});
