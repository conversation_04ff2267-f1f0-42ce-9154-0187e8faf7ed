/**
 * Test utilities for React Testing Library
 * Provides custom render functions with providers and common test helpers
 */

import React from 'react';
import { render as rtlRender } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { Provider } from 'react-redux';
import { configureStore } from '@reduxjs/toolkit';
import { ThemeProvider } from '@mui/material/styles';
import { PureLightTheme } from '../../theme/schemes/PureLightTheme';
import rootReducer from '../../store/rootReducer';
// Mock providers that might not be available in test environment
const MockLocalizationProvider = ({ children }) => children;
const MockSnackbarProvider = ({ children }) => children;
const MockI18nextProvider = ({ children }) => children;

// Use the actual app theme instead of creating a mock
const testTheme = PureLightTheme;

// Mock store configuration
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: rootReducer,
    preloadedState: initialState,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        serializableCheck: false
      })
  });
};

// All providers wrapper (with BrowserRouter)
const AllTheProviders = ({
  children,
  initialState = {},
  theme = testTheme
}) => {
  const store = createMockStore(initialState);

  return (
    <Provider store={store}>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <MockLocalizationProvider>
            <MockSnackbarProvider>
              <MockI18nextProvider>{children}</MockI18nextProvider>
            </MockSnackbarProvider>
          </MockLocalizationProvider>
        </ThemeProvider>
      </BrowserRouter>
    </Provider>
  );
};

// All providers wrapper without router (for use with MemoryRouter)
const AllTheProvidersWithoutRouter = ({
  children,
  initialState = {},
  theme = testTheme
}) => {
  const store = createMockStore(initialState);

  return (
    <Provider store={store}>
      <ThemeProvider theme={theme}>
        <MockLocalizationProvider>
          <MockSnackbarProvider>
            <MockI18nextProvider>{children}</MockI18nextProvider>
          </MockSnackbarProvider>
        </MockLocalizationProvider>
      </ThemeProvider>
    </Provider>
  );
};

// Custom render function
const customRender = (ui, options = {}) => {
  const { initialState, theme, ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <AllTheProviders initialState={initialState} theme={theme}>
      {children}
    </AllTheProviders>
  );

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
};

// Custom render function without router (for use with MemoryRouter)
const customRenderWithoutRouter = (ui, options = {}) => {
  const { initialState, theme, ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <AllTheProvidersWithoutRouter initialState={initialState} theme={theme}>
      {children}
    </AllTheProvidersWithoutRouter>
  );

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
};

// Theme-only wrapper for components that only need theme
export const ThemeWrapper = ({ children, theme = testTheme }) => (
  <ThemeProvider theme={theme}>
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      {children}
    </LocalizationProvider>
  </ThemeProvider>
);

// Custom render for theme-only components
const renderWithTheme = (ui, options = {}) => {
  const { theme, ...renderOptions } = options;

  const Wrapper = ({ children }) => (
    <ThemeWrapper theme={theme}>{children}</ThemeWrapper>
  );

  return rtlRender(ui, { wrapper: Wrapper, ...renderOptions });
};

// Mock user data for authentication tests
export const mockUser = {
  id: '1',
  name: 'Test User',
  email: '<EMAIL>',
  role: 'admin',
  avatar: '/test-avatar.jpg',
  profile: {
    jobtitle: 'Test Developer',
    location: 'Test City'
  }
};

// Mock project data
export const mockProject = {
  id: '1',
  name: 'Test Project',
  description: 'Test project description',
  status: 'active',
  tasks: [],
  files: [],
  cases: []
};

// Mock form data
export const mockFormData = {
  formSelected: {
    name: 'Test Form',
    id: '1'
  },
  formLayout: 'stack',
  formGroups: [
    {
      title: 'Basic Information',
      columns: '2',
      description: 'Basic project information'
    }
  ],
  formFields: [
    {
      name: 'project_name',
      label: 'Project Name',
      type: 'text',
      value: 'Test Project',
      group: 'Basic Information'
    }
  ]
};

// Helper to wait for async operations
export const waitForLoadingToFinish = () => {
  return new Promise((resolve) => {
    setTimeout(resolve, 0);
  });
};

// Helper to create mock API responses
export const createMockApiResponse = (data, status = 200) => ({
  data,
  status,
  statusText: 'OK',
  headers: {},
  config: {}
});

// Helper to create mock error responses
export const createMockApiError = (message = 'API Error', status = 500) => ({
  response: {
    data: { message },
    status,
    statusText: 'Internal Server Error'
  },
  message
});

// Export Jest-based axios mock utilities for easy access in tests
export {
  mockedAxios,
  mockAxiosSuccess,
  mockAxiosError,
  mockGetSuccess,
  mockPostSuccess,
  mockPutSuccess,
  mockDeleteSuccess,
  mockGetError,
  mockPostError,
  mockPutError,
  mockDeleteError,
  resetMocks,
  clearMocks
} from './axios-mock';

// Re-export everything from React Testing Library
export * from '@testing-library/react';
export { userEvent } from '@testing-library/user-event';

// Export custom render as the default render
export { customRender as render, customRenderWithoutRouter as renderWithoutRouter, renderWithTheme };
