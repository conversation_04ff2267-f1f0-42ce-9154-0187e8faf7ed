const puppeteer = require('puppeteer');

(async () => {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();
  const timeout = 5000;
  page.setDefaultTimeout(timeout);

  async function waitForSelectors(selectors, frame, timeout) {
    for (const selector of selectors) {
      try {
        return await waitForSelector(selector, frame, timeout);
      } catch (err) {
        console.error(err);
      }
    }
    throw new Error(
      'Could not find element for selectors: ' + JSON.stringify(selectors)
    );
  }

  async function waitForSelector(selector, frame, timeout) {
    if (selector instanceof Array) {
      let element = null;
      for (const part of selector) {
        if (!element) {
          element = await frame.waitForSelector(part, { timeout });
        } else {
          element = await element.$(part);
        }
        if (!element) {
          throw new Error('Could not find element: ' + part);
        }
        element = (
          await element.evaluateHandle((el) =>
            el.shadowRoot ? el.shadowRoot : el
          )
        ).asElement();
      }
      if (!element) {
        throw new Error('Could not find element: ' + selector.join('|'));
      }
      return element;
    }
    const element = await frame.waitForSelector(selector, { timeout });
    if (!element) {
      throw new Error('Could not find element: ' + selector);
    }
    return element;
  }

  async function waitForElement(step, frame, timeout) {
    const count = step.count || 1;
    const operator = step.operator || '>=';
    const comp = {
      '==': (a, b) => a === b,
      '>=': (a, b) => a >= b,
      '<=': (a, b) => a <= b
    };
    const compFn = comp[operator];
    await waitForFunction(async () => {
      const elements = await querySelectorsAll(step.selectors, frame);
      return compFn(elements.length, count);
    }, timeout);
  }

  async function querySelectorsAll(selectors, frame) {
    for (const selector of selectors) {
      const result = await querySelectorAll(selector, frame);
      if (result.length) {
        return result;
      }
    }
    return [];
  }

  async function querySelectorAll(selector, frame) {
    if (selector instanceof Array) {
      let elements = [];
      let i = 0;
      for (const part of selector) {
        if (i === 0) {
          elements = await frame.$$(part);
        } else {
          const tmpElements = elements;
          elements = [];
          for (const el of tmpElements) {
            elements.push(...(await el.$$(part)));
          }
        }
        if (elements.length === 0) {
          return [];
        }
        const tmpElements = [];
        for (const el of elements) {
          const newEl = (
            await el.evaluateHandle((el) =>
              el.shadowRoot ? el.shadowRoot : el
            )
          ).asElement();
          if (newEl) {
            tmpElements.push(newEl);
          }
        }
        elements = tmpElements;
        i++;
      }
      return elements;
    }
    const element = await frame.$$(selector);
    if (!element) {
      throw new Error('Could not find element: ' + selector);
    }
    return element;
  }

  async function waitForFunction(fn, timeout) {
    let isActive = true;
    setTimeout(() => {
      isActive = false;
    }, timeout);
    while (isActive) {
      const result = await fn();
      if (result) {
        return;
      }
      await new Promise((resolve) => setTimeout(resolve, 100));
    }
    throw new Error('Timed out');
  }
  {
    const targetPage = page;
    await targetPage.setViewport({ width: 2962, height: 1444 });
  }
  {
    const targetPage = page;
    const promises = [];
    promises.push(targetPage.waitForNavigation());
    await targetPage.goto('http://localhost:3000/#/management/projects/list');
    await Promise.all(promises);
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Create new project'],
        [
          '#root > div.MuiBox-root.css-olk31d > div > div.MuiBox-root.css-i9gxme > div > div.MuiPageTitle-wrapper.MuiBox-root.css-od4lca > div > div:nth-child(2) > button'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 63.203125, y: 39.984375 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Event name here...'], ['#mui-58']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 97, y: 36.671875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Event name here...'], ['#mui-58']],
      targetPage,
      timeout
    );
    const type = await element.evaluate((el) => el.type);
    if (
      [
        'textarea',
        'select-one',
        'text',
        'url',
        'tel',
        'search',
        'password',
        'number',
        'email'
      ].includes(type)
    ) {
      await element.type('Test Project');
    } else {
      await element.focus();
      await element.evaluate((el, value) => {
        el.value = value;
        el.dispatchEvent(new Event('input', { bubbles: true }));
        el.dispatchEvent(new Event('change', { bubbles: true }));
      }, 'Test Project');
    }
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(3) > div.MuiFormControl-root.MuiFormControl-fullWidth.MuiTextField-root.css-wb57ya-MuiFormControl-root-MuiTextField-root > div > div > button > svg > path'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 12.5, y: 2.984375 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Feb 18, 2022[role="button"]'],
        [
          'body > div.css-1ozefeb > div.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation8.css-1p38u8g-MuiPaper-root > div > div > div > div.PrivatePickersFadeTransitionGroup-root.MuiCalendarPicker-viewTransitionContainer.css-1wvgxus-MuiCalendarPicker-viewTransitionContainer > div > div.PrivatePickersSlideTransition-root.css-dhopo2 > div > div:nth-child(3) > div:nth-child(6) > button'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 15, y: 16 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(7) > div.MuiFormControl-root.MuiFormControl-fullWidth.MuiTextField-root.css-wb57ya-MuiFormControl-root-MuiTextField-root > div > div > button > svg > path'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 12.5, y: 14.984375 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Feb 21, 2022[role="button"]'],
        [
          'body > div.css-1ozefeb > div.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation8.css-1p38u8g-MuiPaper-root > div > div > div > div.PrivatePickersFadeTransitionGroup-root.MuiCalendarPicker-viewTransitionContainer.css-1wvgxus-MuiCalendarPicker-viewTransitionContainer > div > div.PrivatePickersSlideTransition-root.css-dhopo2 > div > div:nth-child(4) > div:nth-child(2) > button'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 13, y: 27 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Event start'], ['#mui-63']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 99, y: 29.671875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Feb 21, 2022[role="button"]'],
        [
          'body > div.css-1ozefeb > div.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation8.css-1p38u8g-MuiPaper-root > div > div > div:nth-child(1) > div.PrivatePickersSlideTransition-root.css-170k9md > div > div:nth-child(4) > div:nth-child(2) > div > button'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({
      offset: { x: 19.800048828125, y: 22.79998779296875 }
    });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Feb 25, 2022[role="button"]'],
        [
          'body > div.css-1ozefeb > div.MuiPaper-root.MuiPaper-elevation.MuiPaper-rounded.MuiPaper-elevation8.css-1p38u8g-MuiPaper-root > div > div > div:nth-child(1) > div.PrivatePickersSlideTransition-root.css-170k9md > div > div:nth-child(4) > div:nth-child(6) > div > button'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({
      offset: { x: 29.800048828125, y: 20.79998779296875 }
    });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Select booth size...'], ['#mui-70']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 88, y: 19.421875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [["aria/10'x30'"], ['#mui-70-option-2']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 148, y: 29 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Select Shipping Address...'], ['#mui-72']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 147.5, y: 2.421875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'aria/Tim Anonymous 8495 E Phoenix Ave Phoenix AZ 85310 United States'
        ],
        ['#mui-72-option-0']
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 174, y: 30 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(13) > div > div > div.ql-container.ql-snow > div.ql-editor.ql-blank'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 315, y: 47.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(13) > div > div > div.ql-toolbar.ql-snow'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 41, y: 0.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(13) > div > div > div.ql-toolbar.ql-snow > span:nth-child(1) > span > span.ql-picker-label'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 42, y: 12.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [['aria/Heading 1'], ['#ql-picker-options-1 > span:nth-child(1)']],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 59, y: 32.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/  '],
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiGrid-root.MuiGrid-container.MuiGrid-spacing-xs-1.css-m9a1j-MuiGrid-root > div:nth-child(13) > div > div > div.ql-container.ql-snow > div.ql-editor > h1'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 67, y: 23.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiBox-root.css-183p715'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 875, y: 79.296875 } });
  }
  {
    const targetPage = page;
    const element = await waitForSelectors(
      [
        ['aria/Create project'],
        [
          'body > div.MuiModal-root.MuiDialog-root.css-zw3mfo-MuiModal-root-MuiDialog-root > div.MuiDialog-container.MuiDialog-scrollPaper.css-hz1bth-MuiDialog-container > div > form > div > div.MuiBox-root.css-183p715 > div > button.MuiButton-root.MuiButton-contained.MuiButton-containedPrimary.MuiButton-sizeLarge.MuiButton-containedSizeLarge.MuiButtonBase-root.css-coyqh1-MuiButtonBase-root-MuiButton-root'
        ]
      ],
      targetPage,
      timeout
    );
    await element.click({ offset: { x: 127.296875, y: 35.296875 } });
  }

  await browser.close();
})();
