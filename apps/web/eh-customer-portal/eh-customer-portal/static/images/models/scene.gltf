{"accessors": [{"bufferView": 1, "componentType": 5126, "count": 193, "max": [0.5373600125312805, 0.10441000014543533, 0.17463000118732452], "min": [-0.5631600022315979, -0.02744000032544136, -0.17975999414920807], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2316, "componentType": 5126, "count": 193, "max": [0.9990076422691345, 0.8028094172477722, 0.9999943375587463], "min": [-0.9960398077964783, -0.9999366998672485, -0.9999858736991882], "type": "VEC3"}, {"bufferView": 0, "componentType": 5125, "count": 1056, "max": [192], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 4632, "componentType": 5126, "count": 1040, "max": [1.1848900318145752, 0.7594299912452698, -0.28227999806404114], "min": [-1.1848900318145752, 0.5596200227737427, -0.4054099917411804], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 17112, "componentType": 5126, "count": 1040, "max": [0.9007583856582642, 0.8597317337989807, 0.986579418182373], "min": [-0.900759220123291, -0.8498291373252869, 0.43336403369903564], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 4224, "componentType": 5125, "count": 5760, "max": [1039], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 29592, "componentType": 5126, "count": 390, "max": [0.67781001329422, 0.16503000259399414, 0.20762999355793], "min": [-0.6656399965286255, -0.020020000636577606, -0.2292799949645996], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 34272, "componentType": 5126, "count": 390, "max": [0.6476373076438904, -0.47227582335472107, 0.8814508318901062], "min": [-0.5867093205451965, -1, -0.7459840774536133], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 27264, "componentType": 5125, "count": 1872, "max": [389], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 38952, "componentType": 5126, "count": 922, "max": [1.1473100185394287, 0.7241700291633606, -0.3467099964618683], "min": [-1.1473100185394287, 0.591509997844696, -0.40786001086235046], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 50016, "componentType": 5126, "count": 922, "max": [0.5010592341423035, 0.10196137428283691, 0.9682097434997559], "min": [-0.5010592341423035, -0.19510222971439362, 0.8624783754348755], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 34752, "componentType": 5125, "count": 5280, "max": [921], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 61080, "componentType": 5126, "count": 2353, "max": [1.028730034828186, 0.5932599902153015, 0.3111099898815155], "min": [-1.028730034828186, 0.24879999458789825, -0.20196999609470367], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 89316, "componentType": 5126, "count": 2353, "max": [0.9999999403953552, 0.9870073795318604, 1], "min": [-0.9999999403953552, -0.9933498501777649, -0.9578005075454712], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 55872, "componentType": 5125, "count": 12768, "max": [2352], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 117552, "componentType": 5126, "count": 1080, "max": [8.544480323791504, 3.8673601150512695, -0.14172999560832977], "min": [-8.544480323791504, 2.3824400901794434, -0.5675899982452393], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 130512, "componentType": 5126, "count": 1080, "max": [0.9999999403953552, 1, 0.9998289346694946], "min": [-0.9999999403953552, -1, -0.9606181979179382], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 106944, "componentType": 5125, "count": 12960, "max": [1079], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 143472, "componentType": 5126, "count": 768, "max": [0.4517099857330322, 0.032919999212026596, -0.0086899995803833], "min": [-0.4517099857330322, -0.020390000194311142, -0.10687000304460526], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 152688, "componentType": 5126, "count": 768, "max": [0.2427704930305481, 0.841401219367981, 0.7958709597587585], "min": [-0.2427704930305481, -0.8330828547477722, 0.5251722931861877], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 158784, "componentType": 5125, "count": 1152, "max": [767], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 161904, "componentType": 5126, "count": 1399, "max": [0.9573299884796143, 0.7191100120544434, -0.03960999846458435], "min": [-0.9573299884796143, 0.5216699838638306, -0.3245700001716614], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 178692, "componentType": 5126, "count": 1399, "max": [0.9999502301216125, 1, 0.995535135269165], "min": [-0.9999502301216125, -1, 0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 163392, "componentType": 5125, "count": 6552, "max": [1398], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 195480, "componentType": 5126, "count": 11200, "max": [0.9758099913597107, 0.5615500211715698, 0.16362999379634857], "min": [-0.9758099913597107, 0.3383899927139282, -0.20382000505924225], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 329880, "componentType": 5126, "count": 11200, "max": [1, 0.9997615218162537, 0], "min": [-1, -0.9997770190238953, -0.9375424385070801], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 189600, "componentType": 5125, "count": 16800, "max": [11199], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 464280, "componentType": 5126, "count": 390, "max": [0.67781001329422, 0.07334999740123749, 0.20762999355793], "min": [-0.6656399965286255, -0.0010100000072270632, -0.2292799949645996], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 468960, "componentType": 5126, "count": 390, "max": [0.8080489039421082, -0.4264783561229706, 0.9044976830482483], "min": [-0.7609646320343018, -1, -0.8182888627052307], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 256800, "componentType": 5125, "count": 1872, "max": [389], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 473640, "componentType": 5126, "count": 129, "max": [0.5341899991035461, 0.0630899965763092, 0.17454999685287476], "min": [-0.5497199892997742, -0.009119999594986439, -0.17975999414920807], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 475188, "componentType": 5126, "count": 129, "max": [0.5861160755157471, -0.6721879839897156, 0.6933878660202026], "min": [-0.730582058429718, -1, -0.7403698563575745], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 264288, "componentType": 5125, "count": 672, "max": [128], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 476736, "componentType": 5126, "count": 2904, "max": [1.2922199964523315, 3.82135009765625, -0.2025199979543686], "min": [-1.2922199964523315, 3.2002499103546143, -0.3668600022792816], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 511584, "componentType": 5126, "count": 2904, "max": [0.9900808334350586, 0.992766261100769, 0.9999968409538269], "min": [-0.9900808334350586, -0.9985203146934509, -0.9997414350509644], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 266976, "componentType": 5125, "count": 17088, "max": [2903], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 546432, "componentType": 5126, "count": 624, "max": [1, 1, 0.00761000020429492], "min": [-1, -1, 0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 553920, "componentType": 5126, "count": 624, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 335328, "componentType": 5125, "count": 1584, "max": [623], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 561408, "componentType": 5126, "count": 10752, "max": [1.306939959526062, -0.2548699975013733, 0.9378600120544434], "min": [-1.306939959526062, -1.3966000080108643, 0.5583699941635132], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 690432, "componentType": 5126, "count": 10752, "max": [0.9659291505813599, 0.5136900544166565, 0.9954872727394104], "min": [-0.9659305810928345, -0.6563020348548889, -0.9916260242462158], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 341664, "componentType": 5125, "count": 16128, "max": [10751], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 819456, "componentType": 5126, "count": 12100, "max": [1.6033600568771362, 1.1747299432754517, 0.5204600095748901], "min": [-1.6033600568771362, -1.281209945678711, -0.8611900210380554], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 964656, "componentType": 5126, "count": 12100, "max": [0.9999621510505676, 0.9976961612701416, 0.996954619884491], "min": [-0.9999621510505676, -0.9999428391456604, -0.9936135411262512], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 406176, "componentType": 5125, "count": 72576, "max": [12099], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1109856, "componentType": 5126, "count": 8322, "max": [1.2649999856948853, -1.324779987335205, 0.8231300115585327], "min": [-1.2649999856948853, -3.6350300312042236, 0.5724800229072571], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1209720, "componentType": 5126, "count": 8322, "max": [0.9992247819900513, 0.99413001537323, 0.9999964833259583], "min": [-0.9992247819900513, -0.9982531666755676, -0.9999964833259583], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 696480, "componentType": 5125, "count": 49920, "max": [8321], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1309584, "componentType": 5126, "count": 1617, "max": [0.8911200165748596, 3.121920108795166, 0.5046300292015076], "min": [-0.8911200165748596, 1.9879599809646606, -0.05502999946475029], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1328988, "componentType": 5126, "count": 1617, "max": [0.14060381054878235, 0.5902027487754822, 0.9907263517379761], "min": [-0.14060381054878235, 0.13587087392807007, 0.7949537038803101], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 896160, "componentType": 5125, "count": 9216, "max": [1616], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1348392, "componentType": 5126, "count": 15390, "max": [1.3825000524520874, 3.7581698894500732, -1.0227999687194824], "min": [-1.3825000524520874, 2.8543601036071777, -1.3613300323486328], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1533072, "componentType": 5126, "count": 15390, "max": [0.9982728958129883, 0.9999021887779236, 0.9997833371162415], "min": [-0.9982728958129883, -0.9991940855979919, -0.9998433589935303], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 933024, "componentType": 5125, "count": 92352, "max": [15389], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1717752, "componentType": 5126, "count": 8, "max": [3.060580015182495, 1, 0], "min": [-3.060580015182495, -1.2007900476455688, -0.8653299808502197], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1717848, "componentType": 5126, "count": 8, "max": [0.2720693349838257, 0, 0.9622777104377747], "min": [-0.2720693349838257, 0, 0.9622777104377747], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1302432, "componentType": 5125, "count": 12, "max": [7], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1717944, "componentType": 5126, "count": 8, "max": [3.3770198822021484, 1.1308200359344482, -0.02921999990940094], "min": [-3.3770198822021484, -1.4501500129699707, -1.0937999486923218], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1718040, "componentType": 5126, "count": 8, "max": [0.2977985441684723, 0.005059975199401379, 0.9546153545379639], "min": [-0.2977985441684723, 0.005059975199401379, 0.9546153545379639], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1302480, "componentType": 5125, "count": 12, "max": [7], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1718136, "componentType": 5126, "count": 35, "max": [1, 2.0581700801849365, 0.15107999742031097], "min": [-1, -2.1102700233459473, 0.02022000029683113], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1718556, "componentType": 5126, "count": 35, "max": [0.03823009878396988, 0.12273001670837402, -0.9924401044845581], "min": [-0.03823009878396988, -0.01768004707992077, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1302528, "componentType": 5125, "count": 84, "max": [34], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1718976, "componentType": 5126, "count": 96, "max": [1.3930200338363647, 1.0907100439071655, 0.5381100177764893], "min": [-1.3930200338363647, -1.1137399673461914, 0.015890000388026237], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1720128, "componentType": 5126, "count": 96, "max": [0.8842291831970215, 0.03390005975961685, 0.508030891418457], "min": [-0.8842291831970215, 0.006660018116235733, 0.46666955947875977], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1302864, "componentType": 5125, "count": 144, "max": [95], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1721280, "componentType": 5126, "count": 8, "max": [4.833769798278809, 1.7747999429702759, 1.7000800371170044], "min": [-4.833769798278809, -1.542449951171875, -0.0004199999966658652], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1721376, "componentType": 5126, "count": 8, "max": [0.8471218943595886, 0.06190014258027077, 0.5277811884880066], "min": [-0.8471218943595886, 0.06190014258027077, 0.5277811884880066], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1303440, "componentType": 5125, "count": 12, "max": [7], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1721472, "componentType": 5126, "count": 290, "max": [1.346060037612915, 1.1816400289535522, 0.6469500064849854], "min": [-1.346060037612915, 1.0745899677276611, 0.1785299926996231], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1724952, "componentType": 5126, "count": 290, "max": [0.8694028258323669, 0.4875466227531433, 0.5139442086219788], "min": [-0.8694028258323669, -0.1980324536561966, 0.46654951572418213], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1303488, "componentType": 5125, "count": 1344, "max": [289], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1728432, "componentType": 5126, "count": 7748, "max": [0.00471000000834465, 1.5237799882888794, 0.06659000366926193], "min": [-3.1648900508880615, -0.06659000366926193, -4.550769805908203], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 1821408, "componentType": 5126, "count": 7748, "max": [1, 0.9904904961585999, 0.9904822707176208], "min": [-1, -0.9904863834381104, -0.9904822707176208], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1308864, "componentType": 5125, "count": 45696, "max": [7747], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 1914384, "componentType": 5126, "count": 65532, "max": [2.9999999242136255e-05, 1.9247599840164185, 0.4857499897480011], "min": [-3.221450090408325, -0.4849399924278259, -4.967860221862793], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 2700768, "componentType": 5126, "count": 65532, "max": [1, 0.9999955296516418, 0.9999929070472717], "min": [-1, -0.9999961256980896, -0.999993622303009], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 1491648, "componentType": 5125, "count": 380112, "max": [65531], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 3487152, "componentType": 5126, "count": 36603, "max": [2.9999999242136255e-05, 1.9381200075149536, -4.003489971160889], "min": [-3.221450090408325, 0.9707199931144714, -4.972060203552246], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 3926388, "componentType": 5126, "count": 36603, "max": [1, 0.9999955296516418, 0.999993622303009], "min": [-1, -0.9999380111694336, -0.999993622303009], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 3012096, "componentType": 5125, "count": 208368, "max": [36602], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 4365624, "componentType": 5126, "count": 13312, "max": [0.1815599948167801, 5.295539855957031, 0.5804700255393982], "min": [-2.9737699031829834, -0.5804700255393982, -0.5804700255393982], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4525368, "componentType": 5126, "count": 13312, "max": [0.9997336864471436, 0.6689062118530273, 0.6689062118530273], "min": [-0.9997336864471436, -0.668921172618866, -0.668921172618866], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 3845568, "componentType": 5125, "count": 73728, "max": [13311], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 4685112, "componentType": 5126, "count": 5376, "max": [0.18057000637054443, 5.3060197830200195, 0.590939998626709], "min": [-2.9582200050354004, -0.590939998626709, -0.590939998626709], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 4749624, "componentType": 5126, "count": 5376, "max": [0.998856782913208, 1, 1], "min": [-0.998856782913208, -1, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 4140480, "componentType": 5125, "count": 30720, "max": [5375], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 4814136, "componentType": 5126, "count": 65532, "max": [2.7120299339294434, 9.484760284423828, 9.484729766845703], "min": [-46.86178970336914, -9.484800338745117, -84.4290771484375], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 5600520, "componentType": 5126, "count": 65532, "max": [1, 0.9999939799308777, 1], "min": [-1, -0.9999938607215881, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 4263360, "componentType": 5125, "count": 144966, "max": [65531], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 6386904, "componentType": 5126, "count": 46022, "max": [2.7120299339294434, 9.484689712524414, -65.45966339111328], "min": [-46.86178970336914, -9.484800338745117, -84.4290771484375], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 6939168, "componentType": 5126, "count": 46022, "max": [1, 0.9999938607215881, 1], "min": [-1, -0.9999938607215881, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 4843224, "componentType": 5125, "count": 94074, "max": [46021], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 7491432, "componentType": 5126, "count": 2760, "max": [1.0000100135803223, 1, 9.077930450439453], "min": [-1, -15.312769889831543, 0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 7524552, "componentType": 5126, "count": 2760, "max": [0.9951844215393066, 0.9951854944229126, 1], "min": [-0.9951844215393066, -0.9951854944229126, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5219520, "componentType": 5125, "count": 6504, "max": [2759], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 7557672, "componentType": 5126, "count": 794, "max": [1.0302499532699585, 1.0302400588989258, 9.083990097045898], "min": [-1.0302400588989258, -15.343009948730469, -0.006070000119507313], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 7567200, "componentType": 5126, "count": 794, "max": [0.9951844215393066, 0.9951844215393066, 1], "min": [-0.9951844215393066, -0.9951844215393066, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5245536, "componentType": 5125, "count": 1536, "max": [793], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 7576728, "componentType": 5126, "count": 1310, "max": [0.519760012626648, 0.5197499990463257, 9.084190368652344], "min": [-0.5197499990463257, -14.83251953125, 0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 7592448, "componentType": 5126, "count": 1310, "max": [0.9951844215393066, 0.9951844215393066, 1], "min": [-0.9951844215393066, -0.9951844215393066, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5251680, "componentType": 5125, "count": 2304, "max": [1309], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 7608168, "componentType": 5126, "count": 65532, "max": [0.6395400166511536, 1, 1.3159500360488892], "min": [-28.1146297454834, -1, 0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 8394552, "componentType": 5126, "count": 65532, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 5260896, "componentType": 5125, "count": 384618, "max": [65531], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 9180936, "componentType": 5126, "count": 65532, "max": [0.6395400166511536, 1, 29.019699096679688], "min": [-28.1146297454834, -1.0000100135803223, 0], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 9967320, "componentType": 5126, "count": 65532, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 6799368, "componentType": 5125, "count": 380415, "max": [65531], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 10753704, "componentType": 5126, "count": 34670, "max": [-26.835540771484375, 0.9999899864196777, 29.019699096679688], "min": [-28.114620208740234, -1.0000100135803223, 27.703750610351562], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 11169744, "componentType": 5126, "count": 34670, "max": [1, 1, 1], "min": [-1, -1, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 8321028, "componentType": 5125, "count": 202263, "max": [34669], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 11585784, "componentType": 5126, "count": 1029, "max": [8.359990119934082, 4.208169937133789, 1.7160300016403198], "min": [7.022679805755615, 2.87076997756958, 1.6171300411224365], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 11598132, "componentType": 5126, "count": 1029, "max": [0.21531331539154053, 0.22752168774604797, 1], "min": [-0.24763354659080505, -0.23438389599323273, 0.9687501788139343], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 9130080, "componentType": 5125, "count": 5952, "max": [1028], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 11610480, "componentType": 5126, "count": 5156, "max": [1.490190029144287, 1.0329300165176392, 0.08106999844312668], "min": [-1.490190029144287, 0.7562699913978577, -0.006519999820739031], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 11672352, "componentType": 5126, "count": 5156, "max": [0.9997285604476929, 0.9996874332427979, 0.997005820274353], "min": [-0.9997285604476929, -0.9998345375061035, -0.9969891905784607], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 9153888, "componentType": 5125, "count": 30912, "max": [5155], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 11734224, "componentType": 5126, "count": 65532, "max": [1.632699966430664, 3.8919999599456787, 0.9530799984931946], "min": [-1.5595500469207764, -4.10269021987915, -1.306980013847351], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 12520608, "componentType": 5126, "count": 65532, "max": [0.9999980330467224, 0.9999998211860657, 1], "min": [-0.9999977946281433, -0.999999463558197, -0.9999976754188538], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 9277536, "componentType": 5125, "count": 380643, "max": [65531], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 13306992, "componentType": 5126, "count": 64725, "max": [1.624500036239624, 3.888780117034912, 0.9515500068664551], "min": [-1.632699966430664, -4.10269021987915, -1.306980013847351], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 14083692, "componentType": 5126, "count": 64725, "max": [0.9999977946281433, 0.9999191761016846, 1], "min": [-0.9999979138374329, -0.999999463558197, -1], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 10800108, "componentType": 5125, "count": 370461, "max": [64724], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 14860392, "componentType": 5126, "count": 458, "max": [1.3348599672317505, 0.6315500140190125, 0.0019199999514967203], "min": [-1.3348599672317505, 0.3136500120162964, -0.19461999833583832], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 14865888, "componentType": 5126, "count": 458, "max": [0.9999036192893982, 0.9974513053894043, 0.9977152347564697], "min": [-0.9999036192893982, -0.44909268617630005, 0], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12281952, "componentType": 5125, "count": 2532, "max": [457], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 14871384, "componentType": 5126, "count": 638, "max": [1.2832200527191162, 0.17644000053405762, 0.6235100030899048], "min": [-1.2832200527191162, 0.07959999889135361, 0.3664099872112274], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 14879040, "componentType": 5126, "count": 638, "max": [1, 3.0000732294865884e-05, 0.999998152256012], "min": [-1, -0.9994916915893555, -0.6320248246192932], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12292080, "componentType": 5125, "count": 3360, "max": [637], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 14886696, "componentType": 5126, "count": 3392, "max": [1.067330002784729, 3.5205299854278564, -1.1419700384140015], "min": [-1.067330002784729, 3.3267099857330322, -1.295639991760254], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 14927400, "componentType": 5126, "count": 3392, "max": [0.9982772469520569, 0.9997431039810181, 0.9903823137283325], "min": [-0.9982776045799255, -0.9996451139450073, -0.99614018201828], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12305520, "componentType": 5125, "count": 19968, "max": [3391], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 14968104, "componentType": 5126, "count": 6912, "max": [1.2300000190734863, 3.760730028152466, -0.33052998781204224], "min": [-1.2300000190734863, 3.4851999282836914, -0.5396299958229065], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 15051048, "componentType": 5126, "count": 6912, "max": [0.9999151229858398, 0.999508261680603, 0.9996902942657471], "min": [-0.9999151229858398, -0.9996131062507629, -0.9992819428443909], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12385392, "componentType": 5125, "count": 41472, "max": [6911], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 15133992, "componentType": 5126, "count": 39010, "max": [1.257200002670288, 3.746119976043701, -0.29047998785972595], "min": [-1.257200002670288, 3.482059955596924, -0.5808600187301636], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 15602112, "componentType": 5126, "count": 39010, "max": [0.43238985538482666, 0.9860261678695679, 0.19672591984272003], "min": [-0.43238985538482666, 0.9009883999824524, -0.20527225732803345], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12551280, "componentType": 5125, "count": 104448, "max": [39009], "min": [0], "type": "SCALAR"}, {"bufferView": 1, "byteOffset": 16070232, "componentType": 5126, "count": 4014, "max": [1.7523000240325928, -0.6523299813270569, 0.6044300198554993], "min": [-1.7523000240325928, -0.8350099921226501, 0.44633999466896057], "type": "VEC3"}, {"bufferView": 1, "byteOffset": 16118400, "componentType": 5126, "count": 4014, "max": [0.9997909665107727, 0.9955867528915405, 0.9953999519348145], "min": [-0.9997913241386414, -0.9965583682060242, -0.999798595905304], "type": "VEC3"}, {"bufferView": 0, "byteOffset": 12969072, "componentType": 5125, "count": 23808, "max": [4013], "min": [0], "type": "SCALAR"}], "asset": {"extras": {"author": "afdave14 (https://sketchfab.com/afdave14)", "license": "CC-BY-4.0 (http://creativecommons.org/licenses/by/4.0/)", "source": "https://sketchfab.com/models/86d5ad6f76ee4d2d9c0ca3778ea35d83", "title": "Chevy Camaro"}, "generator": "Sketchfab-3.18.7", "version": "2.0"}, "bufferViews": [{"buffer": 0, "byteLength": 13064304, "byteOffset": 0, "name": "floatBufferViews", "target": 34963}, {"buffer": 0, "byteLength": 16166568, "byteOffset": 13064304, "byteStride": 12, "name": "floatBufferViews", "target": 34962}], "buffers": [{"byteLength": 29230872, "uri": "scene.bin"}], "materials": [{"doubleSided": true, "emissiveFactor": [0, 0, 0], "name": "Scene_-_Root", "pbrMetallicRoughness": {"baseColorFactor": [1, 1, 1, 1], "metallicFactor": 0, "roughnessFactor": 0.2}}], "meshes": [{"name": "emblem_front", "primitives": [{"attributes": {"NORMAL": 1, "POSITION": 0}, "indices": 2, "material": 0, "mode": 4}]}, {"name": "headlight_plastic_cover", "primitives": [{"attributes": {"NORMAL": 4, "POSITION": 3}, "indices": 5, "material": 0, "mode": 4}]}, {"name": "emblem_border_front", "primitives": [{"attributes": {"NORMAL": 7, "POSITION": 6}, "indices": 8, "material": 0, "mode": 4}]}, {"name": "front_light", "primitives": [{"attributes": {"NORMAL": 10, "POSITION": 9}, "indices": 11, "material": 0, "mode": 4}]}, {"name": "grill", "primitives": [{"attributes": {"NORMAL": 13, "POSITION": 12}, "indices": 14, "material": 0, "mode": 4}]}, {"name": "front_light_border.001", "primitives": [{"attributes": {"NORMAL": 16, "POSITION": 15}, "indices": 17, "material": 0, "mode": 4}]}, {"name": "air_intake", "primitives": [{"attributes": {"NORMAL": 19, "POSITION": 18}, "indices": 20, "material": 0, "mode": 4}]}, {"name": "grill_small", "primitives": [{"attributes": {"NORMAL": 22, "POSITION": 21}, "indices": 23, "material": 0, "mode": 4}]}, {"name": "radiator", "primitives": [{"attributes": {"NORMAL": 25, "POSITION": 24}, "indices": 26, "material": 0, "mode": 4}]}, {"name": "emblem_border_rear", "primitives": [{"attributes": {"NORMAL": 28, "POSITION": 27}, "indices": 29, "material": 0, "mode": 4}]}, {"name": "emblem_rear", "primitives": [{"attributes": {"NORMAL": 31, "POSITION": 30}, "indices": 32, "material": 0, "mode": 4}]}, {"name": "body_spoiler", "primitives": [{"attributes": {"NORMAL": 34, "POSITION": 33}, "indices": 35, "material": 0, "mode": 4}]}, {"name": "license_plate", "primitives": [{"attributes": {"NORMAL": 37, "POSITION": 36}, "indices": 38, "material": 0, "mode": 4}]}, {"name": "glass_front", "primitives": [{"attributes": {"NORMAL": 40, "POSITION": 39}, "indices": 41, "material": 0, "mode": 4}]}, {"name": "body_door", "primitives": [{"attributes": {"NORMAL": 43, "POSITION": 42}, "indices": 44, "material": 0, "mode": 4}]}, {"name": "body_hood", "primitives": [{"attributes": {"NORMAL": 46, "POSITION": 45}, "indices": 47, "material": 0, "mode": 4}]}, {"name": "glass_rear", "primitives": [{"attributes": {"NORMAL": 49, "POSITION": 48}, "indices": 50, "material": 0, "mode": 4}]}, {"name": "rear_mud_guard", "primitives": [{"attributes": {"NORMAL": 52, "POSITION": 51}, "indices": 53, "material": 0, "mode": 4}]}, {"name": "grill_small-back", "primitives": [{"attributes": {"NORMAL": 55, "POSITION": 54}, "indices": 56, "material": 0, "mode": 4}]}, {"name": "grill_big-back", "primitives": [{"attributes": {"NORMAL": 58, "POSITION": 57}, "indices": 59, "material": 0, "mode": 4}]}, {"name": "bottom", "primitives": [{"attributes": {"NORMAL": 61, "POSITION": 60}, "indices": 62, "material": 0, "mode": 4}]}, {"name": "glass_side", "primitives": [{"attributes": {"NORMAL": 64, "POSITION": 63}, "indices": 65, "material": 0, "mode": 4}]}, {"name": "glass_side-back", "primitives": [{"attributes": {"NORMAL": 67, "POSITION": 66}, "indices": 68, "material": 0, "mode": 4}]}, {"name": "rubber_trim", "primitives": [{"attributes": {"NORMAL": 70, "POSITION": 69}, "indices": 71, "material": 0, "mode": 4}]}, {"name": "wheel_rim_cover", "primitives": [{"attributes": {"NORMAL": 73, "POSITION": 72}, "indices": 74, "material": 0, "mode": 4}]}, {"name": "wheel_rim", "primitives": [{"attributes": {"NORMAL": 76, "POSITION": 75}, "indices": 77, "material": 0, "mode": 4}]}, {"name": "wheel_rim", "primitives": [{"attributes": {"NORMAL": 79, "POSITION": 78}, "indices": 80, "material": 0, "mode": 4}]}, {"name": "tire_inner", "primitives": [{"attributes": {"NORMAL": 82, "POSITION": 81}, "indices": 83, "material": 0, "mode": 4}]}, {"name": "tire", "primitives": [{"attributes": {"NORMAL": 85, "POSITION": 84}, "indices": 86, "material": 0, "mode": 4}]}, {"name": "tire_treads", "primitives": [{"attributes": {"NORMAL": 88, "POSITION": 87}, "indices": 89, "material": 0, "mode": 4}]}, {"name": "tire_treads", "primitives": [{"attributes": {"NORMAL": 91, "POSITION": 90}, "indices": 92, "material": 0, "mode": 4}]}, {"name": "wheel_rotor", "primitives": [{"attributes": {"NORMAL": 94, "POSITION": 93}, "indices": 95, "material": 0, "mode": 4}]}, {"name": "wheel_rotor_rim.001", "primitives": [{"attributes": {"NORMAL": 97, "POSITION": 96}, "indices": 98, "material": 0, "mode": 4}]}, {"name": "wheel_rotor_rim", "primitives": [{"attributes": {"NORMAL": 100, "POSITION": 99}, "indices": 101, "material": 0, "mode": 4}]}, {"name": "brakes", "primitives": [{"attributes": {"NORMAL": 103, "POSITION": 102}, "indices": 104, "material": 0, "mode": 4}]}, {"name": "brakes", "primitives": [{"attributes": {"NORMAL": 106, "POSITION": 105}, "indices": 107, "material": 0, "mode": 4}]}, {"name": "brakes", "primitives": [{"attributes": {"NORMAL": 109, "POSITION": 108}, "indices": 110, "material": 0, "mode": 4}]}, {"name": "front_light_dome", "primitives": [{"attributes": {"NORMAL": 112, "POSITION": 111}, "indices": 113, "material": 0, "mode": 4}]}, {"name": "body_handle", "primitives": [{"attributes": {"NORMAL": 115, "POSITION": 114}, "indices": 116, "material": 0, "mode": 4}]}, {"name": "body", "primitives": [{"attributes": {"NORMAL": 118, "POSITION": 117}, "indices": 119, "material": 0, "mode": 4}]}, {"name": "body", "primitives": [{"attributes": {"NORMAL": 121, "POSITION": 120}, "indices": 122, "material": 0, "mode": 4}]}, {"name": "light_front_back_piece", "primitives": [{"attributes": {"NORMAL": 124, "POSITION": 123}, "indices": 125, "material": 0, "mode": 4}]}, {"name": "front_light_border", "primitives": [{"attributes": {"NORMAL": 127, "POSITION": 126}, "indices": 128, "material": 0, "mode": 4}]}, {"name": "rear_mud_guard.001", "primitives": [{"attributes": {"NORMAL": 130, "POSITION": 129}, "indices": 131, "material": 0, "mode": 4}]}, {"name": "headlights_trim", "primitives": [{"attributes": {"NORMAL": 133, "POSITION": 132}, "indices": 134, "material": 0, "mode": 4}]}, {"name": "headlights", "primitives": [{"attributes": {"NORMAL": 136, "POSITION": 135}, "indices": 137, "material": 0, "mode": 4}]}, {"name": "body_side_view_mirrors", "primitives": [{"attributes": {"NORMAL": 139, "POSITION": 138}, "indices": 140, "material": 0, "mode": 4}]}], "nodes": [{"children": [1], "name": "RootNode (gltf orientation matrix)", "rotation": [-0.7071067811865475, 0, 0, 0.7071067811865476]}, {"children": [2], "matrix": [0, 1, 0, 0, -1, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1], "name": "RootNode (model correction matrix)"}, {"children": [3, 5, 7, 9, 11, 13, 15, 17, 19, 21, 23, 25, 27, 29, 31, 33, 35, 37, 39, 41, 43, 45, 47, 49, 51, 74, 76, 78, 81, 83, 85, 87, 89, 91], "name": "Root"}, {"children": [4], "matrix": [0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, -0.00425, -7.9311300000000005, 1.07436, 1], "name": "emblem_front"}, {"mesh": 0, "name": "emblem_front"}, {"children": [6], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, -7.9874, 0.004280000000000001, 1], "name": "headlight_plastic_cover"}, {"mesh": 1, "name": "headlight_plastic_cover"}, {"children": [8], "matrix": [0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, -0.007810000000000002, -7.928179999999999, 1.07464, 1], "name": "emblem_border_front"}, {"mesh": 2, "name": "emblem_border_front"}, {"children": [10], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, -7.9874, 0.004280000000000001, 1], "name": "front_light"}, {"mesh": 3, "name": "front_light"}, {"children": [12], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, -0.0006900000000000002, -7.61846, 0.66846, 1], "name": "grill"}, {"mesh": 4, "name": "grill"}, {"children": [14], "matrix": [0.15141000000000004, 0, 0, 0, 0, 0, 0.15141000000000004, 0, 0, -0.5214799999999999, 0, 0, -0.0006900000000000002, -7.61846, 0.66846, 1], "name": "front_light_border.001"}, {"mesh": 5, "name": "front_light_border.001"}, {"children": [16], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, -7.72346, 1.30541, 1], "name": "air_intake"}, {"mesh": 6, "name": "air_intake"}, {"children": [18], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, -7.9874, 0.004280000000000001, 1], "name": "grill_small"}, {"mesh": 7, "name": "grill_small"}, {"children": [20], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, 0, -7.61846, 0.6587800000000001, 1], "name": "radiator"}, {"mesh": 8, "name": "radiator"}, {"children": [22], "matrix": [-0.24819000000000002, 0, 0, 0, 0, -0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, 0.012210000000000002, -0.06138000000000001, 1.42031, 1], "name": "emblem_border_rear"}, {"mesh": 9, "name": "emblem_border_rear"}, {"children": [24], "matrix": [-0.24819000000000002, 0, 0, 0, 0, -0.24819000000000002, 0, 0, 0, 0, 0.24819000000000002, 0, 0.008640000000000002, -0.058440000000000006, 1.42003, 1], "name": "emblem_rear"}, {"mesh": 10, "name": "emblem_rear"}, {"children": [26], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.32541, 1], "name": "body_spoiler"}, {"mesh": 11, "name": "body_spoiler"}, {"children": [28], "matrix": [0.25772, 0, 0, 0, 0, 0, 0.12511, 0, 0, -1, 0, 0, 0, -0.10327000000000001, 1.06621, 1], "name": "license_plate"}, {"mesh": 12, "name": "license_plate"}, {"children": [30], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "glass_front"}, {"mesh": 13, "name": "glass_front"}, {"children": [32], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "body_door"}, {"mesh": 14, "name": "body_door"}, {"children": [34], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "body_hood"}, {"mesh": 15, "name": "body_hood"}, {"children": [36], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "glass_rear"}, {"mesh": 16, "name": "glass_rear"}, {"children": [38], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "rear_mud_guard"}, {"mesh": 17, "name": "rear_mud_guard"}, {"children": [40], "matrix": [0.**********0000006, 0, 0, 0, 0, -0.010820000000000001, 0.11710000000000002, 0, 0, -0.29405000000000003, -0.027170000000000007, 0, 0, -7.9271199999999995, 0.6288800000000001, 1], "name": "grill_small-back"}, {"mesh": 18, "name": "grill_small-back"}, {"children": [42], "matrix": [0.**********0000006, 0, 0, 0, 0, -0.010820000000000001, 0.11710000000000002, 0, 0, -0.29405000000000003, -0.027170000000000007, 0, 0, -7.72859, 1.1234199999999999, 1], "name": "grill_big-back"}, {"mesh": 19, "name": "grill_big-back"}, {"children": [44], "matrix": [1.4296399999999998, 0, 0, 0, 0, 1.64933, 0, 0, 0, 0, 1, 0, 0, -4.14119, 0.35378000000000004, 1], "name": "bottom"}, {"mesh": 20, "name": "bottom"}, {"children": [46], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, -3.93749, 1.5348199999999999, 1], "name": "glass_side"}, {"mesh": 21, "name": "glass_side"}, {"children": [48], "matrix": [0.28123, 0, 0, 0, 0, 0.28123, 0, 0, 0, 0, 0.28123, 0, 0, -2.55173, 1.58177, 1], "name": "glass_side-back"}, {"mesh": 22, "name": "glass_side-back"}, {"children": [50], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "rubber_trim"}, {"mesh": 23, "name": "rubber_trim"}, {"children": [52, 54, 57, 59, 64, 70], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 1.61071, -6.476230000000001, 0.5870700000000001, 1], "name": "wheel_empty"}, {"children": [53], "matrix": [1, 0, 0, 0, 0, 0.30906000000000006, 0.9510400000000001, 0, 0, -0.9510400000000001, 0.30906000000000006, 0, -0.030620000000000005, 0, 0.004280000000000001, 1], "name": "wheel_rim_cover"}, {"mesh": 24, "name": "wheel_rim_cover"}, {"children": [55, 56], "matrix": [1, 0, 0, 0, 0, 0.30906000000000006, 0.9510400000000001, 0, 0, -0.9510400000000001, 0.30906000000000006, 0, 0, 0, 0.004280000000000001, 1], "name": "wheel_rim"}, {"mesh": 25, "name": "wheel_rim"}, {"mesh": 26, "name": "wheel_rim"}, {"children": [58], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.21461000000000002, 0, 0, 1], "name": "tire_inner"}, {"mesh": 27, "name": "tire_inner"}, {"children": [60, 61], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.22188000000000002, 0, 0, 1], "name": "tire"}, {"mesh": 28, "name": "tire"}, {"children": [62, 63], "matrix": [0.06291000000000001, 0, 0, 0, 0, 0, 0.06291000000000001, 0, 0, -0.06291000000000001, 0, 0, 0, 0, 0, 1], "name": "tire_treads"}, {"mesh": 29, "name": "tire_treads"}, {"mesh": 30, "name": "tire_treads"}, {"children": [65, 66, 68], "matrix": [0, 0, 0.32943000000000006, 0, 0, -0.32943000000000006, 0, 0, -0.32943000000000006, 0, 0, 0, -0.11543000000000002, 0, 0, 1], "name": "wheel_rotor"}, {"mesh": 31, "name": "wheel_rotor"}, {"children": [67], "name": "wheel_rotor_rim.001"}, {"mesh": 32, "name": "wheel_rotor_rim.001"}, {"children": [69], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, -0.003130000000000001, 1], "name": "wheel_rotor_rim"}, {"mesh": 33, "name": "wheel_rotor_rim"}, {"children": [71, 72, 73], "matrix": [0.1073, 0, 0, 0, 0, 0, -0.29125, 0, 0, 0.15004000000000003, 0, 0, -0.13663, 0.18047000000000002, 0, 1], "name": "brakes"}, {"mesh": 34, "name": "brakes"}, {"mesh": 35, "name": "brakes"}, {"mesh": 36, "name": "brakes"}, {"children": [75], "matrix": [0.13978000000000002, 0.043480000000000005, -0.0027500000000000007, 0, 0, 0.009230000000000004, 0.14612, 0, 0.04356000000000001, -0.1395, 0.008810000000000002, 0, -0.0006900000000000002, -7.61846, 0.66846, 1], "name": "front_light_dome"}, {"mesh": 37, "name": "front_light_dome"}, {"children": [77], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "body_handle"}, {"mesh": 38, "name": "body_handle"}, {"children": [79, 80], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "body"}, {"mesh": 39, "name": "body"}, {"mesh": 40, "name": "body"}, {"children": [82], "matrix": [1, 0, 0, 0, 0, 0, 1, 0, 0, -1, 0, 0, -0.0006900000000000002, -7.56577, 0.66846, 1], "name": "light_front_back_piece"}, {"mesh": 41, "name": "light_front_back_piece"}, {"children": [84], "matrix": [1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, -0.0006900000000000002, -7.61846, 0.66846, 1], "name": "front_light_border"}, {"mesh": 42, "name": "front_light_border"}, {"children": [86], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.92512, 1.2239299999999997, 1], "name": "rear_mud_guard.001"}, {"mesh": 43, "name": "rear_mud_guard.001"}, {"children": [88], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "headlights_trim"}, {"mesh": 44, "name": "headlights_trim"}, {"children": [90], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "headlights"}, {"mesh": 45, "name": "headlights"}, {"children": [92], "matrix": [1, 0, 0, 0, 0, 0.98454, 0.17517, 0, 0, -0.17517, 0.98454, 0, 0, -3.95111, 1.2239299999999997, 1], "name": "body_side_view_mirrors"}, {"mesh": 46, "name": "body_side_view_mirrors"}], "scene": 0, "scenes": [{"name": "OSG_Scene", "nodes": [0]}]}