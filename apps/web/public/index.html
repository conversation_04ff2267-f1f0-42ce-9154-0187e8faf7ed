<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="shortcut icon" href="%PUBLIC_URL%/favicon.ico" />
    <link rel="apple-touch-icon" sizes="72x72" href="%PUBLIC_URL%/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="%PUBLIC_URL%/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="%PUBLIC_URL%/favicon-16x16.png">
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, project-scalable=0, shrink-to-fit=no"
    />
    <meta name="theme-color" content="#ffffff">
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:ital,wght@0,400&display=swap"
      rel="stylesheet"
    />
    <link rel="mask-icon" href="%PUBLIC_URL%/safari-pinned-tab.svg" color="#87b9c5">
    <meta name="msapplication-TileColor" content="#4887a8">
    <link href="https://fonts.googleapis.com/css?family=Montserrat:100,200,300" rel="stylesheet">
    <title>ConventionSuite For Exhibit Houses</title>
    <meta
      name="description"
      content="Exhibit Houses ordering portal crafted by ConventionSuite"
    />
    <meta
      name="keywords"
      content="material-ui, admin dashboard, admin template, react, react template, react admin template, react dashboard, react admin panel"
    />
  </head>

  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script src="%PUBLIC_URL%/generated-window-env.js"></script>
  </body>
</html>
