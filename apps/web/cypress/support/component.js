/**
 * Cypress component testing support file
 * Loads custom commands and configuration for component tests
 */

// Import commands.js using ES2015 syntax:
import './commands';

// Import @testing-library/cypress commands
import '@testing-library/cypress/add-commands';

// Import code coverage support
import '@cypress/code-coverage/support';

// Import component testing support
import { mount } from 'cypress/react18';

// Import global styles
import '../../src/styles/global.css';

// Augment the Cypress namespace to include type definitions for
// your custom command.
// Alternatively, can be defined in cypress/support/component.d.ts
// with a <reference path="./component" /> at the top of your spec.
declare global {
  namespace Cypress {
    interface Chainable {
      mount: typeof mount;
    }
  }
}

Cypress.Commands.add('mount', mount);

// Example use:
// cy.mount(<MyComponent />)
