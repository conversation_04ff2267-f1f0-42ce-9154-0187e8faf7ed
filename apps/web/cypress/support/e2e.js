/**
 * Cypress E2E support file
 * Loads custom commands and global configuration for E2E tests
 */

// Import commands.js using ES2015 syntax:
import './commands';

// Import @testing-library/cypress commands
import '@testing-library/cypress/add-commands';

// Import code coverage support
import '@cypress/code-coverage/support';

// Global configuration
Cypress.on('uncaught:exception', (err, runnable) => {
  // Prevent Cypress from failing on uncaught exceptions
  // that might occur in the application
  if (err.message.includes('ResizeObserver loop limit exceeded')) {
    return false;
  }
  if (err.message.includes('Non-Error promise rejection captured')) {
    return false;
  }
  // Let other errors fail the test
  return true;
});

// Custom commands for authentication
Cypress.Commands.add('login', (email = '<EMAIL>', password = 'TokyoPass1@') => {
  cy.session([email, password], () => {
    cy.visit('/');
    cy.get('[data-testid="email-input"]').type(email);
    cy.get('[data-testid="password-input"]').type(password);
    cy.get('[data-testid="login-button"]').click();
    cy.url().should('include', '/dashboard');
  });
});

Cypress.Commands.add('logout', () => {
  cy.get('[data-testid="user-menu"]').click();
  cy.get('[data-testid="logout-button"]').click();
  cy.url().should('not.include', '/dashboard');
});

// Custom commands for project management
Cypress.Commands.add('createProject', (projectData) => {
  const defaultData = {
    name: 'Test Project',
    description: 'Test project description',
    type: 'exhibition',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
  };
  
  const data = { ...defaultData, ...projectData };
  
  cy.visit('/projects');
  cy.get('[data-testid="create-project-button"]').click();
  cy.get('[data-testid="project-name-input"]').type(data.name);
  cy.get('[data-testid="project-description-input"]').type(data.description);
  
  if (data.type) {
    cy.get('[data-testid="project-type-select"]').select(data.type);
  }
  
  if (data.startDate) {
    cy.get('[data-testid="start-date-input"]').type(data.startDate);
  }
  
  if (data.endDate) {
    cy.get('[data-testid="end-date-input"]').type(data.endDate);
  }
  
  cy.get('[data-testid="create-project-submit"]').click();
  cy.contains('Project created successfully').should('be.visible');
});

// Custom commands for form interactions
Cypress.Commands.add('fillForm', (formData) => {
  Object.keys(formData).forEach((fieldName) => {
    const value = formData[fieldName];
    const selector = `[data-testid="${fieldName}-input"]`;
    
    cy.get(selector).then(($el) => {
      const tagName = $el.prop('tagName').toLowerCase();
      const type = $el.attr('type');
      
      if (tagName === 'select') {
        cy.get(selector).select(value);
      } else if (type === 'checkbox') {
        if (value) {
          cy.get(selector).check();
        } else {
          cy.get(selector).uncheck();
        }
      } else if (type === 'radio') {
        cy.get(`${selector}[value="${value}"]`).check();
      } else {
        cy.get(selector).clear().type(value);
      }
    });
  });
});

// Custom commands for waiting
Cypress.Commands.add('waitForLoadingToFinish', () => {
  cy.get('[data-testid="loading-spinner"]', { timeout: 10000 }).should('not.exist');
  cy.get('[data-testid="loading-skeleton"]', { timeout: 10000 }).should('not.exist');
});

// Custom commands for API interactions
Cypress.Commands.add('mockApiResponse', (method, url, response, statusCode = 200) => {
  cy.intercept(method, url, {
    statusCode,
    body: response,
  }).as('apiCall');
});

Cypress.Commands.add('mockApiError', (method, url, statusCode = 500, message = 'Server Error') => {
  cy.intercept(method, url, {
    statusCode,
    body: { message },
  }).as('apiError');
});

// Custom commands for accessibility testing
Cypress.Commands.add('checkA11y', (context = null, options = {}) => {
  cy.injectAxe();
  cy.checkA11y(context, options);
});

// Custom commands for file uploads
Cypress.Commands.add('uploadFile', (selector, fileName, fileType = 'application/pdf') => {
  cy.get(selector).selectFile({
    contents: Cypress.Buffer.from('Test file content'),
    fileName,
    mimeType: fileType,
  });
});

// Custom commands for drag and drop
Cypress.Commands.add('dragAndDrop', (sourceSelector, targetSelector) => {
  cy.get(sourceSelector).trigger('mousedown', { button: 0 });
  cy.get(targetSelector).trigger('mousemove').trigger('mouseup');
});

// Custom commands for responsive testing
Cypress.Commands.add('setMobileViewport', () => {
  cy.viewport(375, 667); // iPhone SE
});

Cypress.Commands.add('setTabletViewport', () => {
  cy.viewport(768, 1024); // iPad
});

Cypress.Commands.add('setDesktopViewport', () => {
  cy.viewport(1280, 720); // Desktop
});

// Custom commands for local storage
Cypress.Commands.add('setLocalStorage', (key, value) => {
  cy.window().then((window) => {
    window.localStorage.setItem(key, JSON.stringify(value));
  });
});

Cypress.Commands.add('getLocalStorage', (key) => {
  return cy.window().then((window) => {
    const value = window.localStorage.getItem(key);
    return value ? JSON.parse(value) : null;
  });
});

// Custom commands for testing utilities
Cypress.Commands.add('getByTestId', (testId) => {
  return cy.get(`[data-testid="${testId}"]`);
});

Cypress.Commands.add('findByTestId', (testId) => {
  return cy.find(`[data-testid="${testId}"]`);
});

// Before each test
beforeEach(() => {
  // Clear application state
  cy.clearLocalStorage();
  cy.clearCookies();
  
  // Set up default viewport
  cy.setDesktopViewport();
});

// After each test
afterEach(() => {
  // Clean up any test data
  cy.task('clearDatabase', null, { log: false });
});
