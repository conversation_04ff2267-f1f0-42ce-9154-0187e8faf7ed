/**
 * End-to-End tests for authentication workflows
 * Tests complete user authentication flows in the browser
 */

describe('Authentication E2E Tests', () => {
  beforeEach(() => {
    // Visit the application
    cy.visit('/');
    
    // Clear any existing authentication state
    cy.clearLocalStorage();
    cy.clearCookies();
  });

  describe('Login Flow', () => {
    it('should display login page for unauthenticated users', () => {
      cy.contains('Fill in the fields below to sign into your account').should('be.visible');
      cy.get('[data-testid="email-input"]').should('be.visible');
      cy.get('[data-testid="password-input"]').should('be.visible');
      cy.get('[data-testid="login-button"]').should('be.visible');
    });

    it('should successfully log in with valid credentials', () => {
      // Fill in login form
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('TokyoPass1@');
      
      // Submit login form
      cy.get('[data-testid="login-button"]').click();
      
      // Should redirect to dashboard after successful login
      cy.url().should('include', '/dashboard');
      cy.contains('Dashboard').should('be.visible');
    });

    it('should show error message for invalid credentials', () => {
      // Fill in login form with invalid credentials
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('wrongpassword');
      
      // Submit login form
      cy.get('[data-testid="login-button"]').click();
      
      // Should show error message
      cy.contains('Invalid credentials').should('be.visible');
      cy.url().should('not.include', '/dashboard');
    });

    it('should validate required fields', () => {
      // Try to submit without filling fields
      cy.get('[data-testid="login-button"]').click();
      
      // Should show validation errors
      cy.contains('Email is required').should('be.visible');
      cy.contains('Password is required').should('be.visible');
    });

    it('should validate email format', () => {
      cy.get('[data-testid="email-input"]').type('invalid-email');
      cy.get('[data-testid="password-input"]').type('password123');
      cy.get('[data-testid="login-button"]').click();
      
      cy.contains('Please enter a valid email').should('be.visible');
    });

    it('should toggle password visibility', () => {
      cy.get('[data-testid="password-input"]').type('password123');
      
      // Password should be hidden by default
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'password');
      
      // Click toggle button
      cy.get('[data-testid="password-toggle"]').click();
      
      // Password should now be visible
      cy.get('[data-testid="password-input"]').should('have.attr', 'type', 'text');
    });
  });

  describe('Registration Flow', () => {
    it('should navigate to registration page', () => {
      cy.contains('Sign up here').click();
      cy.url().should('include', '/register');
      cy.contains('Create your account').should('be.visible');
    });

    it('should successfully register a new user', () => {
      cy.contains('Sign up here').click();
      
      // Fill registration form
      cy.get('[data-testid="name-input"]').type('Test User');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('Password123!');
      cy.get('[data-testid="confirm-password-input"]').type('Password123!');
      cy.get('[data-testid="terms-checkbox"]').check();
      
      // Submit registration
      cy.get('[data-testid="register-button"]').click();
      
      // Should redirect to dashboard after successful registration
      cy.url().should('include', '/dashboard');
    });

    it('should validate password confirmation', () => {
      cy.contains('Sign up here').click();
      
      cy.get('[data-testid="name-input"]').type('Test User');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('Password123!');
      cy.get('[data-testid="confirm-password-input"]').type('DifferentPassword');
      
      cy.get('[data-testid="register-button"]').click();
      
      cy.contains('Passwords do not match').should('be.visible');
    });

    it('should require terms acceptance', () => {
      cy.contains('Sign up here').click();
      
      cy.get('[data-testid="name-input"]').type('Test User');
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('Password123!');
      cy.get('[data-testid="confirm-password-input"]').type('Password123!');
      
      // Don't check terms checkbox
      cy.get('[data-testid="register-button"]').click();
      
      cy.contains('You must accept the terms').should('be.visible');
    });
  });

  describe('Logout Flow', () => {
    beforeEach(() => {
      // Log in first
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('TokyoPass1@');
      cy.get('[data-testid="login-button"]').click();
      cy.url().should('include', '/dashboard');
    });

    it('should successfully log out', () => {
      // Click user menu
      cy.get('[data-testid="user-menu"]').click();
      
      // Click logout
      cy.get('[data-testid="logout-button"]').click();
      
      // Should redirect to login page
      cy.url().should('not.include', '/dashboard');
      cy.contains('Fill in the fields below to sign into your account').should('be.visible');
    });

    it('should clear authentication state on logout', () => {
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="logout-button"]').click();
      
      // Try to access protected route directly
      cy.visit('/dashboard');
      
      // Should redirect to login
      cy.contains('Fill in the fields below to sign into your account').should('be.visible');
    });
  });

  describe('Protected Routes', () => {
    it('should redirect unauthenticated users to login', () => {
      const protectedRoutes = ['/dashboard', '/projects', '/settings'];
      
      protectedRoutes.forEach((route) => {
        cy.visit(route);
        cy.contains('Fill in the fields below to sign into your account').should('be.visible');
      });
    });

    it('should remember requested route after login', () => {
      // Try to access protected route
      cy.visit('/projects');
      
      // Should be redirected to login
      cy.contains('Fill in the fields below to sign into your account').should('be.visible');
      
      // Log in
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('TokyoPass1@');
      cy.get('[data-testid="login-button"]').click();
      
      // Should redirect to originally requested route
      cy.url().should('include', '/projects');
    });
  });

  describe('Session Management', () => {
    it('should maintain session across page refreshes', () => {
      // Log in
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('TokyoPass1@');
      cy.get('[data-testid="login-button"]').click();
      cy.url().should('include', '/dashboard');
      
      // Refresh page
      cy.reload();
      
      // Should still be logged in
      cy.url().should('include', '/dashboard');
      cy.contains('Dashboard').should('be.visible');
    });

    it('should handle session expiration', () => {
      // Log in
      cy.get('[data-testid="email-input"]').type('<EMAIL>');
      cy.get('[data-testid="password-input"]').type('TokyoPass1@');
      cy.get('[data-testid="login-button"]').click();
      cy.url().should('include', '/dashboard');
      
      // Simulate session expiration by clearing storage
      cy.clearLocalStorage();
      
      // Try to access protected content
      cy.visit('/projects');
      
      // Should redirect to login
      cy.contains('Fill in the fields below to sign into your account').should('be.visible');
    });
  });

  describe('Auth0 Integration', () => {
    it('should display Auth0 login option', () => {
      cy.contains('Sign in with Auth0').should('be.visible');
    });

    it('should handle Auth0 login flow', () => {
      cy.contains('Sign in with Auth0').click();
      
      // Note: In a real test, this would redirect to Auth0
      // For testing purposes, we might mock this or use a test Auth0 tenant
    });
  });

  describe('Accessibility', () => {
    it('should be keyboard navigable', () => {
      // Tab through form elements
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid', 'email-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'password-input');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid', 'login-button');
    });

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="email-input"]').should('have.attr', 'aria-label');
      cy.get('[data-testid="password-input"]').should('have.attr', 'aria-label');
      cy.get('[data-testid="login-button"]').should('have.attr', 'aria-label');
    });
  });
});
