/**
 * End-to-End tests for dashboard navigation and functionality
 * Tests navigation between different dashboard views and components
 */

describe('Dashboard Navigation E2E Tests', () => {
  beforeEach(() => {
    // Log in before each test
    cy.login();
    cy.visit('/dashboard');
  });

  describe('Main Dashboard', () => {
    it('should display main dashboard elements', () => {
      cy.contains('Dashboard').should('be.visible');
      cy.get('[data-testid="dashboard-header"]').should('be.visible');
      cy.get('[data-testid="dashboard-content"]').should('be.visible');
    });

    it('should show user information', () => {
      cy.get('[data-testid="user-info"]').should('be.visible');
      cy.get('[data-testid="user-avatar"]').should('be.visible');
      cy.contains('Test User').should('be.visible');
    });

    it('should display navigation menu', () => {
      cy.get('[data-testid="main-navigation"]').should('be.visible');
      cy.get('[data-testid="nav-dashboard"]').should('be.visible');
      cy.get('[data-testid="nav-projects"]').should('be.visible');
      cy.get('[data-testid="nav-analytics"]').should('be.visible');
    });
  });

  describe('Dashboard Types Navigation', () => {
    it('should navigate to exhibit tasks dashboard', () => {
      cy.visit('/dashboard/exhibit-tasks');
      
      cy.contains('Exhibit Tasks').should('be.visible');
      cy.get('[data-testid="tasks-overview"]').should('be.visible');
      cy.get('[data-testid="task-list"]').should('be.visible');
    });

    it('should navigate to analytics dashboard', () => {
      cy.visit('/dashboard/analytics');
      
      cy.contains('Analytics').should('be.visible');
      cy.get('[data-testid="analytics-charts"]').should('be.visible');
      cy.get('[data-testid="audience-overview"]').should('be.visible');
    });

    it('should navigate to commerce dashboard', () => {
      cy.visit('/dashboard/commerce');
      
      cy.contains('Commerce').should('be.visible');
      cy.get('[data-testid="sales-overview"]').should('be.visible');
      cy.get('[data-testid="revenue-chart"]').should('be.visible');
    });

    it('should navigate to automation dashboard', () => {
      cy.visit('/dashboard/automation');
      
      cy.contains('Automation').should('be.visible');
      cy.get('[data-testid="automation-controls"]').should('be.visible');
      cy.get('[data-testid="device-status"]').should('be.visible');
    });
  });

  describe('Sidebar Navigation', () => {
    it('should toggle sidebar', () => {
      cy.get('[data-testid="sidebar-toggle"]').click();
      cy.get('[data-testid="sidebar"]').should('have.class', 'collapsed');
      
      cy.get('[data-testid="sidebar-toggle"]').click();
      cy.get('[data-testid="sidebar"]').should('not.have.class', 'collapsed');
    });

    it('should navigate using sidebar links', () => {
      cy.get('[data-testid="sidebar-projects"]').click();
      cy.url().should('include', '/projects');
      
      cy.get('[data-testid="sidebar-dashboard"]').click();
      cy.url().should('include', '/dashboard');
    });

    it('should highlight active navigation item', () => {
      cy.visit('/projects');
      cy.get('[data-testid="sidebar-projects"]').should('have.class', 'active');
      
      cy.visit('/dashboard');
      cy.get('[data-testid="sidebar-dashboard"]').should('have.class', 'active');
    });
  });

  describe('Breadcrumb Navigation', () => {
    it('should display breadcrumbs', () => {
      cy.visit('/projects');
      cy.get('[data-testid="breadcrumb"]').should('be.visible');
      cy.get('[data-testid="breadcrumb"]').should('contain', 'Projects');
    });

    it('should navigate using breadcrumbs', () => {
      cy.visit('/projects/1');
      
      cy.get('[data-testid="breadcrumb-projects"]').click();
      cy.url().should('include', '/projects');
      cy.url().should('not.include', '/projects/1');
    });
  });

  describe('Quick Actions', () => {
    it('should display quick action buttons', () => {
      cy.get('[data-testid="quick-actions"]').should('be.visible');
      cy.get('[data-testid="quick-create-project"]').should('be.visible');
      cy.get('[data-testid="quick-view-reports"]').should('be.visible');
    });

    it('should execute quick actions', () => {
      cy.get('[data-testid="quick-create-project"]').click();
      cy.url().should('include', '/projects/create');
    });
  });

  describe('Search Functionality', () => {
    it('should display global search', () => {
      cy.get('[data-testid="global-search"]').should('be.visible');
    });

    it('should perform search', () => {
      cy.get('[data-testid="global-search"]').type('test project');
      cy.get('[data-testid="search-results"]').should('be.visible');
      cy.get('[data-testid="search-result-item"]').should('have.length.greaterThan', 0);
    });

    it('should navigate to search results', () => {
      cy.get('[data-testid="global-search"]').type('test project');
      cy.get('[data-testid="search-result-item"]').first().click();
      
      // Should navigate to the selected item
      cy.url().should('match', /\/(projects|dashboard|analytics)/);
    });
  });

  describe('Notifications', () => {
    it('should display notification bell', () => {
      cy.get('[data-testid="notification-bell"]').should('be.visible');
    });

    it('should show notification dropdown', () => {
      cy.get('[data-testid="notification-bell"]').click();
      cy.get('[data-testid="notification-dropdown"]').should('be.visible');
      cy.get('[data-testid="notification-list"]').should('be.visible');
    });

    it('should mark notifications as read', () => {
      cy.get('[data-testid="notification-bell"]').click();
      cy.get('[data-testid="notification-item"]').first().click();
      
      // Notification should be marked as read
      cy.get('[data-testid="notification-item"]').first().should('have.class', 'read');
    });
  });

  describe('User Menu', () => {
    it('should display user menu', () => {
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="user-dropdown"]').should('be.visible');
    });

    it('should navigate to profile', () => {
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="profile-link"]').click();
      cy.url().should('include', '/profile');
    });

    it('should navigate to settings', () => {
      cy.get('[data-testid="user-menu"]').click();
      cy.get('[data-testid="settings-link"]').click();
      cy.url().should('include', '/settings');
    });
  });

  describe('Theme Switching', () => {
    it('should toggle between light and dark themes', () => {
      cy.get('[data-testid="theme-toggle"]').click();
      cy.get('body').should('have.class', 'dark-theme');
      
      cy.get('[data-testid="theme-toggle"]').click();
      cy.get('body').should('have.class', 'light-theme');
    });

    it('should persist theme preference', () => {
      cy.get('[data-testid="theme-toggle"]').click();
      cy.reload();
      cy.get('body').should('have.class', 'dark-theme');
    });
  });

  describe('Responsive Navigation', () => {
    it('should adapt to mobile viewport', () => {
      cy.setMobileViewport();
      
      cy.get('[data-testid="mobile-menu-toggle"]').should('be.visible');
      cy.get('[data-testid="sidebar"]').should('have.class', 'mobile-hidden');
    });

    it('should show mobile menu', () => {
      cy.setMobileViewport();
      
      cy.get('[data-testid="mobile-menu-toggle"]').click();
      cy.get('[data-testid="mobile-menu"]').should('be.visible');
    });

    it('should adapt to tablet viewport', () => {
      cy.setTabletViewport();
      
      cy.get('[data-testid="sidebar"]').should('be.visible');
      cy.get('[data-testid="mobile-menu-toggle"]').should('not.be.visible');
    });
  });

  describe('Keyboard Navigation', () => {
    it('should support keyboard shortcuts', () => {
      // Test common keyboard shortcuts
      cy.get('body').type('{ctrl}k'); // Global search shortcut
      cy.get('[data-testid="global-search"]').should('be.focused');
      
      cy.get('body').type('{esc}'); // Close search
      cy.get('[data-testid="search-results"]').should('not.be.visible');
    });

    it('should be tab navigable', () => {
      cy.get('body').tab();
      cy.focused().should('have.attr', 'data-testid');
      
      cy.focused().tab();
      cy.focused().should('have.attr', 'data-testid');
    });
  });

  describe('Error Handling', () => {
    it('should handle navigation errors gracefully', () => {
      cy.visit('/dashboard/nonexistent');
      cy.contains('Page not found').should('be.visible');
      cy.get('[data-testid="back-to-dashboard"]').should('be.visible');
    });

    it('should provide fallback navigation', () => {
      cy.visit('/dashboard/nonexistent');
      cy.get('[data-testid="back-to-dashboard"]').click();
      cy.url().should('include', '/dashboard');
    });
  });
});
