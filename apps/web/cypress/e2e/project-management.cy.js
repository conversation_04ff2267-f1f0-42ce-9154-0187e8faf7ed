/**
 * End-to-End tests for project management workflows
 * Tests complete project CRUD operations in the browser
 */

describe('Project Management E2E Tests', () => {
  beforeEach(() => {
    // Log in before each test
    cy.visit('/');
    cy.get('[data-testid="email-input"]').type('<EMAIL>');
    cy.get('[data-testid="password-input"]').type('TokyoPass1@');
    cy.get('[data-testid="login-button"]').click();
    cy.url().should('include', '/dashboard');
  });

  describe('Project List', () => {
    it('should display projects list', () => {
      cy.visit('/projects');
      
      cy.contains('Projects').should('be.visible');
      cy.get('[data-testid="projects-grid"]').should('be.visible');
      cy.get('[data-testid="project-card"]').should('have.length.greaterThan', 0);
    });

    it('should search projects', () => {
      cy.visit('/projects');
      
      // Wait for projects to load
      cy.get('[data-testid="project-card"]').should('have.length.greaterThan', 0);
      
      // Search for specific project
      cy.get('[data-testid="search-input"]').type('Test Project');
      
      // Should filter results
      cy.get('[data-testid="project-card"]').should('contain', 'Test Project');
    });

    it('should filter projects by status', () => {
      cy.visit('/projects');
      
      // Open filter dropdown
      cy.get('[data-testid="status-filter"]').click();
      
      // Select active projects
      cy.get('[data-testid="filter-active"]').click();
      
      // Should show only active projects
      cy.get('[data-testid="project-card"]').each(($card) => {
        cy.wrap($card).should('contain', 'Active');
      });
    });

    it('should sort projects', () => {
      cy.visit('/projects');
      
      // Open sort dropdown
      cy.get('[data-testid="sort-dropdown"]').click();
      
      // Sort by name
      cy.get('[data-testid="sort-name"]').click();
      
      // Verify sorting (this would need specific implementation)
      cy.get('[data-testid="project-card"]').should('be.visible');
    });
  });

  describe('Project Creation', () => {
    it('should create a new project', () => {
      cy.visit('/projects');
      
      // Click create project button
      cy.get('[data-testid="create-project-button"]').click();
      
      // Fill project form
      cy.get('[data-testid="project-name-input"]').type('E2E Test Project');
      cy.get('[data-testid="project-description-input"]').type('This is a test project created by E2E tests');
      
      // Select project type
      cy.get('[data-testid="project-type-select"]').click();
      cy.get('[data-testid="project-type-exhibition"]').click();
      
      // Set project dates
      cy.get('[data-testid="start-date-input"]').type('2024-01-01');
      cy.get('[data-testid="end-date-input"]').type('2024-12-31');
      
      // Submit form
      cy.get('[data-testid="create-project-submit"]').click();
      
      // Should redirect to project detail page
      cy.url().should('include', '/projects/');
      cy.contains('E2E Test Project').should('be.visible');
      
      // Should show success message
      cy.contains('Project created successfully').should('be.visible');
    });

    it('should validate required fields', () => {
      cy.visit('/projects');
      
      cy.get('[data-testid="create-project-button"]').click();
      
      // Try to submit without required fields
      cy.get('[data-testid="create-project-submit"]').click();
      
      // Should show validation errors
      cy.contains('Project name is required').should('be.visible');
      cy.contains('Description is required').should('be.visible');
    });

    it('should handle form errors gracefully', () => {
      cy.visit('/projects');
      
      cy.get('[data-testid="create-project-button"]').click();
      
      // Fill form with invalid data
      cy.get('[data-testid="project-name-input"]').type('A'); // Too short
      cy.get('[data-testid="project-description-input"]').type('Short');
      
      cy.get('[data-testid="create-project-submit"]').click();
      
      // Should show validation errors
      cy.contains('Project name must be at least 3 characters').should('be.visible');
    });

    it('should save draft and continue later', () => {
      cy.visit('/projects');
      
      cy.get('[data-testid="create-project-button"]').click();
      
      // Fill partial form
      cy.get('[data-testid="project-name-input"]').type('Draft Project');
      
      // Save as draft
      cy.get('[data-testid="save-draft-button"]').click();
      
      // Should show draft saved message
      cy.contains('Draft saved').should('be.visible');
      
      // Navigate away and back
      cy.visit('/dashboard');
      cy.visit('/projects');
      
      // Should be able to continue draft
      cy.get('[data-testid="continue-draft-button"]').should('be.visible');
    });
  });

  describe('Project Detail View', () => {
    it('should display project details', () => {
      cy.visit('/projects');
      
      // Click on first project
      cy.get('[data-testid="project-card"]').first().click();
      
      // Should show project details
      cy.get('[data-testid="project-title"]').should('be.visible');
      cy.get('[data-testid="project-description"]').should('be.visible');
      cy.get('[data-testid="project-status"]').should('be.visible');
    });

    it('should navigate between tabs', () => {
      cy.visit('/projects');
      cy.get('[data-testid="project-card"]').first().click();
      
      // Should show tabs
      cy.get('[data-testid="tab-tasks"]').should('be.visible');
      cy.get('[data-testid="tab-items"]').should('be.visible');
      cy.get('[data-testid="tab-files"]').should('be.visible');
      cy.get('[data-testid="tab-cases"]').should('be.visible');
      
      // Click on items tab
      cy.get('[data-testid="tab-items"]').click();
      cy.get('[data-testid="items-content"]').should('be.visible');
      
      // Click on files tab
      cy.get('[data-testid="tab-files"]').click();
      cy.get('[data-testid="files-content"]').should('be.visible');
    });

    it('should edit project details', () => {
      cy.visit('/projects');
      cy.get('[data-testid="project-card"]').first().click();
      
      // Click edit button
      cy.get('[data-testid="edit-project-button"]').click();
      
      // Update project name
      cy.get('[data-testid="project-name-input"]').clear().type('Updated Project Name');
      
      // Save changes
      cy.get('[data-testid="save-changes-button"]').click();
      
      // Should show updated name
      cy.contains('Updated Project Name').should('be.visible');
      cy.contains('Project updated successfully').should('be.visible');
    });
  });

  describe('Project Tasks', () => {
    beforeEach(() => {
      cy.visit('/projects');
      cy.get('[data-testid="project-card"]').first().click();
      cy.get('[data-testid="tab-tasks"]').click();
    });

    it('should add a new task', () => {
      // Click add task button
      cy.get('[data-testid="add-task-button"]').click();
      
      // Fill task form
      cy.get('[data-testid="task-name-input"]').type('E2E Test Task');
      cy.get('[data-testid="task-description-input"]').type('Task created by E2E test');
      cy.get('[data-testid="task-priority-select"]').select('High');
      cy.get('[data-testid="task-assignee-select"]').select('Test User');
      
      // Set due date
      cy.get('[data-testid="task-due-date"]').type('2024-06-30');
      
      // Save task
      cy.get('[data-testid="save-task-button"]').click();
      
      // Should show new task in list
      cy.contains('E2E Test Task').should('be.visible');
      cy.contains('Task created successfully').should('be.visible');
    });

    it('should mark task as complete', () => {
      // Find a task and mark as complete
      cy.get('[data-testid="task-item"]').first().within(() => {
        cy.get('[data-testid="task-checkbox"]').check();
      });
      
      // Should show completion status
      cy.get('[data-testid="task-item"]').first().should('have.class', 'completed');
      cy.contains('Task marked as complete').should('be.visible');
    });

    it('should edit task details', () => {
      // Click on task to edit
      cy.get('[data-testid="task-item"]').first().click();
      
      // Update task name
      cy.get('[data-testid="task-name-input"]').clear().type('Updated Task Name');
      
      // Save changes
      cy.get('[data-testid="save-task-button"]').click();
      
      // Should show updated task
      cy.contains('Updated Task Name').should('be.visible');
    });

    it('should delete a task', () => {
      // Click delete button on first task
      cy.get('[data-testid="task-item"]').first().within(() => {
        cy.get('[data-testid="delete-task-button"]').click();
      });
      
      // Confirm deletion
      cy.get('[data-testid="confirm-delete-button"]').click();
      
      // Should show deletion confirmation
      cy.contains('Task deleted successfully').should('be.visible');
    });
  });

  describe('File Management', () => {
    beforeEach(() => {
      cy.visit('/projects');
      cy.get('[data-testid="project-card"]').first().click();
      cy.get('[data-testid="tab-files"]').click();
    });

    it('should upload a file', () => {
      // Create a test file
      const fileName = 'test-document.pdf';
      
      // Upload file
      cy.get('[data-testid="file-upload-input"]').selectFile({
        contents: Cypress.Buffer.from('Test file content'),
        fileName: fileName,
        mimeType: 'application/pdf',
      });
      
      // Should show upload progress
      cy.get('[data-testid="upload-progress"]').should('be.visible');
      
      // Should show uploaded file
      cy.contains(fileName).should('be.visible');
      cy.contains('File uploaded successfully').should('be.visible');
    });

    it('should download a file', () => {
      // Click download button on first file
      cy.get('[data-testid="file-item"]').first().within(() => {
        cy.get('[data-testid="download-file-button"]').click();
      });
      
      // File should start downloading (browser dependent)
      // In a real test, you might verify the download folder
    });

    it('should delete a file', () => {
      // Click delete button on first file
      cy.get('[data-testid="file-item"]').first().within(() => {
        cy.get('[data-testid="delete-file-button"]').click();
      });
      
      // Confirm deletion
      cy.get('[data-testid="confirm-delete-button"]').click();
      
      // Should show deletion confirmation
      cy.contains('File deleted successfully').should('be.visible');
    });
  });

  describe('Project Deletion', () => {
    it('should delete a project', () => {
      cy.visit('/projects');
      cy.get('[data-testid="project-card"]').first().click();
      
      // Click delete project button
      cy.get('[data-testid="delete-project-button"]').click();
      
      // Confirm deletion
      cy.get('[data-testid="confirm-delete-input"]').type('DELETE');
      cy.get('[data-testid="confirm-delete-button"]').click();
      
      // Should redirect to projects list
      cy.url().should('include', '/projects');
      cy.contains('Project deleted successfully').should('be.visible');
    });
  });
});
