{"name": "eh-react-portal-v2", "version": "4.22.62", "title": "Exhibit house react application", "description": "High performance exhibitor built with lots of powerful components across multiple product niches for fast & perfect apps development processes.", "author": {"name": "<PERSON>", "url": "https://newgennow.com"}, "private": true, "dependencies": {"@auth0/auth0-spa-js": "1.19.2", "@emoji-mart/data": "^1.0.2", "@emotion/react": "^11.7.1", "@emotion/styled": "^11.6.0", "@fontsource/roboto": "^4.5.8", "@fullcalendar/core": "6.1.5", "@fullcalendar/daygrid": "6.1.5", "@fullcalendar/interaction": "6.1.5", "@fullcalendar/list": "6.1.5", "@fullcalendar/multimonth": "6.1.5", "@fullcalendar/react": "6.1.5", "@fullcalendar/timegrid": "6.1.5", "@google/model-viewer": "^1.10.1", "@headlessui/react": "^1.5.0", "@lottiefiles/react-lottie-player": "^3.4.2", "@mui/base": "^5.0.0-alpha.122", "@mui/icons-material": "^6.4.0", "@mui/lab": "6.0.0-beta.23", "@mui/material": "^6.4.0", "@mui/material-next": "^6.0.0-alpha.79", "@mui/styles": "^6.4.0", "@mui/x-data-grid": "^7.23.6", "@mui/x-data-grid-pro": "^7.23.6", "@mui/x-date-pickers": "^7.23.6", "@mui/x-date-pickers-pro": "^7.23.6", "@mui/x-license-pro": "^6.10.2", "@reduxjs/toolkit": "1.6.2", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@untitled-ui/icons-react": "0.1.1", "apexcharts": "3.30.0", "autosuggest-highlight": "^3.3.0", "aws-amplify": "4.3.7", "axios": "0.24.0", "axios-mock-adapter": "1.20.0", "chart.js": "2.9.4", "clsx": "1.1.1", "country-flag-icons": "1.4.14", "date-fns": "2.25.0", "dompurify": "^2.3.5", "emoji-mart": "^5.1.0", "firebase": "9.4.1", "formik": "2.2.9", "formik-mui": "4.0.0-alpha.3", "formik-mui-lab": "1.0.0-alpha.3", "framer-motion": "^6.2.6", "gltf-validator": "^2.0.0-dev.3.5", "history": "5.3.0", "html-react-parser": "^1.4.8", "i18next": "21.8.8", "i18next-browser-languagedetector": "6.1.4", "js-cookie": "^3.0.1", "jsonwebtoken": "8.5.1", "mui-tel-input": "^5.1.2", "notistack": "3.0.1", "nprogress": "0.2.0", "numeral": "2.0.6", "prop-types": "15.8.1", "react": "18.2.0", "react-apexcharts": "1.4.0", "react-beautiful-dnd": "13.1.0", "react-chartjs-2": "2.11.1", "react-circular-progressbar": "2.0.4", "react-countup": "6.2.0", "react-custom-scrollbars-2": "4.4.0", "react-dom": "18.2.0", "react-dropzone": "14.2.1", "react-gauge-chart": "0.4.0", "react-helmet-async": "1.3.0", "react-i18next": "11.17.0", "react-imask": "^6.4.2", "react-quill": "2.0.0-beta.4", "react-redux": "8.0.2", "react-router": "6.3.0", "react-router-dom": "6.3.0", "react-scripts": "5.0.1", "react-simple-maps": "2.3.0", "react-sparklines": "1.7.0", "react-spinners-kit": "^1.9.1", "react-swipeable-views": "^0.14.0", "react-syntax-highlighter": "15.5.0", "redux": "4.2.0", "redux-devtools-extension": "2.13.9", "redux-thunk": "2.4.1", "request": "^2.88.2", "stylis": "4.1.1", "stylis-plugin-rtl": "2.1.1", "swiper": "8.2.2", "swr": "^2.2.5", "uuid": "8.3.2", "web-vitals": "2.1.4", "yup": "0.32.11", "zustand": "^4.5.2"}, "scripts": {"dev": "npx craco start", "start": "npx craco start", "build-web:bundle-ci": "node postbuild --ci", "build-web:bundle": "node postbuild", "build-web:react": "npx craco build", "build-web:dev": "yarn run build-web:react && yarn run build-web:bundle", "build-web": "yarn run remove:build-web && yarn run build-web:react && yarn run build-web:bundle-ci", "remove:build-web": "rm -rf ./eh-customer-portal && rm -rf ./eh-customer-portal.zip && yarn run remove-ns-web:build-web", "remove-ns-web:build-web": "rm -rf \"../backend/src/FileCabinet/SuiteApps/com.newgennow.csexhibithouse/CSEH-Web/eh-customer-portal\"", "test": "npx craco test --watchAll=false", "test:watch": "npx craco test", "test:coverage": "npx craco test --coverage --watchAll=false", "test:unit": "npx craco test --testPathPattern=src/.*\\.(test|spec)\\.(js|jsx|ts|tsx)$ --watchAll=false", "test:integration": "npx craco test --testPathPattern=src/__tests__/integration --watchAll=false", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:e2e:headed": "cypress run --headed", "test:component": "cypress run --component", "test:component:open": "cypress open --component", "test:all": "node scripts/test-runner.js all", "test:runner": "node scripts/test-runner.js", "lint": "npx oxlint", "eslint": "eslint .", "lint:fix": "eslint --fix ./src", "format": "npx prettier --write \"./**/*.{js,jsx,json}\" --config ./.prettierrc", "chrome:unsecure": "\"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" --user-data-dir=\"C:/Chrome dev session\" --disable-web-security"}, "lint-staged": {"*.+(js|jsx)": ["npx prettier --write --ignore-unknown"], "*.+(json|css|md)": ["npx prettier --write"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/core": "7.18.2", "@babel/eslint-parser": "7.18.2", "@craco/craco": "^7.1.0", "@cypress/code-coverage": "^3.9.12", "@cypress/instrument-cra": "^1.4.0", "@cypress/react": "^5.12.4", "@cypress/webpack-dev-server": "^1.8.3", "@playwright/test": "^1.40.0", "@testing-library/cypress": "^10.0.3", "@testing-library/dom": "^8.20.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^13.4.0", "@testing-library/react-hooks": "^8.0.1", "@testing-library/user-event": "^14.5.1", "browserify-zlib": "^0.2.0", "chalk": "^4.1.2", "crypto-browserify": "^3.12.0", "cypress": "^14.5.0", "dotenv-webpack": "^8.0.1", "eslint": "8.34.0", "eslint-config-prettier": "8.6.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-import": "2.26.0", "eslint-plugin-jsx-a11y": "6.5.1", "eslint-plugin-oxlint": "^0.2.9", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-testing-library": "^6.1.0", "fs-extra": "^10.0.1", "https-browserify": "^1.0.0", "jest-environment-jsdom": "^29.7.0", "mv": "^2.1.1", "node-polyfill-webpack-plugin": "^2.0.1", "oxlint": "^0.2.17", "path-browserify": "^1.0.1", "prettier": "2.6.2", "prompts": "^2.4.2", "rewire": "^6.0.0", "stream-http": "^3.2.0", "tailwindcss": "^3.2.4", "zip-folder": "^1.0.0"}}