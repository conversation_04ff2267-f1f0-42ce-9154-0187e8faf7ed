/**
 * Cypress configuration for E2E and component testing
 */

const { defineConfig } = require('cypress');

module.exports = defineConfig({
  // Global configuration
  viewportWidth: 1280,
  viewportHeight: 720,
  video: true,
  screenshotOnRunFailure: true,
  defaultCommandTimeout: 10000,
  requestTimeout: 10000,
  responseTimeout: 10000,
  pageLoadTimeout: 30000,
  
  // Retry configuration
  retries: {
    runMode: 2,
    openMode: 0,
  },

  // E2E Testing configuration
  e2e: {
    baseUrl: 'http://localhost:3000',
    specPattern: 'cypress/e2e/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/e2e.js',
    
    setupNodeEvents(on, config) {
      // implement node event listeners here
      require('@cypress/code-coverage/task')(on, config);
      
      // Add custom tasks
      on('task', {
        log(message) {
          console.log(message);
          return null;
        },
        
        clearDatabase() {
          // Clear test database if needed
          return null;
        },
        
        seedDatabase() {
          // Seed test data if needed
          return null;
        },
      });

      return config;
    },
  },

  // Component Testing configuration
  component: {
    devServer: {
      framework: 'create-react-app',
      bundler: 'webpack',
    },
    specPattern: 'src/**/*.cy.{js,jsx,ts,tsx}',
    supportFile: 'cypress/support/component.js',
    
    setupNodeEvents(on, config) {
      require('@cypress/code-coverage/task')(on, config);
      return config;
    },
  },

  // Environment variables
  env: {
    REACT_APP_API_BASE_URL: 'http://localhost:3000',
    coverage: true,
    codeCoverage: {
      exclude: [
        'cypress/**/*.*',
        'src/**/*.test.*',
        'src/**/*.spec.*',
        'src/setupTests.js',
        'src/reportWebVitals.js',
        'src/serviceWorker.js',
      ],
    },
  },
});
