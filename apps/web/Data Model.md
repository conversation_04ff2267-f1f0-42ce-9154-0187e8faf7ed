# Data Models

This document outlines the data structures used in the Exhibit House web application.

## Project List Item

Represents a project in the project listing view.

### Properties

| Property | Type | Description |
|----------|------|-------------|
| custentity_ng_eh_booth_description | String | Description of the booth |
| custentity_ng_eh_event_address | String | ID of the event address |
| custentity_ng_eh_proj_portal_blurb | String | HTML content of the project description |
| customer | String | Customer ID |
| enddate | String | End date of the project (MM/DD/YYYY) |
| entityid | String | Project identifier (e.g., "PRJ0009") |
| entitystatus | String | Status of the project |
| id | String | Project ID |
| internalid | String | Internal project ID |
| materialarrival | String | Material arrival date (MM/DD/YYYY) |
| materialpickup | String | Material pickup date (MM/DD/YYYY) |
| progress | String | Project completion percentage |
| project | String | Project name with customer (e.g., "Anonymous : Test Project") |
| projreturn | String | Project return date (MM/DD/YYYY) |
| shipdate | String | Ship date (MM/DD/YYYY) |
| shipmentlocation | String | Shipment location ID |
| shipmentlocationname | String | Shipment location name |
| startdate | String | Project start date (MM/DD/YYYY) |
| status | String | Project status |

## Project Detail

Represents detailed information about a project.

### Properties

| Property | Type | Description |
|----------|------|-------------|
| bannerImage | String | URL of the project banner image |
| boothDescription | String | Description of the booth |
| cases | Array | List of cases associated with the project |
| customer | String | Customer ID |
| discountexpirationdate | String | Discount expiration date (MM/DD/YYYY) |
| eventenddate | String | Event end date (MM/DD/YYYY) |
| eventstartdate | String | Event start date (MM/DD/YYYY) |
| files | Array | List of files associated with the project |
| id | String | Project ID |
| materialarrival | String | Material arrival date (MM/DD/YYYY) |
| materialpickup | String | Material pickup date (MM/DD/YYYY) |
| moveindate | String | Move-in date (MM/DD/YYYY) |
| moveoutdate | String | Move-out date (MM/DD/YYYY) |
| name | String | Project name with customer |
| projectContentBlurb | String | HTML content of the project description |
| returndate | String | Return date (MM/DD/YYYY) |
| sales | Array | Sales information |
| shipdate | String | Ship date (MM/DD/YYYY) |
| tasks | Task[] | Array of tasks associated with the project |

### Task Object

Represents a task within a project.

| Property | Type | Description |
|----------|------|-------------|
| id | String | Task ID |
| internalid | String | Internal task ID |
| projectid | String | ID of the associated project |
| name | String | Name of the task |
| company | String | Company name with project ID |
| startdate | String | Task start date (MM/DD/YYYY) |
| parsedstartdate | Number | Unix timestamp of start date |
| enddate | String | Task end date (MM/DD/YYYY) |
| parsedenddate | Number | Unix timestamp of end date |
| finishbydate | String | Task due date (MM/DD/YYYY) |
| parsedfinishbydate | Number | Unix timestamp of due date |
| status | String | Task status (e.g., "Not Started") |
| progress | String | Task completion percentage |
| assignee | String | Name of the person assigned to the task |

## Notes

- Dates in the format MM/DD/YYYY are stored as strings in the frontend
- Timestamps are represented as Unix timestamps (seconds since epoch)
- HTML content is stored as escaped HTML strings
