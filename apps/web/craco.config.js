const NodePolyfillPlugin = require('node-polyfill-webpack-plugin');
const DotEnv = require('dotenv-webpack');

module.exports = {
  webpack: {
    mode: 'extends',
    plugins: {
      add: [new NodePolyfillPlugin(), [new DotEnv(), 'append']]
    },
    configure: (webpackConfig, { env, paths }) => {
      /* ... */
      webpackConfig.resolve.fallback = {
        ...webpackConfig.resolve.fallback,
        net: false,
        tls: false,
        fs: false
      };

      return webpackConfig;
    }
  }
};
