#!/usr/bin/env node

/**
 * Comprehensive test runner script
 * Runs different types of tests based on command line arguments
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Parse command line arguments
const args = process.argv.slice(2);
const testType = args[0] || 'all';
const options = args.slice(1);

// Test configurations
const testConfigs = {
  unit: {
    command: 'craco',
    args: ['test', '--testPathPattern=src/.*\\.(test|spec)\\.(js|jsx|ts|tsx)$', '--watchAll=false'],
    description: 'Running unit tests...',
  },
  integration: {
    command: 'craco',
    args: ['test', '--testPathPattern=src/__tests__/integration', '--watchAll=false'],
    description: 'Running integration tests...',
  },
  e2e: {
    command: 'cypress',
    args: ['run'],
    description: 'Running E2E tests...',
  },
  'e2e:headed': {
    command: 'cypress',
    args: ['run', '--headed'],
    description: 'Running E2E tests in headed mode...',
  },
  'e2e:open': {
    command: 'cypress',
    args: ['open'],
    description: 'Opening Cypress Test Runner...',
  },
  component: {
    command: 'cypress',
    args: ['run', '--component'],
    description: 'Running component tests...',
  },
  coverage: {
    command: 'craco',
    args: ['test', '--coverage', '--watchAll=false'],
    description: 'Running tests with coverage...',
  },
  watch: {
    command: 'craco',
    args: ['test'],
    description: 'Running tests in watch mode...',
  },
};

// Helper functions
function runCommand(command, args, description) {
  console.log(`\n🧪 ${description}`);
  console.log(`📝 Command: ${command} ${args.join(' ')}\n`);

  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`\n✅ ${description} completed successfully\n`);
        resolve(code);
      } else {
        console.log(`\n❌ ${description} failed with exit code ${code}\n`);
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error(`\n❌ Error running ${description}:`, error.message);
      reject(error);
    });
  });
}

function checkPrerequisites() {
  console.log('🔍 Checking prerequisites...');
  
  // Check if node_modules exists
  if (!fs.existsSync(path.join(process.cwd(), 'node_modules'))) {
    console.error('❌ node_modules not found. Please run "npm install" first.');
    process.exit(1);
  }

  // Check if required test files exist
  const requiredFiles = [
    'src/setupTests.js',
    'jest.config.js',
  ];

  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(process.cwd(), file))) {
      console.error(`❌ Required file ${file} not found.`);
      process.exit(1);
    }
  }

  console.log('✅ Prerequisites check passed\n');
}

function showHelp() {
  console.log(`
🧪 Test Runner Help

Usage: npm run test:runner [type] [options]

Test Types:
  unit         Run unit tests only
  integration  Run integration tests only
  e2e          Run E2E tests in headless mode
  e2e:headed   Run E2E tests in headed mode
  e2e:open     Open Cypress Test Runner
  component    Run component tests
  coverage     Run all tests with coverage report
  watch        Run tests in watch mode
  all          Run all tests (default)

Examples:
  npm run test:runner unit
  npm run test:runner e2e
  npm run test:runner coverage
  npm run test:runner all

Options:
  --help       Show this help message
  --verbose    Show verbose output
  --bail       Stop on first test failure
`);
}

async function runAllTests() {
  console.log('🚀 Running comprehensive test suite...\n');
  
  const testSequence = [
    'unit',
    'integration',
    'e2e',
  ];

  let allPassed = true;
  const results = {};

  for (const testType of testSequence) {
    try {
      const config = testConfigs[testType];
      await runCommand(config.command, config.args, config.description);
      results[testType] = 'PASSED';
    } catch (error) {
      results[testType] = 'FAILED';
      allPassed = false;
      
      if (options.includes('--bail')) {
        console.log('🛑 Stopping test execution due to --bail flag');
        break;
      }
    }
  }

  // Print summary
  console.log('\n📊 Test Results Summary:');
  console.log('========================');
  
  for (const [testType, result] of Object.entries(results)) {
    const icon = result === 'PASSED' ? '✅' : '❌';
    console.log(`${icon} ${testType.padEnd(12)} ${result}`);
  }

  if (allPassed) {
    console.log('\n🎉 All tests passed successfully!');
    process.exit(0);
  } else {
    console.log('\n💥 Some tests failed. Please check the output above.');
    process.exit(1);
  }
}

async function main() {
  // Show help if requested
  if (options.includes('--help') || testType === 'help') {
    showHelp();
    return;
  }

  // Check prerequisites
  checkPrerequisites();

  // Handle special case for running all tests
  if (testType === 'all') {
    await runAllTests();
    return;
  }

  // Run specific test type
  const config = testConfigs[testType];
  
  if (!config) {
    console.error(`❌ Unknown test type: ${testType}`);
    console.log('Run "npm run test:runner --help" for available options.');
    process.exit(1);
  }

  try {
    // Add any additional options
    const finalArgs = [...config.args, ...options.filter(opt => !opt.startsWith('--'))];
    await runCommand(config.command, finalArgs, config.description);
  } catch (error) {
    process.exit(1);
  }
}

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Run the main function
main().catch((error) => {
  console.error('❌ Test runner failed:', error.message);
  process.exit(1);
});
