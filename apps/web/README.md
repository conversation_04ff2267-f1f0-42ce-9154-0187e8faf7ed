<p align="center">
    <a href="https://bloomui.com" title="BloomUI.com">
        <img src="https://bloomui.s3.us-east-2.amazonaws.com/tokyo-logo.png" alt="Tokyo React Javascript Admin Dashboard">
    </a>
</p>
<h1 align="center">
    <b>Tokyo React Javascript Admin Dashboard</b>
    <br>
    <a href="https://twitter.com/intent/tweet?url=https://bloomui.com&text=I like this React admin dashboard">
        <img src="https://img.shields.io/twitter/url/http/shields.io.svg?style=social" />
    </a>
</h1>
<div align="center">

![version](https://img.shields.io/badge/version-3.0-blue.svg)

</div>

<h3 align="center">High performance React template built with lots of powerful MUI (Material-UI) components across multiple product niches for fast & perfect apps development processes.
</h3>

---

<h2>
    Documentation
</h2>

<p>To view the available online documentation files please visit the following link:
<a href="https://tokyo.bloomui.com/docs" title="Click to view the online documentation">
    https://tokyo.bloomui.com/docs
</a>
</p>

---

<h2>
    Technical Support
</h2>
<p>
    You can open a support ticket by sending an email here: <a href="mailto:<EMAIL>" title="Open Support Ticket">
        <EMAIL>
    </a>
</p>

---

<h2>
    Quick Start
</h2>
<p>
    Make sure you have the latest stable versions for Node.js and NPM installed. After that, run <code>npm install</code> inside the project-folder and after the install finishes, run <code>npm run start</code>. A browser window will open and you will see the live preview.
</p>
<p>
    We recommend browsing through the online documentation we've prepared for Tokyo React Javascript Admin Dashboard.
</p>
