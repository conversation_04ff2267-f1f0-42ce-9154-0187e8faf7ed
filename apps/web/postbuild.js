const zipFolder = require('zip-folder');
const mv = require('mv');
const fs = require('fs');
const fse = require('fs-extra');
const { exec } = require('child_process');
const chalk = require('chalk');
const prompts = require('prompts');

const folderName = 'eh-customer-portal';
const suiteCloudProjectFolder =
  'backend/src/FileCabinet/SuiteApps/com.newgennow.csexhibithouse/CSEH-Web';

if (fs.existsSync(`./${folderName}`)) {
  console.log('Folder already exists:', folderName);
} else fs.mkdirSync(`./${folderName}`);

if (fs.existsSync(`../${suiteCloudProjectFolder}/${folderName}`)) {
  console.log('SuiteCloud folder already exists:', folderName);
  fse.removeSync(`../${suiteCloudProjectFolder}/${folderName}`);
} else fs.mkdirSync(`../${suiteCloudProjectFolder}/${folderName}`);

const args = process.argv;
const ciFlags = ['--ci', '-CI'];
const uploadFlags = ['--upload', '-U'];
let ciFlag = false;
let uploadFlag = false;

async function getCmdFlags() {
  args.forEach((arg) => {
    if (ciFlags.includes(arg)) ciFlag = true;
    if (uploadFlags.includes(arg)) uploadFlag = true;
  });
  return true;
}

// console.log('Process args:', args)
let uploadToNetsuite = false;

async function copyToSuiteCloud() {
  let folderReady = false;
  fs.exists('build', async (exists) => {
    if (exists) {
      console.log('Build folder exists!');
      await fse.copy(
        './build',
        `../${suiteCloudProjectFolder}/${folderName}`,
        async function (err, succ) {
          if (err) {
            console.log('Error encountered copying to SuiteCloud folder!', err);
            folderReady = false;
          } else {
            // Copied to NS file cabinet folder - check for ci or upload flags to skip prompt
            console.log(
              chalk.green('Copied build to suiteCloud project successfully! ✔')
            );

            uploadToNetsuite = await prompts({
              type: !ciFlag || !uploadFlag ? null : 'confirm',
              name: 'value',
              message: 'Would you like to upload to the file cabinet?',
              initial: false
            });

            if (uploadFlag || uploadToNetsuite?.value) {
              console.log(chalk.bold("I'll find the command for that soon!"));
              // Most likely will be an exec to call suitecloud-plugin
            } else if (ciFlag) {
              console.log(chalk.blue.bold('SKIPPING UPLOAD DUE TO CI FLAG...'));
            } else {
              console.log(
                chalk.cyan.bold(
                  'MAKE SURE TO UPLOAD FOLDER TO NETSUITE ACCOUNT!'
                )
              );
              console.log(
                'RUN',
                chalk.bold(' ALT + SHIFT + U '),
                'ON FOLDER',
                chalk.italic.blue(suiteCloudProjectFolder)
              );
            }

            await fse.copy(
              './build',
              `./${folderName}/${folderName}`,
              function (err, succ) {
                if (err) {
                  console.log('Error encountered copying to EH folder!', err);
                  folderReady = false;
                } else {
                  fs.writeFile(
                    `./${folderName}/${folderName}/generated-window-env.js`,
                    '',
                    (err) => {
                      if (err) throw err;
                      console.log('Window ENV file has been generated!');
                    }
                  );
                  fs.writeFile(
                    `../${suiteCloudProjectFolder}/${folderName}/generated-window-env.js`,
                    '',
                    (err) => {
                      if (err) throw err;
                      console.log(
                        'Window ENV file has been generated in suitecloud project!'
                      );
                    }
                  );
                  console.log(
                    chalk.green('Copied build to EH folder successfully! ✔')
                  );
                  console.log(chalk.yellow('Zipping build...'));
                  zipFolder(
                    `./${folderName}`,
                    `./${folderName}.zip`,
                    function (err) {
                      if (err) {
                        console.log('oh no!', err);
                      } else {
                        console.log(chalk.green('File zipped successfully! ✔'));
                      }
                    }
                  );
                  folderReady = true;
                }
              }
            );
            folderReady = true;
          }
        }
      );
    } else console.log('Build folder not found!');
  });
  return folderReady;
}

async function main() {
  await getCmdFlags()
    .then(() => copyToSuiteCloud())
    .catch((err) => console.log(err));
}

console.log('Initializing production build copy to SuiteCloud...');
try {
  main().catch((err) => console.log(err));
} catch (err) {
  console.error(err);
}
