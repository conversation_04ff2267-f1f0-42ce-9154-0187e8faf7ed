<!DOCTYPE html>
<html lang="en" xmlns:v="urn:schemas-microsoft-com:vml">
<head>
  <meta charset="utf-8">
  <meta name="x-apple-disable-message-reformatting">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="format-detection" content="telephone=no, date=no, address=no, email=no, url=no">
  <meta name="color-scheme" content="light dark">
  <meta name="supported-color-schemes" content="light dark">
  <!--[if mso]>
  <noscript>
    <xml>
      <o:OfficeDocumentSettings xmlns:o="urn:schemas-microsoft-com:office:office">
        <o:PixelsPerInch>96</o:PixelsPerInch>
      </o:OfficeDocumentSettings>
    </xml>
  </noscript>
  <style>
    td,th,div,p,a,h1,h2,h3,h4,h5,h6 {font-family: "Segoe UI", sans-serif; mso-line-height-rule: exactly;}
  </style>
  <![endif]-->
  <title>New Comments added to your project task</title>
  <style>
    @media (max-width: 600px) {
      .sm-my-8 {
        margin-top: 32px !important;
        margin-bottom: 32px !important
      }
      .sm-p-6 {
        padding: 24px !important
      }
      .sm-px-4 {
        padding-left: 16px !important;
        padding-right: 16px !important
      }
      .sm-py-32 {
        padding-top: 128px !important;
        padding-bottom: 128px !important
      }
      .sm-text-4xl {
        font-size: 36px !important
      }
    }
  </style>
</head>
<body style="margin: 0; width: 100%; background-color: #f8fafc; padding: 0; -webkit-font-smoothing: antialiased; word-break: break-word">
  <div role="article" aria-roledescription="email" aria-label="New Comments added to your project task" lang="en">
    <div class="sm-px-4" style="background-color: #f8fafc; font-family: ui-sans-serif, system-ui, -apple-system, 'Segoe UI', sans-serif">
      <table align="center" cellpadding="0" cellspacing="0" role="none">
        <tr>
          <td style="width: 720px; max-width: 100%">
            <div class="sm-my-8" style="margin-top: 48px; margin-bottom: 48px; display: flex; text-align: center">
              <section style="flex: 1 1 auto">
                <img src="https://tstdrv2149044.app.netsuite.com/core/media/media.nl?id=9377&c=TSTDRV2149044&h=8fQG4XWNzsFIAUoP-ORsKqESy2sT6Iyp7ua7NPeYX5F7Z_Xj" width="70" alt="Maizzle" style="max-width: 100%; vertical-align: middle; line-height: 1; border: 0">
              </section>
            </div>
            <table style="width: 100%;" cellpadding="0" cellspacing="0" role="none">
              <tr>
                <td class="sm-py-32" style="background-color: #fff; padding-top: 8px; padding-bottom: 8px">
                  <div style="margin-left: auto; margin-right: auto; max-width: 640px; padding-left: 24px; padding-right: 24px">
                    <div style="margin-left: auto; margin-right: auto; max-width: 336px">
                      <h4 class="sm-text-4xl" style="font-size: 18px; font-weight: 700; letter-spacing: -0.025em; color: #111827">Project: <span>${taskProject}</span></h4>
                      <h4 class="sm-text-4xl" style="font-size: 18px; font-weight: 700; letter-spacing: -0.025em; color: #111827">Task: <span>${taskName}</span></h4>
                      <p style="margin-top: 24px; font-size: 18px; line-height: 32px; color: #4b5563">
                        <span style="font-weight: 700;">Status:</span>
                        <span style="display: inline-flex; align-items: center; border-radius: 6px; background-color: rgb(156 163 175 / 0.1); padding: 4px 8px; font-size: 12px; font-weight: 500; color: #9ca3af; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-color: rgb(156 163 175 / 0.2)">${diffObject.prevValues.status}</span>
                        <span style="display: inline-flex; align-items: center; border-radius: 6px; background-color: rgb(96 165 250 / 0.1); padding: 4px 8px; font-size: 12px; font-weight: 500; color: #60a5fa; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-color: rgb(96 165 250 / 0.3)">Badge</span>
                        →
                        <span style="display: inline-flex; align-items: center; border-radius: 6px; background-color: rgb(34 197 94 / 0.1); padding: 4px 8px; font-size: 12px; font-weight: 500; color: #4ade80; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-color: rgb(34 197 94 / 0.2)">${diffObject.newValues.status}</span>
                      </p>
                      <br>
                      <p style="margin-top: 24px; font-size: 18px; line-height: 32px; color: #4b5563;">
                        <span style="font-weight: 700;">Sub-Status:</span>
                        <span style="display: inline-flex; align-items: center; border-radius: 6px; background-color: rgb(156 163 175 / 0.1); padding: 4px 8px; font-size: 12px; font-weight: 500; color: #9ca3af; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-color: rgb(156 163 175 / 0.2);">
                              ${diffObject.prevValues.status}
                            </span>
                        →
                        <span style="display: inline-flex; align-items: center; border-radius: 6px; background-color: rgb(129 140 248 / 0.1); padding: 4px 8px; font-size: 12px; font-weight: 500; color: #818cf8; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-color: rgb(129 140 248 / 0.3)">
                              ${diffObject.newValues.status}
                            </span>
                      </p>
                      <p style="margin-top: 24px; font-size: 18px; line-height: 32px; color: #4b5563;">Here are the last 5 messages posted...</p>
                    </div>
                  </div>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td>
            <div>
              <li style="position: relative; display: flex; column-gap: 16px">
                <div style="position: absolute; bottom: -24px; left: 0; top: 0; display: flex; width: 24px; justify-content: center">
                  <div style="width: 1px; background-color: #e5e7eb"></div>
                </div>
                <img src="${imageUrl}" alt style="max-width: 100%; vertical-align: middle; line-height: 1; border: 0; position: relative; margin-top: 12px; height: 24px; width: 24px; flex: none; border-radius: 9999px; background-color: #f9fafb">
                <span style="position: relative; margin-top: 12px; display: inline-flex; height: 24px; width: 24px; align-items: center; justify-content: center; border-radius: 9999px; background-color: #6b7280">
              <span style="font-size: 12px; font-weight: 500; line-height: 1; color: #fff">${initals}</span>
                </span>
                <div style="flex: 1 1 auto; border-radius: 6px; padding: 12px; --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color); --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color); box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000); --tw-ring-inset: inset; --tw-ring-opacity: 1; --tw-ring-color: rgb(229 231 235 / var(--tw-ring-opacity))">
                  <div style="display: flex; justify-content: space-between; column-gap: 16px">
                    <div style="padding-top: 2px; padding-bottom: 2px; font-size: 12px; line-height: 20px; color: #6b7280"><span style="font-weight: 500; color: #111827;">${authorName}</span> commented</div>
                    <time datetime="2023-01-23T15:56" style="flex: none; padding-top: 2px; padding-bottom: 2px; font-size: 12px; line-height: 20px; color: #6b7280;">${message.created}</time>
                  </div>
                  <p style="font-size: 14px; line-height: 24px; color: #6b7280">${message.custrecord_ng_eh_cs_chat_content}</p>
                </div>
              </li>
            </div>
          </td>
        </tr>
        <tr>
          <td style="width: 100%;">
            <div class="sm-p-6" style="padding: 20px 16px">
              <div style="margin-top: 40px; display: flex; align-items: center; column-gap: 24px">
                <div>
                  <a href="${taskUrl}" style="display: inline-block; border-radius: 6px; background-color: #4338ca; padding: 16px 24px; font-size: 16px; font-weight: 600; line-height: 1; color: #f8fafc; text-decoration: none">
                    <!--[if mso]>
      <i style="mso-font-width: -100%; letter-spacing: 32px; mso-text-raise: 30px" hidden>&nbsp;</i>
    <![endif]-->
                    <span style="mso-text-raise: 16px">View Task</span>
                    <!--[if mso]>
      <i style="mso-font-width: -100%; letter-spacing: 32px;" hidden>&nbsp;</i>
    <![endif]-->
                  </a>
                </div>
              </div>
            </div>
          </td>
        </tr>
      </table>
    </div>
  </div>
</body>
</html>
