<scheduledscript scriptid="customscript_ng_eh_ss_relate_trans_tsks">
  <description>This is a task queue for project tasks ready to be related to their transaction line.</description>
  <isinactive>F</isinactive>
  <name>NG[EH]-SS Relate Transaction Line Tasks</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/scheduled/ng_eh_ss_relate_transaction_line_tasks.js]</scriptfile>
  <scriptcustomfields>
    <scriptcustomfield scriptid="custscript_tranid">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Transaction ID</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_transtype">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Transaction Type</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_tasks">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CLOBTEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Tasks</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
  </scriptcustomfields>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_ss_relate_trans_tsks">
      <custscript_tasks></custscript_tasks>
      <custscript_tranid></custscript_tranid>
      <custscript_transtype></custscript_transtype>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG SS Relate Transaction Line Tasks</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2023-02-28</startdate>
          <starttime>01:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
  </scriptdeployments>
</scheduledscript>