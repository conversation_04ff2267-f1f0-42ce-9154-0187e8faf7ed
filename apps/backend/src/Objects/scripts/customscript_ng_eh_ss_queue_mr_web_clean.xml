<scheduledscript scriptid="customscript_ng_eh_ss_queue_mr_web_clean">
  <description>This will queue the map/reduce script to execute the cleanup process.</description>
  <isinactive>F</isinactive>
  <name>NG[EH]-SS Queue Bulk Web Clean</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/scheduled/ng_eh_ss_queue_mr_web_clean.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_ss_queue_mr_web_clean">
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG EH Queue Bulk Web Clean</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2022-11-18</startdate>
          <starttime>01:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
  </scriptdeployments>
</scheduledscript>