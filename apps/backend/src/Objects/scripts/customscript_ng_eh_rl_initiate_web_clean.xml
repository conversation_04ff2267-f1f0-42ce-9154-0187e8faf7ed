<restlet scriptid="customscript_ng_eh_rl_initiate_web_clean">
  <description>This REST communication call for the Scheduled script to initial a M/R script to clean up the portal files before a bundle update.</description>
  <isinactive>F</isinactive>
  <name>NG[EH 🟢]-RL Initiate Web Clean</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/REST/ng_eh_rl_start_web_clean.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_rl_initiate_web_clean">
      <allemployees>T</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole>CUSTOMER_CENTER|EMPLOYEE_CENTER|NETSUITE_SUPPORT_CENTER|NETSUITE_SUPPORT_CENTER__BASIC|ONLINE_FORM_USER|PARTNER_CENTER|SHOPPER|VENDOR_CENTER</audslctrole>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>RELEASED</status>
      <title>NG RL Initiate Web Clean</title>
    </scriptdeployment>
  </scriptdeployments>
</restlet>