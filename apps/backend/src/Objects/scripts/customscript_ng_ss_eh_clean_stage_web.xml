<scheduledscript scriptid="customscript_ng_ss_eh_clean_stage_web">
  <description>This cleans the web folder prior to bundle install</description>
  <isinactive>F</isinactive>
  <name>NG[EH]-SS Web Clean Stage 1</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/scheduled/ng_ss_eh_clean_stage_web.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_ss_eh_clean_stage_web">
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>NOTSCHEDULED</status>
      <title>NG EH Web Clean Stage 1</title>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2022-08-26</startdate>
          <starttime>01:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
  </scriptdeployments>
</scheduledscript>