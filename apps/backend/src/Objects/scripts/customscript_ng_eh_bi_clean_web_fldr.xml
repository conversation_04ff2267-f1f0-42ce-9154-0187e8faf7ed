<bundleinstallationscript scriptid="customscript_ng_eh_bi_clean_web_fldr">
  <description>Cleans up web folder components for installation automation</description>
  <isinactive>F</isinactive>
  <name>NG[EH ❎]-BI Clean Up EH Web Folder</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/install/ng_eh_cleanwebfolder.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_bi_clean_web_fldr">
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
      <title>Clean Up EH Web Folder</title>
    </scriptdeployment>
  </scriptdeployments>
</bundleinstallationscript>