<mapreducescript scriptid="customscript_ng_eh_mr_bulk_strg_inv_gen">
  <description></description>
  <isinactive>F</isinactive>
  <name>NG[EH 🟢]-MR Bulk Storage Invoice Gen</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/map-reduce/ng_eh_mr_bulk_storage_inv_generator.js]</scriptfile>
  <scriptcustomfields>
    <scriptcustomfield scriptid="custscript_strg_inv_customers">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>CLOBTEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Customers</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_strg_inv_memo">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Memo</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_strg_inv_date">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>DATE</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>Inv Date</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
    <scriptcustomfield scriptid="custscript_strg_inv_emailed">
      <accesslevel>2</accesslevel>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth>40</displaywidth>
      <dynamicdefault></dynamicdefault>
      <fieldtype>TEXT</fieldtype>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <label>To Be Emailed</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <setting></setting>
      <storevalue>T</storevalue>
    </scriptcustomfield>
  </scriptcustomfields>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_mr_bulk_strg_inv_gen">
      <buffersize>1</buffersize>
      <concurrencylimit>1</concurrencylimit>
      <custscript_strg_inv_customers></custscript_strg_inv_customers>
      <custscript_strg_inv_date></custscript_strg_inv_date>
      <custscript_strg_inv_emailed></custscript_strg_inv_emailed>
      <custscript_strg_inv_memo></custscript_strg_inv_memo>
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <queueallstagesatonce>T</queueallstagesatonce>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>NOTSCHEDULED</status>
      <title>NG[EH 🟢]-MR Bulk Storage Invoice Gen</title>
      <yieldaftermins>60</yieldaftermins>
      <recurrence>
        <single>
          <repeat></repeat>
          <startdate>2025-04-17</startdate>
          <starttime>01:00:00Z</starttime>
        </single>
      </recurrence>
    </scriptdeployment>
  </scriptdeployments>
</mapreducescript>