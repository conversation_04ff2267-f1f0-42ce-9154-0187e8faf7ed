<sdfinstallationscript scriptid="customscript_ng_eh_si_generate_login_pg">
  <description>This SDF install script will generate the styled portal login page</description>
  <isinactive>F</isinactive>
  <name>NG[EH]-SI Build Web Login Page - Stage 2</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/install/ng_eh_si_generate_login_file.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_eh_si_generate_login_pg">
      <isdeployed>T</isdeployed>
      <loglevel>DEBUG</loglevel>
      <status>RELEASED</status>
      <title>NG EH Build Portal Login Page</title>
    </scriptdeployment>
  </scriptdeployments>
</sdfinstallationscript>