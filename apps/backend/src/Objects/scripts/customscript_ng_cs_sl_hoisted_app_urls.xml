<suitelet scriptid="customscript_ng_cs_sl_hoisted_app_urls">
  <description>Hoists together all useful application urls that will be used in the web portal.</description>
  <isinactive>F</isinactive>
  <name>NG[EH 🟢]-SL Hoisted Portal Urls</name>
  <notifyadmins>F</notifyadmins>
  <notifyemails></notifyemails>
  <notifyowner>T</notifyowner>
  <notifyuser>F</notifyuser>
  <scriptfile>[/SuiteApps/com.newgennow.csexhibithouse/CSEH-Scripting/suitelets/web/ng_cs_sl_hoisted_portal_urls.js]</scriptfile>
  <scriptdeployments>
    <scriptdeployment scriptid="customdeploy_ng_cs_sl_hoisted_app_urls">
      <allemployees>F</allemployees>
      <allpartners>F</allpartners>
      <allroles>T</allroles>
      <audslctrole>CUSTOMER_CENTER|EMPLOYEE_CENTER|NETSUITE_SUPPORT_CENTER|NETSUITE_SUPPORT_CENTER__BASIC|ONLINE_FORM_USER|PARTNER_CENTER|SHOPPER|VENDOR_CENTER</audslctrole>
      <eventtype></eventtype>
      <isdeployed>T</isdeployed>
      <isonline>T</isonline>
      <loglevel>DEBUG</loglevel>
      <runasrole>ADMINISTRATOR</runasrole>
      <status>RELEASED</status>
      <title>NG[EH 🟢]-SL Hoisted Portal Urls</title>
    </scriptdeployment>
  </scriptdeployments>
</suitelet>