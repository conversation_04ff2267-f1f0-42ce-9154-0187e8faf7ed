<transactionForm scriptid="custform_ng_eh_wip_invt_adjst" standard="STANDARDINVENTORYADJUSTMENT">
  <name>EH WIP Inventory Adjustment</name>
  <recordType>INVENTORYADJUSTMENT</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANID</id>
          <label>Reference #</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CUSTOMER</id>
          <label>Customer</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ACCOUNT</id>
          <label>Adjustment Account</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ESTIMATEDTOTALVALUE</id>
          <label>Estimated Total Value</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANDATE</id>
          <label>Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>POSTINGPERIOD</id>
          <label>Posting Period</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>MEMO</id>
          <label>Memo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANSACTIONNUMBER</id>
          <label>Transaction Number</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>GENERATETRANIDONSAVE</id>
          <label>Generate Document Number on Save</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>UNCHECKED</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_iswipaccrual]</id>
          <label>Generated WIP Accrual Entry</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="classification">
      <label>Classification</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>SUBSIDIARY</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>DEPARTMENT</id>
          <label>Department</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CLASS</id>
          <label>Class</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LOCATION</id>
          <label>Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ADJLOCATION</id>
          <label>Adjustment Location</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[appid=com.netsuite.featureactivitycodes, type=transactionbodycustomfield, scriptid=cseg_paactivitycode]</id>
          <label>Activity Code</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="intercompanymanagement">
      <label>Intercompany Management</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>[appid=com.netsuite.purchase2project, scriptid=custbody_p2p_txn_vendor_filter]</id>
          <label>Project Vendor</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_estimate_showbtn]</id>
          <label>Show Customer Approval Button</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_estimate_apprvd]</id>
          <label>Customer Estimate Approved</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_apprved_date]</id>
          <label>Customer Estimate Approved Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_pay_link_url]</id>
          <label>Payment Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_conv_fee_exempt]</id>
          <label>Convenience Fee Exempt</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_deposit_link_url]</id>
          <label>Deposit Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_customer_contact]</id>
          <label>Customer Contact</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_amount_recieved]</id>
          <label>Amount Recieved</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_balance]</id>
          <label>Balance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_show_quote_detail_lines]</id>
          <label>Show Detail Lines on PDF</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_division]</id>
          <label>Division</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_conv_fee_invoice]</id>
          <label>Convenience Fee Invoice</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paygen_edi_transaction_id]</id>
          <label>PayGen EDI Transaction ID</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_ship_to_loc]</id>
          <label>Ship To Location</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_ro_quick_ship]</id>
          <label>RO Quick Ship</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>ADJUSTMENTS</id>
      <label>Adjustments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ITEM</id>
          <neverEmpty>F</neverEmpty>
          <columns>
            <column>
              <id>ITEM</id>
              <label>Item</label>
              <visible>T</visible>
            </column>
            <column>
              <id>DISPLAYNAME</id>
              <label>Display Name</label>
              <visible>F</visible>
            </column>
            <column>
              <id>DESCRIPTION</id>
              <label>Description</label>
              <visible>T</visible>
            </column>
            <column>
              <id>LOCATION</id>
              <label>Location</label>
              <visible>T</visible>
            </column>
            <column>
              <id>UNITS</id>
              <label>Units</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITYONHAND</id>
              <label>Qty. On Hand</label>
              <visible>T</visible>
            </column>
            <column>
              <id>CURRENTVALUE</id>
              <label>Current Value</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ADJUSTQTYBY</id>
              <label>Adjust Qty. By</label>
              <visible>T</visible>
            </column>
            <column>
              <id>NEWQUANTITY</id>
              <label>New Quantity</label>
              <visible>T</visible>
            </column>
            <column>
              <id>UNITCOST</id>
              <label>Est. Unit Cost</label>
              <visible>T</visible>
            </column>
            <column>
              <id>INVENTORYDETAIL</id>
              <label>Inventory Detail</label>
              <visible>T</visible>
            </column>
            <column>
              <id>DEPARTMENT</id>
              <label>Department</label>
              <visible>T</visible>
            </column>
            <column>
              <id>CLASS</id>
              <label>Class</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_est_origin_department]</id>
              <label>Division Origin</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_estimate_origin]</id>
              <label>Estimate Origin</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_item_category]</id>
              <label>Item Category</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_markup_template]</id>
              <label>Markup Template</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_markup]</id>
              <label>Markup</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_loc]</id>
              <label>Rental Location</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_start_date]</id>
              <label>Rental Start Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_end_date]</id>
              <label>Rental End Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_project_task_created]</id>
              <label>Project Task Created</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_rental_item_returned]</id>
              <label>Returned To Stock</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_availability]</id>
              <label>Rental Availability</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_assoc_proj]</id>
              <label>Associated Project</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_vendor_line]</id>
              <label>Vendor</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_show_on_pdf]</id>
              <label>Show on PDF</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_rental_available_status]</id>
              <label>Rental Availability Status</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_ord_confrm_pdf_url]</id>
              <label>Primary Image URL</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_wip_cleared]</id>
              <label>WIP Cleared</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_graphic_calculator]</id>
              <label>Open Graphic Calculator</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_check_out_txn]</id>
              <label>Check Out Txn</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_start_date]</id>
              <label>Rentals Start Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_end_date]</id>
              <label>Rentals End Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_rts_date]</id>
              <label>Rentals RTS Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_check_in_txn]</id>
              <label>Check In Txn</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rentals_availability]</id>
              <label>Rentals Availability</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.netsuite.featureactivitycodes, type=transactioncolumncustomfield, scriptid=cseg_paactivitycode]</id>
              <label>Activity Code</label>
              <visible>T</visible>
            </column>
          </columns>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ACTIVITIES</id>
          <label>Activities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTS</id>
          <label>Events</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>TASKS</id>
          <label>Tasks</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>CALLS</id>
          <label>Phone Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>MEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>USERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONSYSTEMINFORMATION</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_master_estimate]</id>
              <label>Master Estimate</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>SYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>WORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>CUSTOM</id>
      <label>Custom</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_html_proposal_link]</id>
              <label>Estimate Approval Link</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[bundleid=532268, scriptid=custbody_ng_pt_amount_due]</id>
              <label>Amount Due</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_est_was_revised]</id>
              <label>Was Revised</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_copy_address_btns]</id>
              <label>Copy CS Address to Ship To Field Buttons</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_is_web_order]</id>
              <label>Is Web Order</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_disable_reports_center]</id>
              <label>Disable In Portal Reports Center</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_wip_cleared]</id>
              <label>WIP Cleared</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_wip_auto_exp_acc]</id>
              <label>WIP Accrual Automation Expense Account</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_storage_invoice]</id>
              <label>Storage Invoice</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_rel_rental_order]</id>
              <label>Related Rental Order</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>GLIMPACTTAB</id>
      <label>GL Impact</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_transact_scope]</id>
      <label>Scoping</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_event_address]</id>
              <label>Event Site Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_new_build_out_plan]</id>
              <label>Space Description</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_booth_size]</id>
              <label>Booth Size</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_proj_type]</id>
              <label>Project Type</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_location]</id>
              <label>Event Site Location</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_show_svcs_deadline]</id>
              <label>Discount Deadline</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_mat_arrival]</id>
              <label>Material Arrival</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_move_in_date]</id>
              <label>Move In Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_event_start]</id>
              <label>Event Start Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_event_finish]</id>
              <label>Event End Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_move_out_date]</id>
              <label>Move Out Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_event_name]</id>
              <label>Event Name</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_mat_pickup]</id>
              <label>Material Pickup</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_ship_date]</id>
              <label>Ship Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_return_date]</id>
              <label>Return Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_proj_template]</id>
              <label>Project Template</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_view_templates_link]</id>
              <label>View Templates</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_booth_number]</id>
              <label>Booth Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_adv_warehouse]</id>
              <label>Advanced Warehouse Location</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_adv_wh_address]</id>
              <label>Advanced Warehouse Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>[scriptid=custtab_ng_eh_evnt_exhbt_info]</id>
          <label>Event/Exhibit Information</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup/>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GLIMPACT</id>
        <label>GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTGLIMPACT</id>
        <label>Print GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>ACTIVITYHISTORY</id>
        <label>Show Activity</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <allowAddMultiple>F</allowAddMultiple>
  <emailMessageTemplate>USE_DEFAULT</emailMessageTemplate>
  <roles>
    <role>
      <id>ACCOUNTANT</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ACCOUNTANT__REVIEWER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_eh_accountant_test]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ADMINISTRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>BOOKKEEPER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO_HANDS_OFF</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE41</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_eh_cust_admin]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>FULL_ACCESS</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>WAREHOUSE_MANAGER</id>
      <preferred>F</preferred>
    </role>
  </roles>
</transactionForm>
