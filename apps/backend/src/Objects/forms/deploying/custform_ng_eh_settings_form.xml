<entryForm scriptid="custform_ng_eh_settings_form" standard="STANDARDCUSTOMRECORD_NG_EH_SETTINGSFORM">
  <name>Primary EH Settings Form</name>
  <recordType>[scriptid=customrecord_ng_eh_settings]</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>F</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>NAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>RECORDID</id>
          <label>ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>OWNER</id>
          <label>Owner</label>
          <visible>F</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CREATED</id>
          <label>Date Created</label>
          <visible>F</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LASTMODIFIED</id>
          <label>Last Modified</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ISINACTIVE</id>
          <label>Inactive</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PARENT</id>
          <label>Parent</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>[scriptid=customrecord_ng_eh_settings.tab_188_t2149044_160]</id>
      <label>General</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_3_t2149044_450">
          <label>Rental Inventory</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_cs_ri_order_forms]</id>
              <label>RI Order Form Selection</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_2_t2149044_921">
          <label>Rental Transfer Dates</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_10_t2149044_468">
          <label>Quoting</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_8_t2149044_259">
          <label>Projects</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_prj_template]</id>
              <label>Default Project Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_portal_blurb]</id>
              <label>Default Project Portal Blurb</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_chat_notification_author]</id>
              <label>Chat Notification Author</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_enable_project_task_sub_statu]</id>
              <label>Enable Project Task Sub-Status</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_print_proj_summary]</id>
              <label>Enable Print Project Summary</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proj_summ_prnt_temp]</id>
              <label>Project Summary Print Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proj_summ_advpdf_temp]</id>
              <label>Project Summary Adv/PDF Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proj_sum_email]</id>
              <label>Enable Project Summary Email</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proj_sum_email_temp]</id>
              <label>Project Summary Email Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_ext_prj_options]</id>
              <label>Enabled Extended Project Create Options</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_ext_prj_copy_options]</id>
              <label>Enabled Extended Project Copy Options</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_copy_add_frm_proj]</id>
              <label>Copy Addresses From Project</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_enable_cal_event_auto]</id>
              <label>Enable Calendar Event Automation</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_project_subsidiary]</id>
              <label>Project Subsidiary</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_11_t2149044_259">
          <label>Transaction Numbering</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_12_t2149044_727">
          <label>Deposit %</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_4_t2149044_398">
          <label>Other</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_settings_access]</id>
              <label>Additional Settings Access</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_24_t2149044_606">
          <label>Project Tasks</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_enable_project_task_prt]</id>
              <label>Enable Project Task Print</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_project_task_print_temp]</id>
              <label>Project Task Print Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_prjt_tsk_email_assignee]</id>
              <label>Enable Project Task Assignee Email </label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_prj_task_email_temp_add]</id>
              <label>Project Task Email Template (Add Assignee)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_prj_task_email_temp_remove]</id>
              <label>Project Task Email Template (Remove Assignee)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_ng_eh_settings.tab_transactions_config]</id>
      <label>Transactions</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_13_t2149044_443">
          <label>Estimates</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_copy_opp_num_to_est]</id>
              <label>Copy Opportunity Number to Estimate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_copy_est_num_to_ord]</id>
              <label>Copy Estimate Number to Order</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_use_est_version_suffix]</id>
              <label>Use Estimate Auto-Versioning Suffix (XX-A, XX-B, XX-C...)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_enable_master_estimate]</id>
              <label>Enable Master Estimate Generator</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_show_all_tran_lines]</id>
              <label>Automatically Show All Lines on Transaction Edit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_takeoff_source_id]</id>
              <label>Transaction Line Column Set (Estimate Takeoff)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_takeoff_save_by_default]</id>
              <label>Save To Transaction Line By Default (Estimate Takeoff)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_18_t2149044_816">
          <label>Estimate Approval Link</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proposal_html_file]</id>
              <label>Estimate Approval HTML file</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_proposal_redirect]</id>
              <label>Suitelet Redirect (From Proposal)</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_25_t2149044_572">
          <label>Add Line Item Scripting</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_add_item_form]</id>
              <label>Add Item Form ID Listing</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_14_t2149044_321">
          <label>Sales Orders</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_15_t2149044_989">
          <label>Deposits</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_deposit_percent_helper]</id>
              <label>Deposit % Mapping Helper Text</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_use_default_deposit_pct]</id>
              <label>Use Default Deposit %</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_set_dep_pct_from_terms]</id>
              <label>Set Deposit % From Terms</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_deposit_pct]</id>
              <label>Default Deposit %</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_27_t2149044_680">
          <label>WIP Automation Tool</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_exp_account]</id>
              <label>Default WIP Expense Account</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_wip_prj_sts]</id>
              <label>Default WIP Project Status</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_22_t2149044_460">
          <label>Sales Order Print Out Form</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_sales_order_printout_form]</id>
              <label>SO Pick Ticket Form</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_23_t2149044_751">
          <label>Line Color Settings</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_color_trans_lines_edit]</id>
              <label>Enable Colors On Transaction Lines In Edit</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_color_trans_lines_view]</id>
              <label>Enable Colors On Transaction Lines In View</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_discount_color_picker]</id>
              <label>Discount Color Picker</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_trans_disc_color]</id>
              <label>Discount Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_markup_color_picker]</id>
              <label>Markup Color Picker</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_trans_markup_color]</id>
              <label>Markup Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_subtotal_color_picker]</id>
              <label>Subtotal Color Picker</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_trans_subtotal_color]</id>
              <label>Subtotal Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_desc_color_picker]</id>
              <label>Description Color Picker</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_trans_desc_color]</id>
              <label>Description Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_ng_eh_settings.tab_rentals_config]</id>
      <label>Rentals</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_16_t2149044_483">
          <label>Transfer Dates</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_ship_date_days_before]</id>
              <label>Ship Date Days Before Material Arrival Date</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_return_date_days_after]</id>
              <label>Return Date Days After Material Pickup Date</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_17_t2149044_445">
          <label>Rental Activity</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_ri_order_forms]</id>
              <label>Rental Inventory Transaction Forms</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_inv_ship_loc]</id>
              <label>Default Inventory Shipment Location</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_rts_enabled]</id>
              <label>Enable Return To Stock</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=customrecord_ng_eh_settings.tab_189_t2149044_828]</id>
      <label>Portal</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_5_t2149044_612">
          <label>Suitelets</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_6_t2149044_726">
          <label>Images</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_react_logo_image_url]</id>
              <label>Logo Image URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_react_logo_image]</id>
              <label>Logo Image</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_item_fallback_img_url]</id>
              <label>Item Fallback Image URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_item_fallback_img]</id>
              <label>Item Fallback Image</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_fallback_cover_img_url]</id>
              <label>Cover Image Fallback URL</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_react_fallback_cover]</id>
              <label>Cover Image Fallback </label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_9_t2149044_344">
          <label>Login Screen</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_login_screen_text]</id>
              <label>Portal Login Screen Header Text</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_login_splash_img]</id>
              <label>Login Splash Image</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_login_splash_url]</id>
              <label>Login Splash Image Url</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_7_t2149044_423">
          <label>Other</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_react_checkout_tos]</id>
              <label>Portal Checkout TOS</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_react_google_api_key]</id>
              <label>Portal Google API Key</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_default_portal_ordform]</id>
              <label>Default Portal Sales Order Form</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_sales_order_printing_te]</id>
              <label>Sales Order Printing Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_invoice_printing_temp]</id>
              <label>Invoice Printing Template</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_disable_ship_bill_add]</id>
              <label>Hide Shipping Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_19_t2149044_350">
          <label>Permissions</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_disable_rental_items]</id>
              <label>Disable Web Rental Items</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_disable_inventory_items]</id>
              <label>Disable Web Customer Inventory Items</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_enable_web_project_perm]</id>
              <label>Enable Web Project Permissions</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_26_t2149044_935">
          <label>Reports Center</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_disable_recent_orders]</id>
              <label>Disable Recent Sales Orders</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_disable_recent_invoices]</id>
              <label>Disable Recent Invoices</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_20_t2149044_225">
          <label>Theming</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_primary_web_color]</id>
              <label>Primary Web Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_web_primary_html]</id>
              <label>Web Primary Color HTML</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_secondary_web_color]</id>
              <label>Secondary Web Color</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_web_secondary_html]</id>
              <label>Web Secondary Color HTML</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_web_dark_brand]</id>
              <label>Theme Mode</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_ng_eh_web_dark_switch_html]</id>
              <label>Activate Dark Mode Switch HTML</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=customrecord_ng_eh_settings.custrecord_enable_cases_in_portal]</id>
              <label>Enable Cases In Portal</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>RECORDNOTES</id>
      <label>Notes</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDMEDIA</id>
      <label>Files</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDWORKFLOW</id>
      <label>Workflow</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDCUSTOM</id>
      <label>Custom</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITCOPY</id>
        <label>Save &amp; Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
