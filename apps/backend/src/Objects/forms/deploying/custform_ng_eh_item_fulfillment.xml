<transactionForm scriptid="custform_ng_eh_item_fulfillment" standard="STANDARDITEMFULFILLMENT">
  <name>CS EH Item Fulfillment</name>
  <recordType>ITEMFULFILLMENT</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANID</id>
          <label>Ref. No.</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ENTITY</id>
          <label>Customer</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CREATEDFROM</id>
          <label>Created From</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>DISABLED</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANDATE</id>
          <label>Date</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>POSTINGPERIOD</id>
          <label>Posting Period</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>MEMO</id>
          <label>Memo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANSACTIONNUMBER</id>
          <label>Transaction Number</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>GENERATETRANIDONSAVE</id>
          <label>Generate TranId on Save</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>UNCHECKED</checkBoxDefault>
        </field>
        <field>
          <id>INCOTERM</id>
          <label>Incoterm</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>DISABLED</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="classification">
      <label>Classification</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>DEPARTMENT</id>
          <label>Department</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CLASS</id>
          <label>Class</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LOCATION</id>
          <label>Location</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TRANSFERLOCATION</id>
          <label>Destination Location</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>DISABLED</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PARTNER</id>
          <label>Partner</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="intercompanymanagement">
      <label>Intercompany Management</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=custbody_ng_eh_show_quote_detail_lines]</id>
          <label>Show Detail Lines</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[appid=com.netsuite.purchase2project, scriptid=custbody_p2p_txn_vendor_filter]</id>
          <label>Project Vendor</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_estimate_apprvd]</id>
          <label>Customer Estimate Approved</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_apprved_date]</id>
          <label>Customer Estimate Approved Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_cstmer_estimate_showbtn]</id>
          <label>Show Customer Approval Button</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_conv_fee_exempt]</id>
          <label>Convenience Fee Exempt (2)</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_customer_contact]</id>
          <label>Customer Contact</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_pay_link_url]</id>
          <label>Payment Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_deposit_link_url]</id>
          <label>Deposit Link URL</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_amount_recieved]</id>
          <label>Amount Recieved</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_balance]</id>
          <label>Balance</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_division]</id>
          <label>Division</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paytrace_conv_fee_invoice]</id>
          <label>Convenience Fee Invoice</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[bundleid=532268, scriptid=custbody_ng_paygen_edi_transaction_id]</id>
          <label>PayGen EDI Transaction ID</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_ship_to_loc]</id>
          <label>Ship To Location</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custbody_ng_eh_iswipaccrual]</id>
          <label>Generated WIP Accrual Entry</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
        <field>
          <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_ro_quick_ship]</id>
          <label>RO Quick Ship</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>TRANSACTIONITEMS</id>
      <label>Items</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ITEM</id>
          <neverEmpty>F</neverEmpty>
          <columns>
            <column>
              <id>JOB</id>
              <label>Project</label>
              <visible>F</visible>
            </column>
            <column>
              <id>ITEM</id>
              <label>Item</label>
              <visible>T</visible>
            </column>
            <column>
              <id>DISPLAYNAME</id>
              <label>Display Name</label>
              <visible>F</visible>
            </column>
            <column>
              <id>DESCRIPTION</id>
              <label>Description</label>
              <visible>F</visible>
            </column>
            <column>
              <id>DEPARTMENT</id>
              <label>Department</label>
              <visible>F</visible>
            </column>
            <column>
              <id>CLASS</id>
              <label>Class</label>
              <visible>F</visible>
            </column>
            <column>
              <id>LOCATION</id>
              <label>Location</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITYONHAND</id>
              <label>On Hand</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITYREMAINING</id>
              <label>Remaining</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITYCOMMITTED</id>
              <label>Committed</label>
              <visible>T</visible>
            </column>
            <column>
              <id>QUANTITY</id>
              <label>Quantity</label>
              <visible>T</visible>
            </column>
            <column>
              <id>UNITS</id>
              <label>Units</label>
              <visible>T</visible>
            </column>
            <column>
              <id>INVENTORYDETAIL</id>
              <label>Inventory Detail</label>
              <visible>T</visible>
            </column>
            <column>
              <id>CREATEPO</id>
              <label>Drop Ship PO</label>
              <visible>F</visible>
            </column>
            <column>
              <id>OPTIONS</id>
              <label>Options</label>
              <visible>T</visible>
            </column>
            <column>
              <id>RATE</id>
              <label>Rate</label>
              <visible>T</visible>
            </column>
            <column>
              <id>ISDROPSHIPMENT</id>
              <label>Drop Shipment</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_item_category]</id>
              <label>Item Category</label>
              <visible>F</visible>
            </column>
            <column>
              <id>EXCLUDEFROMRATEREQUEST</id>
              <label>Exclude Item from Rate Request</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_assoc_proj]</id>
              <label>Associated Project</label>
              <visible>T</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_markup_template]</id>
              <label>Markup Template</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_markup]</id>
              <label>M/U</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_loc]</id>
              <label>Rental Location</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_start_date]</id>
              <label>Rental Start Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_end_date]</id>
              <label>Rental End Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_rental_availability]</id>
              <label>Rental Availability</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_cs_vendor_line]</id>
              <label>Vendor</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_show_on_pdf]</id>
              <label>Show on PDF</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_rental_available_status]</id>
              <label>Rental Availability Status</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_rental_item_returned]</id>
              <label>Returned To Stock</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_est_origin_department]</id>
              <label>Origin Division</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_project_task_created]</id>
              <label>Project Task Created</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_estimate_origin]</id>
              <label>Estimate Origin</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_ord_confrm_pdf_url]</id>
              <label>Order Confirmation PDF URL</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_wip_cleared]</id>
              <label>WIP Cleared</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_check_out_txn]</id>
              <label>Check Out Txn</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_start_date]</id>
              <label>Rentals Start Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_end_date]</id>
              <label>Rentals End Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rental_rts_date]</id>
              <label>Rentals RTS Date</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_check_in_txn]</id>
              <label>Check In Txn</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rentals_availability]</id>
              <label>Rentals Availability</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[scriptid=custcol_ng_eh_est_takeoff]</id>
              <label>Estimate Takeoff</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rentals_histroy]</id>
              <label>Rentals History</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rentals_availability_s]</id>
              <label>Rentals Availability Status</label>
              <visible>F</visible>
            </column>
            <column>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custcol_ng_rnt_rented_qty]</id>
              <label>Rented QTY</label>
              <visible>F</visible>
            </column>
          </columns>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONSHIPPING</id>
      <label>Shipping</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="shippinginformation">
          <label>Shipping Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>SHIPCARRIER</id>
              <label>Shipping Carrier</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPMETHOD</id>
              <label>Return Ship Via</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNTYPE</id>
              <label>Return Type</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNTYPEDESCRIPTION</id>
              <label>Return Type Description</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPMETHOD</id>
              <label>Shipping Method</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGCOST</id>
              <label>Shipping Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGCOST</id>
              <label>Handling Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGTAXCODE</id>
              <label>Shipping Tax Code</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGTAX1RATE</id>
              <label>Shipping Tax Rate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPINGTAX2RATE</id>
              <label>Shipping Tax Rate 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAXCODE</id>
              <label>Handling Tax Code</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAX1RATE</id>
              <label>Handling Tax Rate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>HANDLINGTAX2RATE</id>
              <label>Handling Tax Rate 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="shippingaddress">
          <label>Shipping Address</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>SHIPADDRESSLIST</id>
              <label>Address Select</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPCOMPANY</id>
              <label>Addressee</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPADDRESS</id>
              <label>Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPATTENTION</id>
              <label>Attention</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPOVERRIDE</id>
              <label>Override</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPADDR1</id>
              <label>Address 1</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPADDR2</id>
              <label>Address 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPADDR3</id>
              <label>Address 3</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPCITY</id>
              <label>City</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPSTATE</id>
              <label>State</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPZIP</id>
              <label>Zip</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPCOUNTRY</id>
              <label>Country</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPPHONE</id>
              <label>Phone</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>SHIPISRESIDENTIAL</id>
              <label>Residential Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="returnshippingaddress">
          <label>Return Address</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>RETURNADDRESSLIST</id>
              <label>Return Address Select</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPADDR1</id>
              <label>Address 1</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPPINGADDRESS</id>
              <label>Return Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNADDRESS</id>
              <label>Return Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPADDR2</id>
              <label>Address 2</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPCITY</id>
              <label>City</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPSTATE</id>
              <label>State</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPZIPCODE</id>
              <label>Zip</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>RETURNSHIPCOUNTRY</id>
              <label>Country</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>PACKAGES</id>
      <label>Packages</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>TRANSACTIONRELATIONSHIPS</id>
      <label>Relationships</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>CRMCONTACTS</id>
          <label>Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONCOMMUNICATION</id>
      <label>Communication</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>MESSAGES</id>
          <label>Messages</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ACTIVITIES</id>
          <label>Activities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>EVENTS</id>
          <label>Events</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>TASKS</id>
          <label>Tasks</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>CALLS</id>
          <label>Phone Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>MEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>USERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONSYSTEMINFORMATION</id>
      <label>System Information</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_master_estimate]</id>
              <label>Master Estimate</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>SYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>WORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>TRANSACTIONADDRESS</id>
      <label>Address</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>CUSTOM</id>
      <label>Custom</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_html_proposal_link]</id>
              <label>Estimate Approval Link</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[bundleid=532268, scriptid=custbody_ng_pt_amount_due]</id>
              <label>Amount Due</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_est_was_revised]</id>
              <label>Was Revised</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_copy_address_btns]</id>
              <label>Copy Advanced Warehouse to Shipping</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_is_web_order]</id>
              <label>Is Web Order</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_disable_reports_center]</id>
              <label>Disable In Portal Reports Center</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_wip_cleared]</id>
              <label>WIP Cleared</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_wip_auto_exp_acc]</id>
              <label>WIP Accrual Automation Expense Account</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_storage_invoice]</id>
              <label>Storage Invoice</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
              <checkBoxDefault>USE_FIELD_DEFAULT</checkBoxDefault>
            </field>
            <field>
              <id>[appid=com.newgennow.ngrentalsinventory, scriptid=custbody_ng_rnt_rel_rental_order]</id>
              <label>Related Rental Order</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>GLIMPACTTAB</id>
      <label>GL Impact</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_transact_scope]</id>
      <label>Scoping</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custbody_ng_eh_booth_number]</id>
              <label>Booth Number</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_adv_warehouse]</id>
              <label>Advanced Warehouse Location</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custbody_ng_eh_adv_wh_address]</id>
              <label>Advanced Warehouse Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>[scriptid=custtab_ng_eh_evnt_exhbt_info]</id>
          <label>Event/Exhibit Information</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>[scriptid=custbody_ng_eh_mat_arrival]</id>
                  <label>Target Move In</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_new_build_out_plan]</id>
                  <label>New Build Out Plan</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_booth_size]</id>
                  <label>Booth Size</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_move_in_date]</id>
                  <label>Installation Date</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_event_start]</id>
                  <label>Event Start</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_proj_type]</id>
                  <label>Rental/Purchase</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_event_finish]</id>
                  <label>Event Finish</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_location]</id>
                  <label>Location/Hall</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_move_out_date]</id>
                  <label>Dismantle Date</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_show_svcs_deadline]</id>
                  <label>Show Services Deadline</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_event_name]</id>
                  <label>Event Name</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_mat_pickup]</id>
                  <label>Material Pickup</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_ship_date]</id>
                  <label>Ship Date</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_return_date]</id>
                  <label>Return Date</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_proj_template]</id>
                  <label>Project Template</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_view_templates_link]</id>
                  <label>View Templates</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>[scriptid=custbody_ng_eh_event_address]</id>
                  <label>Event Address</label>
                  <visible>F</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
  </tabs>
  <actionbar>
    <buttons>
      <button>
        <id>SUBMITINV</id>
        <label>Bill</label>
        <visible>T</visible>
      </button>
      <button>
        <id>MARKPACKED</id>
        <label>Mark Packed</label>
        <visible>T</visible>
      </button>
    </buttons>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>GLIMPACT</id>
        <label>GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTETDCOPY</id>
        <label>Print ETD Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTGLIMPACT</id>
        <label>Print GL Impact</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTITEMLABELS</id>
        <label>Print Item Labels</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINTLABEL</id>
        <label>Print Label</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITBILL</id>
        <label>Save &amp; Bill</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SAVEPRINT</id>
        <label>Save &amp; Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SAVEANDPRINTLABEL</id>
        <label>Save &amp; Print Label</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITREFUND</id>
        <label>Save &amp; Refund</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>ACTIVITYHISTORY</id>
        <label>Show Activity</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <emailMessageTemplate>USE_DEFAULT</emailMessageTemplate>
  <roles>
    <role>
      <id>AR_CLERK</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>ADMINISTRATOR</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CEO_HANDS_OFF</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE41</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>[scriptid=customrole_ng_eh_cust_admin]</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMER_CENTER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>FULL_ACCESS</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>NETSUITE_SUPPORT_CENTER</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>CUSTOMROLE42</id>
      <preferred>F</preferred>
    </role>
    <role>
      <id>WAREHOUSE_MANAGER</id>
      <preferred>F</preferred>
    </role>
  </roles>
</transactionForm>
