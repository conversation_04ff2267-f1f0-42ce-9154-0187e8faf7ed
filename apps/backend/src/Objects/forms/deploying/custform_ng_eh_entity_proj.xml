<entryForm scriptid="custform_ng_eh_entity_proj" standard="STANDARDJOBFORM">
  <name>CS EH Project Form</name>
  <recordType>MFGPROJECT</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>F</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="primaryinformation">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>COMPANYNAME</id>
          <label>Project Name</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ENTITYID</id>
          <label>Project ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>AUTONAME</id>
          <label>Auto</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
        </field>
        <field>
          <id>ALTNAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>INLINETEXT</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
        </field>
        <field>
          <id>ENTITYSTATUS</id>
          <label>Project Status</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PARENT</id>
          <label>Client</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SUBSIDIARY</id>
          <label>Subsidiary</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CURRENCY</id>
          <label>Currency</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PROJECTMANAGER</id>
          <label>Job Manager</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>JOBTYPE</id>
          <label>Project Type</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custentity_ng_eh_project_manager]</id>
          <label>Project Manager</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PROJECTTEMPLATE</id>
          <label>Project Template</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>COMMENTS</id>
          <label>Comments</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CATEGORY</id>
          <label>Category</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>DEFAULTORDERPRIORITY</id>
          <label>Default Order Priority</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>IMAGE</id>
          <label>Image</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custentity_ng_eh_client_contact]</id>
          <label>Client Contact</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custentity_ng_eh_client_acct_rep]</id>
          <label>Client Account Rep</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="projectoverview">
      <label>Project Overview</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>STARTDATE</id>
          <label>Scheduled Start Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PROJECTEDENDDATE</id>
          <label>Estimated End Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDENDDATE</id>
          <label>Calculated End Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ALLOCATEDTIME</id>
          <label>Allocated Work</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ACTUALTIME</id>
          <label>Actual Work</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>TIMEREMAINING</id>
          <label>Remaining Work</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PLANNEDWORKBASELINE</id>
          <label>Planned Work Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ESTIMATEDTIME</id>
          <label>Initial Time Budget</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PERCENTTIMECOMPLETE</id>
          <label>Calculated Percent Complete</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PERCENTCOMPLETE</id>
          <label>Override Percent Complete</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PERCENTCOMPLETEBYRSRCALLOC</id>
          <label>Percent Complete by Allocated Work</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>LASTBASELINEDATE</id>
          <label>Last Baseline Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDWORK</id>
          <label>Calculated Work</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDWORKBASELINE</id>
          <label>Calculated Work Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PLANNEDWORK</id>
          <label>Planned Work</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>SCHEDULINGMETHOD</id>
          <label>Scheduling Method</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SCHEDULEDENDDATEBASELINE</id>
          <label>Scheduled End Date Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDSTARTDATEBASELINE</id>
          <label>Calculated Start Date Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CURRENCYFORMATSAMPLE</id>
          <label>Format Sample</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>OVERRIDECURRENCYFORMAT</id>
          <label>Override Currency Format</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>DISPLAYSYMBOL</id>
          <label>Currency Symbol</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SYMBOLPLACEMENT</id>
          <label>Symbol Placement</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>NUMBERFORMAT</id>
          <label>Number Format</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>NEGATIVENUMBERFORMAT</id>
          <label>Negative Number Format</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>STARTDATEBASELINE</id>
          <label>Start Date Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>PROJECTEDENDDATEBASELINE</id>
          <label>Estimated End Date Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDENDDATEBASELINE</id>
          <label>Calculated End Date Baseline</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ENDDATE</id>
          <label>End Date</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>EMAIL</id>
          <label>E-Mail</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>EMAILPREFERENCE</id>
          <label>Type</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
        </field>
        <field>
          <id>PHONE</id>
          <label>Phone</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ALTPHONE</id>
          <label>Alt. Phone</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>FAX</id>
          <label>Fax</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>ACCOUNTNUMBER</id>
          <label>Account</label>
          <visible>F</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>CALCULATEDSTARTDATE</id>
          <label>Calculated Start Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>SCHEDULEDENDDATE</id>
          <label>Scheduled End Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
        <field>
          <id>[scriptid=custentity_ng_eh_estimated_invoice_date]</id>
          <label>Estimated Invoice Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>ENTITYSCHEDULE</id>
      <label>Schedule</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYJOBTASKS</id>
          <label>Project Tasks</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_entity_evnt_info]</id>
      <label>Event Information</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_200_t1941896_980">
          <label>Event and Event Dates</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_event_name]</id>
              <label>Event Name</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_discount_deadline]</id>
              <label>Discount Deadline</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_website]</id>
              <label>Event Website</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_ship_date]</id>
              <label>Ship Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_contact_name_phone]</id>
              <label>Main Contact Name &amp; Phone #</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_return_date]</id>
              <label>Return Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_material_arrival]</id>
              <label>Material Arrival</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_movein_date]</id>
              <label>Move In Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_start_date]</id>
              <label>Event Start Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_end_date]</id>
              <label>Event End Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_move_in_time]</id>
              <label>Move In Time</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_moveout_date]</id>
              <label>Move Out Date</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_material_pickup]</id>
              <label>Material Pickup</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_move_out_time]</id>
              <label>Move Out Time</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_203_t2149044_344">
          <label>Event Addresses</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_adv_warehouse]</id>
              <label>Advanced Warehouse Location</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_adv_wh_address]</id>
              <label>Advanced Warehouse Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_location]</id>
              <label>Event Site Location</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_event_address]</id>
              <label>Event Site Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_portal_ship_to_add]</id>
              <label>Ship To Address (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_shipping_add_portal]</id>
              <label>Shipping Address (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_to_show_ship_instruct]</id>
              <label>To - Show Shipping Instructions</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_carrier_check_in_time]</id>
              <label>Carrier Check In Time</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_frm_ship_instructions]</id>
              <label>From Show Shipping Instructions</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_pickup_address]</id>
              <label>Pickup Address</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_202_t1941896_394">
          <label>Scoping and Services</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_booth_size]</id>
              <label>Booth Size</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_prjext_exhibit_size]</id>
              <label>Exhibit Size</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_booth_number]</id>
              <label>Booth Number</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_prj_ext_exhibit_space]</id>
              <label>Exhibit Space (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_booth_description]</id>
              <label>Booth Space Description</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_proj_portal_blurb]</id>
              <label>Project Portal Blurb</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_inv_shipment_location]</id>
              <label>Inventory Shipment Location</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_event_venue_num]</id>
              <label>Event Venue Number</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_204_t2149044_732">
          <label>Additional Event Information</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_prj_ext_dec_name]</id>
              <label>General Contractor</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_prj_ext_dec_url]</id>
              <label>General Contractor Website</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentityng_eh_pr_ext_dec_client_usrnm]</id>
              <label>General Contractor Website User Name</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentityng_eh_prj_ext_dec_cl_usrpw]</id>
              <label>General Contractor Website Password</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_205_t2149044_586">
          <label>Show Services</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_prj_ext_shw_serv_ord_by]</id>
              <label>Show Services Ordered By (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_card_for_show_services]</id>
              <label>Card For Show Services (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_material_handling]</id>
              <label>Material Handling</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_material_handle_checkbx]</id>
              <label>Material Handling</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_labor_checkbox]</id>
              <label>Labor</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_carpet_padding_checkbox]</id>
              <label>Carpet &amp; Padding</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_labor_by_portal]</id>
              <label>Labor By (Portal)</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_internet_checkbox]</id>
              <label>Internet</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_lead_retrieval_checkbox]</id>
              <label>Lead Retrieval</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_rigging_checkbox]</id>
              <label>Rigging</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_electrical_checkbox]</id>
              <label>Electrical</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_electrical_details]</id>
              <label>Electrical Notes</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_furniture_checkbox]</id>
              <label>Furniture</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_furniture_details]</id>
              <label>Furniture Notes</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_av_checkbox]</id>
              <label>AV</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_av_details]</id>
              <label>AV Notes</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_other_checkbox]</id>
              <label>Other</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_other_details]</id>
              <label>Other Notes</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>DEFAULTADDRESS</id>
              <label>Address</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>DISABLED</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_tasks]</id>
      <label>Tasks</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYTASKS</id>
          <label>Tasks</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYRESOURCES</id>
      <label>Resources</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYRESOURCEALLOCATION</id>
          <label>Resource Allocations</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYJOBRESOURCES</id>
          <label>Resources List</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYFINANCIAL</id>
      <label>Financial</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="billinginformation">
          <label>Billing Setup</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>JOBBILLINGTYPE</id>
              <label>Billing Type</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>BILLINGSCHEDULE</id>
              <label>Billing Schedule</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>JOBITEM</id>
              <label>Billing Item</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>JOBPRICE</id>
              <label>Project Price</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="estimates">
          <label>Total Charges</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>CHARGEAMOUNTBILLED</id>
              <label>Billed</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>CHARGEAMOUNTPENDING</id>
              <label>Pending</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
            <field>
              <id>CHARGEAMOUNTREMAINING</id>
              <label>Remaining</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="accountinformation">
          <label>Open Charges</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>CHARGEAMOUNTREADYFORBILLING</id>
              <label>Ready to Bill</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>CHARGEAMOUNTHOLDFORBILLING</id>
              <label>On Hold</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <fieldGroup scriptid="fieldgroup_198_t1499227_449">
          <label>Charge Types</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>T</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>CHARGELABORAMOUNT</id>
              <label>Labor</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>CHARGEEXPENSEAMOUNT</id>
              <label>Expense</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>T</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>OPENINGBALANCE</id>
              <label>Opening Balance</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>OPENINGBALANCEDATE</id>
              <label>Opening Balance Date</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>OPENINGBALANCEACCOUNT</id>
              <label>Opening Balance Account</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYCREDITCARDS</id>
          <label>Credit Cards</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_files]</id>
      <label>Files</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYRELATEDRECORDS</id>
      <label>Related Transactions</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYMESSAGES</id>
          <label>Messages</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCONTACT</id>
          <label>Contacts</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYOPPORTUNITIES</id>
          <label>Opportunities</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYFINHIST</id>
          <label>Sales</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCASES</id>
          <label>Discussions</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYSUBS</id>
          <label>Sub-Projects</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYSUBSCRIPTIONMSGMACH</id>
          <label>Subscription Message History</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCAMPAIGNS</id>
          <label>Campaigns</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYACTIVITIES</id>
          <label>Calls &amp; Meetings</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYEVENTS</id>
          <label>Events</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYCALLS</id>
          <label>Calls</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYTIMEITEM</id>
          <label>Timesheets</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYERPOLICIES</id>
          <label>Expense Report Policies</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYADDRESSBOOK</id>
          <label>Address</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=custsublist_ng_eh_sub_proj_costs]</id>
          <label>Costs</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=custsublist_ng_eh_sub_jrnl_entries]</id>
          <label>Journal Entries</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=custsublist_ng_eh_sub_proj_rent_costs]</id>
          <label>Project Rentals + Costs</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>[scriptid=customrecord_ng_eh_est_mat_takeoff.custrecord_related_project]</id>
          <label>CS Estimate Detail Takeoff</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYACCOUNTING</id>
      <label>Accounting</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYRELATIONSHIPS</id>
      <label>Relationships</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYS_SYSINFO</id>
      <label>System Information</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>LASTPROJECTPLANRECALCULATIONDATETIME</id>
              <label>Last Recalculation</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>LASTPROJECTPLANRECALCULATIONDURATION</id>
              <label>Last Recalculation Duration</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>LASTPROJECTPLANRECALCULATIONTRIGGER</id>
              <label>Last Recalculation Trigger</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>LASTPROJECTPLANRECALCULATIONSTATUS</id>
              <label>Last Recalculation Status</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>AVERAGEPROJECTPLANRECALCULATIONDURATION</id>
              <label>Average Recalculation Duration</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>PROJECTPLANRECALCULATIONINPROGRESS</id>
              <label>Recalculation In Progress</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>PROJECTPLANRECALCULATIONSTARTED</id>
              <label>Recalculation Started at</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>INLINETEXT</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYCOMMUNICATION</id>
      <label>Attachments</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_inventory]</id>
      <label>Inventory</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYCUSTOM</id>
      <label>Custom</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
    <tab>
      <id>ENTITYPREFERENCES</id>
      <label>Administration</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>FXRATE</id>
              <label>Exchange Rate</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>PROJECTEXPENSETYPE</id>
              <label>Project Expense Type</label>
              <visible>T</visible>
              <mandatory>T</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>FORECASTCHARGERUNONDEMAND</id>
              <label>Forecast Charge Run on Demand</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ALLOWTIME</id>
              <label>Allow Time Entry</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>APPLYPROJECTEXPENSETYPETOALL</id>
              <label>Apply to All Time Entries</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ALLOWEXPENSES</id>
              <label>Allow Expenses</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ISUTILIZEDTIME</id>
              <label>Classify Time as Utilized</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ISPRODUCTIVETIME</id>
              <label>Classify Time as Productive</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ISEXEMPTTIME</id>
              <label>Classify Time as Exempt</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ALLOCATEPAYROLLEXPENSES</id>
              <label>Allocate Payroll Expenses</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>MATERIALIZETIME</id>
              <label>Create Planned Time Entries</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ALLOWALLRESOURCESFORTASKS</id>
              <label>Display All Resources for Task Assignment</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>LIMITTIMETOASSIGNEES</id>
              <label>Limit Time and Expense to Assignees</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ALLOWTASKTIMEFORRSRCALLOC</id>
              <label>Allow Allocated Resources to Enter Time to All Tasks</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>USEALLOCATEDTIMEFORFORECAST</id>
              <label>Use Allocated Time for Forecast</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ISINACTIVE</id>
              <label>Project is Inactive</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>DATECREATED</id>
              <label>Date Created</label>
              <visible>F</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>INCLUDECRMTASKSINTOTALS</id>
              <label>Include CRM Tasks in Job Totals</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>TIMEAPPROVAL</id>
              <label>Time Approval</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.netsuite.purchase2project, scriptid=custentity_p2p_allow_purchase]</id>
              <label>Allow Purchase Orders</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[appid=com.netsuite.purchase2project, scriptid=custentity_p2p_allow_vendor_bill]</id>
              <label>Allow Vendor Bills</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYUSERNOTES</id>
          <label>User Notes</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>ENTITYWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_entity_wrk_reqs]</id>
      <label>Work Requests</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_eh_work_request.custrecord_ng_eh_work_req_proj]</id>
          <label>Work Request</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_eh_entity_shipments]</id>
      <label>Shipments</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_proj]</id>
          <label>CS Shipment</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYPL</id>
      <label>P&amp;L</label>
      <visible>T</visible>
      <fieldGroups>
        <fieldGroup scriptid="fieldgroup_199_t1499227_449">
          <label>Estimated Project Gross Margin</label>
          <visible>T</visible>
          <showTitle>T</showTitle>
          <singleColumn>F</singleColumn>
          <fields position="MIDDLE">
            <field>
              <id>ESTIMATEDGROSSPROFIT</id>
              <label>Estimated Gross Margin</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ESTIMATEDREVENUEJC</id>
              <label>Estimated Billing</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ESTIMATEDGROSSPROFITPERCENT</id>
              <label>Estimated Gross Margin %</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>ESTIMATEDCOSTJC</id>
              <label>Estimated Cost</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>T</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </fieldGroup>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYPLSTATEMENT</id>
          <label>P&amp;L</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYBUDGET</id>
      <label>Budget</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subTab>
          <id>ENTITYSUBTAB_CBUDGET</id>
          <label>Cost Budget</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>CBUDGETLABORBUDGETFROMALLOC</id>
                  <label>Calculate Labor Budgets from Resource Allocations</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CBUDGETSHOWCALCULATEDLINES</id>
                  <label>Show Calculated Lines</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>CBUDGETUSECALCULATEDVALUES</id>
                  <label>Use Calculated Values for all Cost Budgets</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
        <subTab>
          <id>ENTITYSUBTAB_BBUDGET</id>
          <label>Billing Budget</label>
          <visible>T</visible>
          <fieldGroups>
            <defaultFieldGroup>
              <fields position="MIDDLE">
                <field>
                  <id>BBUDGETSHOWCALCULATEDLINES</id>
                  <label>Show Calculated Lines</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
                <field>
                  <id>BBUDGETUSECALCULATEDVALUES</id>
                  <label>Use Calculated Values for all Billing Budgets</label>
                  <visible>T</visible>
                  <mandatory>F</mandatory>
                  <displayType>NORMAL</displayType>
                  <columnBreak>F</columnBreak>
                  <sameRowAsPrevious>F</sameRowAsPrevious>
                </field>
              </fields>
            </defaultFieldGroup>
          </fieldGroups>
          <subLists/>
        </subTab>
      </subItems>
    </tab>
    <tab>
      <id>ENTITYPROJINDICATORS</id>
      <label>Project Indicators</label>
      <visible>F</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>ENTITYPROJECTINDICATORS</id>
          <label>Project Indicators</label>
          <visible>F</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=custtab_ng_cs_eh_prduct]</id>
      <label>ConventionSuite</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup>
          <fields position="MIDDLE">
            <field>
              <id>[scriptid=custentity_ng_eh_disp_in_portal]</id>
              <label>Show In Customer Portal</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_portal_perm_level]</id>
              <label>Portal Permission Levels</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
            <field>
              <id>[scriptid=custentity_ng_eh_cs_project_form]</id>
              <label>CS Project Form</label>
              <visible>T</visible>
              <mandatory>F</mandatory>
              <displayType>NORMAL</displayType>
              <columnBreak>F</columnBreak>
              <sameRowAsPrevious>F</sameRowAsPrevious>
            </field>
          </fields>
        </defaultFieldGroup>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <quickViewFields>
    <field>
      <id>ENTITYID</id>
    </field>
    <field>
      <id>COMPANYNAME</id>
    </field>
    <field>
      <id>ENTITYSTATUS</id>
    </field>
    <field>
      <id>[scriptid=custentity_ng_eh_project_manager]</id>
    </field>
    <field>
      <id>STARTDATE</id>
    </field>
    <field>
      <id>CALCULATEDENDDATE</id>
    </field>
  </quickViewFields>
  <actionbar>
    <buttons>
      <button>
        <id>CHANGECUSTOMER</id>
        <label>Change Customer</label>
        <visible>T</visible>
      </button>
      <button>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </button>
      <button>
        <id>SAVEBASELINE</id>
        <label>Set Baseline</label>
        <visible>T</visible>
      </button>
      <button>
        <id>SEARCH</id>
        <label>Search</label>
        <visible>T</visible>
      </button>
    </buttons>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>RECALCULATEPERCENTCOMPLETEOVERRIDE</id>
        <label>Recalculate % Complete Override</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>RECALCULATEACCOUNTINGDATA</id>
        <label>Recalculate Financial Data</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>RECALCULATEPROJECTPLAN</id>
        <label>Recalculate Job Plan</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITEDIT</id>
        <label>Save &amp; Edit</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>UPDATEPROJECTMEASURES</id>
        <label>Update Project Measures</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
