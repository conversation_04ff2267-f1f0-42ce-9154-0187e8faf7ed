<entryForm scriptid="custform_ng_eh_cs_shipment" standard="STANDARDCUSTOMRECORD_NG_EH_SHIPMENTFORM">
  <name>CS EH Shipment Form</name>
  <recordType>[scriptid=customrecord_ng_eh_shipment]</recordType>
  <inactive>F</inactive>
  <preferred>T</preferred>
  <storedWithRecord>T</storedWithRecord>
  <mainFields>
    <fieldGroup scriptid="fieldgroup_2_t2149044_611">
      <label>Primary Information</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>CUSTOMFORM</id>
          <label>Custom Form</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>NAME</id>
          <label>Name</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>T</quickAdd>
        </field>
        <field>
          <id>RECORDID</id>
          <label>ID</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>OWNER</id>
          <label>Owner</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>CREATED</id>
          <label>Date Created</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>LASTMODIFIED</id>
          <label>Last Modified</label>
          <visible>T</visible>
          <mandatory>T</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>ISINACTIVE</id>
          <label>Inactive</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>PARENT</id>
          <label>Parent</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_proj]</id>
          <label>Project</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_customer]</id>
          <label>Customer</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_eh_ship_order]</id>
          <label>Related Order</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_shipdate]</id>
          <label>Ship Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_matarrivaldate]</id>
          <label>Material Arrival</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_matpickupdate]</id>
          <label>Material Pickup</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_returndate]</id>
          <label>Return Date</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>T</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_ship_demo]</id>
          <label>Memo</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_oneway]</id>
          <label>One-way Shipment?</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_3_t2149044_126">
      <label>1st Leg</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_fromaddress]</id>
          <label>Leg 1 From Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_fromco]</id>
          <label>Leg 1 From C/O</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_fromfulladdress]</id>
          <label>Leg 1 From Full Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_frm1_copy_add_btns]</id>
          <label>Copy CS Address to Ship From Leg 1 Field Buttons CS Shipment</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_toaddress]</id>
          <label>Leg 1 To Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_toco]</id>
          <label>Leg 1 To C/O</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_tofulladdress]</id>
          <label>Leg 1 To Full Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_copy_address_btns]</id>
          <label>Copy CS Address to Ship To Field Buttons CS Shipment</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_4_t2149044_568">
      <label>1st Leg Shipping Details</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_carrier]</id>
          <label>Leg 1 Carrier</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_trackingno]</id>
          <label>Leg 1 BOL # / Tracking No.</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_shipinstructions]</id>
          <label>Leg 1 Shipping Instructions</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_checkin]</id>
          <label>Leg 1 Check In Date/Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg1_unloadtime]</id>
          <label>Leg 1 Unload Date/Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_5_t2149044_233">
      <label>2nd Leg</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_fromaddress]</id>
          <label>Leg 2 From Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_fromco]</id>
          <label>Leg 2 From C/O</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_fromfulladdress]</id>
          <label>Leg 2 From Full Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_frm2_copy_add_btns]</id>
          <label>Copy CS Address to Ship From Leg 2 Field Buttons CS Shipment</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_toaddress]</id>
          <label>Leg 2 To Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>T</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_toco]</id>
          <label>Leg 2 To C/O</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_tofulladdress]</id>
          <label>Leg 2 To Full Address</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_copy_add_btns_lg2]</id>
          <label>Copy CS Address to Ship To Field Leg Two Buttons CS Shipment</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <fieldGroup scriptid="fieldgroup_6_t2149044_930">
      <label>2nd Leg Shipping Details</label>
      <visible>T</visible>
      <showTitle>T</showTitle>
      <singleColumn>F</singleColumn>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_carrier]</id>
          <label>Leg 2 Carrier</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_trackingno]</id>
          <label>Leg 2 BOL # / Tracking No.</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_shipinstructions]</id>
          <label>Leg 2 Shipping Instructions</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_checkin]</id>
          <label>Leg 2 Check In Date/Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_leg2_unloadtime]</id>
          <label>Leg 2 Unload Date/Time</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </fieldGroup>
    <defaultFieldGroup>
      <fields position="MIDDLE">
        <field>
          <id>[scriptid=customrecord_ng_eh_shipment.custrecord_ng_eh_ship_items_included]</id>
          <label>Items Included</label>
          <visible>T</visible>
          <mandatory>F</mandatory>
          <displayType>NORMAL</displayType>
          <columnBreak>F</columnBreak>
          <sameRowAsPrevious>F</sameRowAsPrevious>
          <quickAdd>F</quickAdd>
        </field>
      </fields>
    </defaultFieldGroup>
  </mainFields>
  <tabs>
    <tab>
      <id>RECORDNOTES</id>
      <label>Notes</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDUSERNOTES</id>
          <label>User Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDSYSTEMNOTES</id>
          <label>System Notes</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDMEDIA</id>
      <label>Files</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMEDIAITEM</id>
          <label>Files</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDWORKFLOW</id>
      <label>Workflow</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDACTIVEWORKFLOWS</id>
          <label>Active Workflows</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
        <subList>
          <id>RECORDWORKFLOWHISTORY</id>
          <label>Workflow History</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>RECORDCUSTOM</id>
      <label>Custom</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems>
        <subList>
          <id>RECORDMULTIMACHCUSTRECORD_NG_EH_SHIP_ITEMS_INCLUDED</id>
          <label>Items Included</label>
          <visible>T</visible>
          <neverEmpty>F</neverEmpty>
        </subList>
      </subItems>
    </tab>
    <tab>
      <id>[scriptid=customrecord_ng_eh_shipment.tab_195_t2149044_215]</id>
      <label>Items Included</label>
      <visible>T</visible>
      <fieldGroups>
        <defaultFieldGroup/>
      </fieldGroups>
      <subItems/>
    </tab>
  </tabs>
  <actionbar>
    <menu>
      <menuitem>
        <id>DELETE</id>
        <label>Delete</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>MAKECOPY</id>
        <label>Make Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>NEW</id>
        <label>New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>PRINT</id>
        <label>Print</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITCOPY</id>
        <label>Save &amp; Copy</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEW</id>
        <label>Save &amp; New</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITNEXT</id>
        <label>Save &amp; Next</label>
        <visible>T</visible>
      </menuitem>
      <menuitem>
        <id>SUBMITAS</id>
        <label>Save As</label>
        <visible>T</visible>
      </menuitem>
    </menu>
  </actionbar>
  <editingInList>T</editingInList>
</entryForm>
