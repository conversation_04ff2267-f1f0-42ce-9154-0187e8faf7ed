<itemcustomfield scriptid="custitem_ng_eh_strg_inv_item">
  <accesslevel>2</accesslevel>
  <appliestoexpense>F</appliestoexpense>
  <appliestogroup>T</appliestogroup>
  <appliestoinventory>T</appliestoinventory>
  <appliestokit>T</appliestokit>
  <appliestononinventory>T</appliestononinventory>
  <appliestoothercharge>F</appliestoothercharge>
  <appliestopricelist>F</appliestopricelist>
  <appliestoservice>F</appliestoservice>
  <appliestospecificitems>F</appliestospecificitems>
  <appliestosubplan>F</appliestosubplan>
  <applyformatting>F</applyformatting>
  <checkspelling>F</checkspelling>
  <defaultchecked>F</defaultchecked>
  <defaultselection></defaultselection>
  <defaultvalue></defaultvalue>
  <description></description>
  <displayheight></displayheight>
  <displaytype>NORMAL</displaytype>
  <displaywidth></displaywidth>
  <dynamicdefault></dynamicdefault>
  <enabletextenhance>F</enabletextenhance>
  <encryptatrest>F</encryptatrest>
  <fieldtype>SELECT</fieldtype>
  <globalsearch>F</globalsearch>
  <help>Enter the storage item to be used for this item when creating storage invoices.</help>
  <includechilditems>F</includechilditems>
  <isformula>F</isformula>
  <ismandatory>F</ismandatory>
  <isparent>F</isparent>
  <itemmatrix>F</itemmatrix>
  <itemsubtype>BOTH</itemsubtype>
  <label>Storage Invoice Item</label>
  <linktext></linktext>
  <maxlength></maxlength>
  <maxvalue></maxvalue>
  <minvalue></minvalue>
  <onparentdelete>SET_NULL</onparentdelete>
  <parentsubtab></parentsubtab>
  <searchcomparefield></searchcomparefield>
  <searchdefault></searchdefault>
  <searchlevel>2</searchlevel>
  <selectrecordtype>-10</selectrecordtype>
  <showhierarchy>T</showhierarchy>
  <showinlist>F</showinlist>
  <sourcefilterby></sourcefilterby>
  <sourcefrom></sourcefrom>
  <sourcelist></sourcelist>
  <storevalue>T</storevalue>
  <subtab>[scriptid=custtab_ng_eh_item]</subtab>
</itemcustomfield>