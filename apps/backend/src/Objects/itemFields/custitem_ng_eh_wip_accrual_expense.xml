<itemcustomfield scriptid="custitem_ng_eh_wip_accrual_expense">
  <accesslevel>2</accesslevel>
  <appliestoexpense>T</appliestoexpense>
  <appliestogroup>T</appliestogroup>
  <appliestoinventory>T</appliestoinventory>
  <appliestokit>T</appliestokit>
  <appliestononinventory>T</appliestononinventory>
  <appliestoothercharge>T</appliestoothercharge>
  <appliestopricelist>F</appliestopricelist>
  <appliestoservice>T</appliestoservice>
  <appliestospecificitems>F</appliestospecificitems>
  <appliestosubplan>F</appliestosubplan>
  <applyformatting>F</applyformatting>
  <checkspelling>F</checkspelling>
  <defaultchecked>F</defaultchecked>
  <defaultselection></defaultselection>
  <defaultvalue></defaultvalue>
  <description></description>
  <displayheight></displayheight>
  <displaytype>NORMAL</displaytype>
  <displaywidth></displaywidth>
  <dynamicdefault></dynamicdefault>
  <enabletextenhance>F</enabletextenhance>
  <encryptatrest>F</encryptatrest>
  <fieldtype>SELECT</fieldtype>
  <globalsearch>F</globalsearch>
  <help></help>
  <includechilditems>F</includechilditems>
  <isformula>F</isformula>
  <ismandatory>F</ismandatory>
  <isparent>F</isparent>
  <itemmatrix>F</itemmatrix>
  <itemsubtype>BOTH</itemsubtype>
  <label>WIP Accrual Automation Expense Account</label>
  <linktext></linktext>
  <maxlength></maxlength>
  <maxvalue></maxvalue>
  <minvalue></minvalue>
  <onparentdelete>NO_ACTION</onparentdelete>
  <parentsubtab></parentsubtab>
  <searchcomparefield></searchcomparefield>
  <searchdefault></searchdefault>
  <searchlevel>2</searchlevel>
  <selectrecordtype>-112</selectrecordtype>
  <showhierarchy>T</showhierarchy>
  <showinlist>F</showinlist>
  <sourcefilterby></sourcefilterby>
  <sourcefrom></sourcefrom>
  <sourcelist></sourcelist>
  <storevalue>T</storevalue>
  <subtab>ITEMMAIN</subtab>
</itemcustomfield>