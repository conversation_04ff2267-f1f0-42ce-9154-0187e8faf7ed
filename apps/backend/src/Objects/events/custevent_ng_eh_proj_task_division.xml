<crmcustomfield scriptid="custevent_ng_eh_proj_task_division">
  <accesslevel>2</accesslevel>
  <appliesperkeyword>F</appliesperkeyword>
  <appliestocampaign>F</appliestocampaign>
  <appliestocase>F</appliestocase>
  <appliestoevent>F</appliestoevent>
  <appliestophonecall>F</appliestophonecall>
  <appliestoprojecttask>T</appliestoprojecttask>
  <appliestoresourceallocation>F</appliestoresourceallocation>
  <appliestotask>F</appliestotask>
  <applyformatting>F</applyformatting>
  <availableexternally>F</availableexternally>
  <checkspelling>F</checkspelling>
  <defaultchecked>F</defaultchecked>
  <defaultselection></defaultselection>
  <defaultvalue></defaultvalue>
  <description></description>
  <displayheight></displayheight>
  <displaytype>NORMAL</displaytype>
  <displaywidth></displaywidth>
  <dynamicdefault></dynamicdefault>
  <enabletextenhance>F</enabletextenhance>
  <encryptatrest>F</encryptatrest>
  <fieldtype>SELECT</fieldtype>
  <globalsearch>F</globalsearch>
  <help>This field drives the DEFAULTS of the selected watchers for your CS chat. If you don&apos;t have a &quot;Watch Config&quot; set up for this department. It will simply act as a label indicator.</help>
  <isformula>F</isformula>
  <ismandatory>F</ismandatory>
  <isparent>F</isparent>
  <label>Division</label>
  <linktext></linktext>
  <maxlength></maxlength>
  <maxvalue></maxvalue>
  <minvalue></minvalue>
  <onparentdelete>NO_ACTION</onparentdelete>
  <parentsubtab></parentsubtab>
  <searchcomparefield></searchcomparefield>
  <searchdefault></searchdefault>
  <searchlevel>2</searchlevel>
  <selectrecordtype>[scriptid=customrecord_ng_eh_cs_division]</selectrecordtype>
  <showhierarchy>F</showhierarchy>
  <showinlist>F</showinlist>
  <sourcefilterby></sourcefilterby>
  <sourcefrom></sourcefrom>
  <sourcelist></sourcelist>
  <storevalue>T</storevalue>
  <subtab>CRMMAIN</subtab>
</crmcustomfield>