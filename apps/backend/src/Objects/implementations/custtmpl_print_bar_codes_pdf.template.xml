<?xml version="1.0"?><!DOCTYPE pdf PUBLIC "-//big.faceless.org//report" "report-1.1.dtd">
<pdf>
<head>
	<link name="NotoSans" type="font" subtype="truetype" src="${nsfont.NotoSans_Regular}" src-bold="${nsfont.NotoSans_Bold}" src-italic="${nsfont.NotoSans_Italic}" src-bolditalic="${nsfont.NotoSans_BoldItalic}" bytes="2" />
	<#if .locale == "zh_CN">
		<link name="NotoSansCJKsc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKsc_Regular}" src-bold="${nsfont.NotoSansCJKsc_Bold}" bytes="2" />
	<#elseif .locale == "zh_TW">
		<link name="NotoSansCJKtc" type="font" subtype="opentype" src="${nsfont.NotoSansCJKtc_Regular}" src-bold="${nsfont.NotoSansCJKtc_Bold}" bytes="2" />
	<#elseif .locale == "ja_JP">
		<link name="NotoSansCJKjp" type="font" subtype="opentype" src="${nsfont.NotoSansCJKjp_Regular}" src-bold="${nsfont.NotoSansCJKjp_Bold}" bytes="2" />
	<#elseif .locale == "ko_KR">
		<link name="NotoSansCJKkr" type="font" subtype="opentype" src="${nsfont.NotoSansCJKkr_Regular}" src-bold="${nsfont.NotoSansCJKkr_Bold}" bytes="2" />
	<#elseif .locale == "th_TH">
		<link name="NotoSansThai" type="font" subtype="opentype" src="${nsfont.NotoSansThai_Regular}" src-bold="${nsfont.NotoSansThai_Bold}" bytes="2" />
	</#if>
    <style type="text/css">
      table { font-size: 9pt; table-layout: fixed; width: 100%; }
      th { font-weight: bold; font-size: 8pt; vertical-align: middle; padding: 5px 6px 3px; background-color: #e3e3e3; color: #333333; padding-bottom: 10px; padding-top: 10px; }
      td { padding: 4px 6px; }
      b { font-weight: bold; color: #333333; }
      p { font: NotoSans }
	</style>
</head>
<body padding="0.5in 0.5in 0.5in 0.5in" size="letter-landscape">
      <#list results as result>
        <table width="100%">
          <tr><td><p style="text-align: center; font-size: 48pt">${result.custitem_ng_eh_customer_owned}</p></td></tr>
          <tr><td><p style="text-align: center; font-size: 72pt">${result.itemid}</p></td></tr>
          <tr><td><p style="text-align: center; font-size: 36pt">${result.displayname}</p></td></tr>
          <tr><td><p style="text-align: center; font-size: 36pt">${result.custitem_ng_eh_item_category}</p></td></tr>
          <tr><td><p style="text-align: center; font-size: 36pt">${result.custitem_ng_eh_item_subcategory}</p></td></tr>
        </table>

		<table width="100%" padding-top="250px" style="page-break-after: always;">
          <tr>
            <td>
              <table width="100%">
                <tr font-size="24pt" text-align="center">
                  <td>L</td>
                  <td>&nbsp;</td>
                  <td>W</td>
                  <td>&nbsp;</td>
                  <td>H</td>
                  <td>WGT</td>
                </tr>
                <tr font-size="24pt" text-align="center">
                  <td>${result.custitem_ng_eh_length}</td>
                  <td>X</td>
                  <td>${result.custitem_ng_eh_width}</td>
                  <td>X</td>
                  <td>${result.custitem_ng_eh_height}</td>
                  <td>${result.custitem_ng_eh_weight}</td>
                </tr>
              </table>
            </td>
            <td align="right">
              <#if result.upccode?has_content>
                <barcode codetype="code128" showtext="true" style="width: 200pt; height: 60pt;" value="${result.upccode}"/>
              </#if>
            </td>
          </tr>
        </table>

	</#list>
</body>
</pdf>