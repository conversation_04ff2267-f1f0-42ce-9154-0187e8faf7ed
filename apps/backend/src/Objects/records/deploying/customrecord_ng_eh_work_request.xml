<customrecordtype scriptid="customrecord_ng_eh_work_request">
  <accesstype>NONENEEDED</accesstype>
  <allowattachments>T</allowattachments>
  <allowinlinedeleting>F</allowinlinedeleting>
  <allowinlinedetaching>T</allowinlinedetaching>
  <allowinlineediting>F</allowinlineediting>
  <allowmobileaccess>F</allowmobileaccess>
  <allownumberingoverride>F</allownumberingoverride>
  <allowquickadd>T</allowquickadd>
  <allowquicksearch>F</allowquicksearch>
  <allowuiaccess>T</allowuiaccess>
  <customsegment></customsegment>
  <description></description>
  <enabledle>T</enabledle>
  <enablekeywords>T</enablekeywords>
  <enablemailmerge>F</enablemailmerge>
  <enablenumbering>T</enablenumbering>
  <enableoptimisticlocking>T</enableoptimisticlocking>
  <enablesystemnotes>T</enablesystemnotes>
  <externalrolepermissionlevel>NONE</externalrolepermissionlevel>
  <hierarchical>F</hierarchical>
  <icon></icon>
  <iconbuiltin>T</iconbuiltin>
  <iconindex></iconindex>
  <includeinsearchmenu>T</includeinsearchmenu>
  <includename>F</includename>
  <isinactive>F</isinactive>
  <isordered>F</isordered>
  <numberinginit></numberinginit>
  <numberingmindigits></numberingmindigits>
  <numberingprefix>WR</numberingprefix>
  <numberingsuffix></numberingsuffix>
  <publicpermissionlevel>NONE</publicpermissionlevel>
  <recordname>CS Work Request</recordname>
  <showcreationdate>F</showcreationdate>
  <showcreationdateonlist>F</showcreationdateonlist>
  <showid>T</showid>
  <showlastmodified>F</showlastmodified>
  <showlastmodifiedonlist>F</showlastmodifiedonlist>
  <shownotes>T</shownotes>
  <showowner>F</showowner>
  <showownerallowchange>F</showownerallowchange>
  <showowneronlist>F</showowneronlist>
  <customrecordcustomfields>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_summary">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>TEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Summary</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_proj_task">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>T</isparent>
      <label>Project Task</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab>[scriptid=custtab_ng_eh_work_reqs]</parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-27</selectrecordtype>
      <showinlist>F</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
      <customfieldfilters>
        <customfieldfilter>
          <fldcomparefield>[scriptid=customrecord_ng_eh_work_request.custrecord_ng_eh_work_req_proj]</fldcomparefield>
          <fldfilter>STDEVENTALLOCATIONPROJECT</fldfilter>
          <fldfilterchecked></fldfilterchecked>
          <fldfiltercomparetype>EQ</fldfiltercomparetype>
          <fldfilternotnull>F</fldfilternotnull>
          <fldfilternull>F</fldfilternull>
          <fldfiltersel></fldfiltersel>
          <fldfilterval></fldfilterval>
        </customfieldfilter>
      </customfieldfilters>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_dept">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Work Request Department</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customlist_ng_eh_work_req_dept]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_proj">
      <accesslevel>2</accesslevel>
      <allowquickadd>T</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>T</isparent>
      <label>Project</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab>[scriptid=custtab_ng_eh_entity_wrk_reqs]</parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-7</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_status">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Status</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>NO_ACTION</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>[scriptid=customlist_ng_eh_work_req_status]</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_sent_by">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Sent By</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_assigned_to">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>SELECT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Assigned To</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete>SET_NULL</onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype>-4</selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_due_by">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>DATE</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Due By</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
    <customrecordcustomfield scriptid="custrecord_ng_eh_work_req_description">
      <accesslevel>2</accesslevel>
      <allowquickadd>F</allowquickadd>
      <applyformatting>F</applyformatting>
      <checkspelling>F</checkspelling>
      <defaultchecked>F</defaultchecked>
      <defaultselection></defaultselection>
      <defaultvalue></defaultvalue>
      <description></description>
      <displayheight></displayheight>
      <displaytype>NORMAL</displaytype>
      <displaywidth></displaywidth>
      <dynamicdefault></dynamicdefault>
      <enabletextenhance>F</enabletextenhance>
      <encryptatrest>F</encryptatrest>
      <fieldtype>RICHTEXT</fieldtype>
      <globalsearch>F</globalsearch>
      <help></help>
      <isformula>F</isformula>
      <ismandatory>F</ismandatory>
      <isparent>F</isparent>
      <label>Brief Job Description</label>
      <linktext></linktext>
      <maxlength></maxlength>
      <maxvalue></maxvalue>
      <minvalue></minvalue>
      <onparentdelete></onparentdelete>
      <parentsubtab></parentsubtab>
      <rolerestrict>F</rolerestrict>
      <searchcomparefield></searchcomparefield>
      <searchdefault></searchdefault>
      <searchlevel>2</searchlevel>
      <selectrecordtype></selectrecordtype>
      <showinlist>T</showinlist>
      <sourcefilterby></sourcefilterby>
      <sourcefrom></sourcefrom>
      <sourcelist></sourcelist>
      <storevalue>T</storevalue>
      <subtab></subtab>
    </customrecordcustomfield>
  </customrecordcustomfields>
  <subtabs>
    <subtab scriptid="tab_179_t2149044_138">
      <tabparent></tabparent>
      <tabtitle>Steps</tabtitle>
    </subtab>
  </subtabs>
</customrecordtype>