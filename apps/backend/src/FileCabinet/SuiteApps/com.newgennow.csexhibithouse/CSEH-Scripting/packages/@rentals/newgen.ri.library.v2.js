/**
 * newgen.ri.library.v2.js
 * @NApiVersion 2.0
 * @NModuleScope Public
 */


define(['N/search', 'N/format', 'N/util'],

	function (search, format, util) {

		function getRentalAvailability(item, loc) {
			var data = {};
			if (!item || !loc) {
				return data;
			}

			var filtItem = null;
			var filtLoc = null;
			if (Array.isArray(item)) {
				filtItem = item;
			} else {
				filtItem = [item];
			}
			if (Array.isArray(loc)) {
				filtLoc = loc;
			} else {
				filtLoc = [loc];
			}

			var avFilt = [
				["custrecord_ng_cs_isa_item", "anyof", filtItem],
				"and",
				["custrecord_ng_cs_isa_location", "anyof", filtLoc]
			];

			var avCols = [
				search.createColumn({name: "custrecord_ng_cs_isa_item"}),
				search.createColumn({name: "custrecord_ng_cs_isa_location"}),
				search.createColumn({name: "custrecord_ng_cs_isa_avail_stock"})
			];
			var results = [];

			try {

				var availabilitySearch = search.create({
					type: "customrecord_ng_cs_item_stock_avail",
					filters: avFilt,
					columns: avCols
				})

				log.audit({title: 'Running availability search...', details: availabilitySearch })
				var resultsCount = availabilitySearch.runPaged().count

				log.audit({ title: 'Availability results count:', details: resultsCount })

				getAllResultsFor(availabilitySearch, function(result) {
					results.push(result)
				})

				log.audit({title: 'Availability search completed!', details: ''})

			} catch (err) {
				try {
					console.log("Error encountered searching for rental inventory counts\n", err);
				} catch (e) {
					log.error({title: 'Error encountered searching for rental inventory counts', details: err})
				}
			}

			if (results.length !== 0) {
				log.audit({ title: '🔎 Results found running location checks...', details: ''})

				var locNameMap = {};
				var locData = []
				var locationSearch = search.create({
					type: search.Type.LOCATION,
					filters: [["internalid", "anyof", filtLoc]],
					columns: [
						search.createColumn({name: "name"})
					]
				})

				var locPagedDataCount = locationSearch.runPaged().count
				log.audit({ title: '🔎 Location results of location count:', details: locPagedDataCount })

				getAllResultsFor(locationSearch, function (result) {
					locData.push(result)
				})

				// Creating massive object of detailed locations
				for (var l = 0; l < locData.length; l++) {
					log.debug({ title: '➡ Looping location results for object:', details: locNameMap })
					locNameMap[locData[l].id] = locData[l].getValue({name: "name"});
				}

				log.audit({ title: '🔧 Location mapping finished:', details: locNameMap })

				for (var r = 0; r < results.length; r++) {
					log.debug({ title: '➡ Looping customrecord_ng_cs_item_stock_avail results for object:', details: data })

					var itemId = results[r].getValue("custrecord_ng_cs_isa_item");
					var itemName = results[r].getText("custrecord_ng_cs_isa_item");
					var locId = results[r].getValue("custrecord_ng_cs_isa_location");
					var locName = results[r].getText("custrecord_ng_cs_isa_location");
					var invQty = Number(results[r].getValue("custrecord_ng_cs_isa_avail_stock") || "0");

					if (!data[itemId]) {
						data[itemId] = {
							name: itemName
							, id: itemId
						};
					}

					data[itemId][locId] = {
						qty: invQty,
						name: locName
					};
				}

				log.audit({ title: '📊 Stock availability object:', details: data })

				for (var i = 0; i < filtItem.length; i++) {
					log.debug({ title: '➡ Looping filtered item for object:', details: data })
					var itemId = filtItem[i];

					if (!data[itemId]) {
						log.debug({ title: '➡ Item id is undefined in data defaulting item quantity:', details: data[itemId] })
						data[itemId] = {};
						for (var locId in locNameMap) {
							log.debug({ title: '➡➡ Looping location map for Item id is undefined in data defaulting item quantity on locations:', details: data[itemId][locId] })
							data[itemId][locId] = {name: locNameMap[locId], qty: 0};
						}
						log.debug({ title: '➡ Data after update to undefined item:', details: data })
					} else {
						log.debug({ title: '➡ Item id is defined in data defaulting item quantities:', details: data[itemId] })
						for (var locId in locNameMap) {
							log.debug({ title: '➡➡ Looping location map for Item id in data defaulting item quantities:', details: data[itemId] })
							if (!data[itemId][locId]) {
								log.debug({ title: '➡➡ Looping location map for undefined key in data defaulting item quantities:', details: data[itemId] })
								data[itemId][locId] = {name: locNameMap[locId], qty: 0};
							}
						}
					}
					log.debug({ title: '➡ Data after update to defined item:', details: data })
				}


			} else {
				log.audit({ title: '🔎 No results from availability search...', details: 'returning NULL'})
				return null;
			}

			log.audit({ title: '🔧 Object assembled:', details: data })

			return data;
		}

		function getReservedQuantitiesGrouped(item, loc, start, end, id) {
			var data = {};
			if (!item || !loc) {
				return data;
			}

			var filtItem = null;
			var filtLoc = [loc];
			if (Array.isArray(item)) {
				if (item.length < 1) {
					return data;
				}
				filtItem = item;
			} else {
				filtItem = [item];
			}

			var avFilt = [
				["item", "anyof", filtItem],
				"and",
				["custcol_ng_cs_rental_loc", "anyof", filtLoc],
				"and",
				["mainline", "is", "F"],
				"and",
				["cogs", "is", "F"],
				"and",
				["shipping", "is", "F"],
				"and",
				["taxline", "is", "F"],
				"and",
				["custcol_ng_cs_rental_start_date", "isnotempty", null],
				"and",
				["custcol_ng_cs_rental_end_date", "isnotempty", null],
				"and",
				[
					[
						["custcol_ng_cs_rental_start_date", "within", start, end],
						"and",
						["custcol_ng_cs_rental_end_date", "within", start, end]
					],
					"or",
					[
						["custcol_ng_cs_rental_start_date", "before", start],
						"and",
						["custcol_ng_cs_rental_end_date", "within", start, end]
					],
					"or",
					[
						["custcol_ng_cs_rental_start_date", "within", start, end],
						"and",
						["custcol_ng_cs_rental_end_date", "after", end]
					],
					"or",
					[
						["custcol_ng_cs_rental_start_date", "before", start],
						"and",
						["custcol_ng_cs_rental_end_date", "after", end]
					]
				]
			];

			if (id) { // scope to one item
				avFilt.push("and", ["internalid", "noneof", [id]]);
			}
			var avCols = [
				search.createColumn({
					name: "item",
					summary: "group"
				}),
				search.createColumn({
					name: "quantity",
					summary: "sum"
				})
			];
			var results = [];
			var hasErr = false;
			try {
				var avSearch = search.create({
					type: search.Type.SALES_ORDER,
					filters: avFilt,
					columns: avCols
				});

				var pagedData = avSearch.runPaged()
				util.each(pagedData.pageRanges, function (pageRange) {
					var myPage = pagedData.fetch({index: pageRange.index})
					util.each(myPage.data, function (result) {
						results.push(result)
					})
				})

			} catch (err) {
				hasErr = true;
				try {
					console.log("Error encountered searching for reserved rental quantities\n", err);
				} catch (e) {
					log.error({title: 'Error Ecountered in searching rental quantities', details: err})
				}
			}

			if (results) {
				for (var r = 0; r < results.length; r++) {
					var itemId = results[r].getValue(avCols[0]);
					var qty = new Number(results[r].getValue(avCols[1]));
					data[itemId] = {};
					data[itemId][loc] = qty;
				}

				for (var f = 0; f < filtItem.length; f++) {
					if (!data[filtItem[f]]) {
						data[filtItem[f]] = {};
						data[filtItem[f]][loc] = 0;
					}
				}
			} else if (!hasErr) {
				for (var f = 0; f < filtItem.length; f++) {
					data[filtItem[f]] = {};
					data[filtItem[f]][loc] = 0;
				}
			} else {
				return null;
			}

			return data;
		}

		/**
		 * @param item {Array<Object>|String}
		 * @param loc {String|Number}
		 * @param start {Date|String}
		 * @param end {Date|String}
		 * @param id {Number|String}
		 * @param additionalFilters {Array<Object|String>}
		 * */
		function getReservedQuantities(item, loc, start, end, id, additionalFilters) {
			var data = {};
			if (!item || !loc) {
				return data;
			}

			var filtItem = null;
			var filtLoc = [loc];
			if (Array.isArray(item)) {
				if (item.length < 1) {
					return data;
				}
				filtItem = item;
			} else {
				filtItem = [item];
			}

			return performItemDataSearch(filtItem, filtLoc, start, end, id, additionalFilters);
		}

		function getReservedQuantitiesNoItem(loc, start, end, id, additionalFilters) {
			var data = {};
			if (!loc) {
				return data;
			}

			var filtLoc = [loc];

			return performItemDataSearch(null, filtLoc, start, end, id, additionalFilters);
		}

		/**
		 * @param filtItem {Array<Object>|String}
		 * @param filtLoc {String|Number}
		 * @param start {Date|String}
		 * @param end {Date|String}
		 * @param id {Number|String}
		 * @param additionalFilters {Array<Object|String>}
		 * */
		function performItemDataSearch(filtItem, filtLoc, start, end, id, additionalFilters) {
			var data = {};

			var avFilt = [
				["custcol_ng_cs_rental_loc", "anyof", filtLoc],
				"and",
				["mainline", "is", "F"],
				"and",
				["cogs", "is", "F"],
				"and",
				["shipping", "is", "F"],
				"and",
				["taxline", "is", "F"],
				"and",
				["custcol_ng_cs_rental_start_date", "isnotempty", null],
				"and",
				["custcol_ng_cs_rental_end_date", "isnotempty", null],
				"and",
				[
					[
						["custcol_ng_cs_rental_start_date", "within", start, end]
						, "and"
						, ["custcol_ng_cs_rental_end_date", "within", start, end]
					]
					, "or"
					, [
					["custcol_ng_cs_rental_start_date", "before", start]
					, "and"
					, ["custcol_ng_cs_rental_end_date", "within", start, end]
				]
					, "or"
					, [
					["custcol_ng_cs_rental_end_date", "after", end]
					, "and"
					, ["custcol_ng_cs_rental_start_date", "within", start, end]
				]
					, "or"
					, [
					["custcol_ng_cs_rental_start_date", "before", start]
					, "and"
					, ["custcol_ng_cs_rental_end_date", "after", end]
				]
				]
			];

			if (filtItem && filtItem.length !== 0) {
				avFilt.unshift(
					["item", "anyof", filtItem]
					, "and"
				);
			}

			if (id) {
				avFilt.push(
					"and",
					["internalid", "noneof", [id]]
				);
			}

			if (additionalFilters) {
				avFilt = avFilt.concat(additionalFilters);
			}

			var avCols = [
				search.createColumn({name: "item"}),
				search.createColumn({name: "quantity"}),
				search.createColumn({name: "lineuniquekey"}),
				search.createColumn({name: "tranid"}),
				search.createColumn({name: "entityid", join: "jobmain"}),
				search.createColumn({name: "companyname", join: "jobmain"}),
				search.createColumn({name: "displayname", join: "item"}),
				search.createColumn({name: "itemid", join: "item"}),
				search.createColumn({name: "custcol_ng_cs_rental_start_date"}),
				search.createColumn({name: "custcol_ng_cs_rental_end_date"}),
				search.createColumn({name: "custitem_ng_eh_item_category", join: "item"}),
				search.createColumn({name: "custitem_ng_eh_item_subcategory", join: "item"})
			];

			var results = [];
			var hasErr = false;
			try {
				var salesOrderSearch = search.create(
					{
						type: search.Type.SALES_ORDER,
						filters: avFilt,
						columns: avCols
					}
				)
				var pagedData = salesOrderSearch.runPaged()
				util.each(pagedData.pageRanges, function (pageRange) {
					var myPage = pagedData.fetch({index: pageRange.index})
					util.each(myPage.data, function (result) {
						results.push(result)
					})
				})
			} catch (err) {
				hasErr = true;
				try {
					console.log("Error encountered searching for reserved rental quantities\n", err);
				} catch (e) {
					log.error({title: 'Error encountered searching for reserved rental quantities', details: err})
				}
			}

			var loc = filtLoc[0];
			if (results && results.length !== 0) {
				for (var r = 0; r < results.length; r++) {
					var itemId = results[r].getValue({name: "item"});
					var qty = Number(results[r].getValue({name: "quantity"}));
					var lineKey = results[r].getValue({name: "lineuniquekey"});
					var orderNum = results[r].getValue({name: "tranid"});
					var prjNumber = results[r].getValue({name: "entityid", join: "jobmain"});
					var prjCompName = results[r].getValue({name: "companyname", join: "jobmain"});
					var prjName = "";
					if (prjNumber && !prjCompName) {
						prjName = prjNumber;
					} else if (!prjNumber && prjCompName) {
						prjName = prjCompName;
					} else if (prjNumber && prjCompName) {
						prjName = String(prjNumber + " : " + prjCompName)
					} else {
						prjName = "N/A";
					}
					var rStart = results[r].getValue({name: "custcol_ng_cs_rental_start_date"});
					var rEnd = results[r].getValue({name: "custcol_ng_cs_rental_end_date"});
					var itemName = results[r].getValue({
						name: "displayname",
						join: "item"
					}) || results[r].getValue({name: "itemid", join: "item"}) || "";
					var catId = results[r].getValue({name: "custitem_ng_eh_item_category", join: "item"});
					var catName = results[r].getText({name: "custitem_ng_eh_item_category", join: "item"});
					var subCatId = results[r].getValue({name: "custitem_ng_eh_item_subcategory", join: "item"});
					var subCatName = results[r].getText({name: "custitem_ng_eh_item_subcategory", join: "item"});
					if (!data[itemId]) {
						data[itemId] = {};
						data[itemId][loc] = [];
					}
					data[itemId][loc].push({
						key: lineKey
						, qty: qty
						, order: orderNum
						, orderId: results[r].id
						, project: prjName
						, rs: rStart
						, re: rEnd
						, itemName: itemName
						, catId: catId
						, catName: catName
						, subCatId: subCatId
						, subCatName: subCatName
					});
				}

				if (filtItem && filtItem.length !== 0) {
					for (var f = 0; f < filtItem.length; f++) {
						if (!data[filtItem[f]]) {
							data[filtItem[f]] = {};
							data[filtItem[f]][loc] = [{ // Why are we modifying the original data?
								key: "ABC123"
								, qty: 0
								, order: "N/A"
								, orderId: "N/A"
								, project: "N/A"
								, rs: "N/A"
								, re: "N/A"
								, itemName: "N/A"
								, catId: "N/A"
								, catName: "N/A"
								, subCatId: "N/A"
								, subCatName: "N/A"
							}];
						}
					}
				}
			} else if (!hasErr && filtItem && filtItem.length !== 0) {
				for (var f = 0; f < filtItem.length; f++) {
					data[filtItem[f]] = {};
					data[filtItem[f]][loc] = [{
						key: "ABC123"
						, qty: 0
						, order: "N/A"
						, orderId: "N/A"
						, project: "N/A"
						, rs: "N/A"
						, re: "N/A"
						, itemName: "N/A"
						, catId: "N/A"
						, catName: "N/A"
						, subCatId: "N/A"
						, subCatName: "N/A"
					}];
				}
			} else {
				return null;
			}

			return data;
		}

		function condenseReservedQuantities(results, extended) {

			var preData = {};
			var data = {};
			extended = extended || false;
			log.debug({ title: 'Results Condensing from for dialog order display:', details: results})
			for (var r = 0; r < results.length; r++) {
				for (var itemId in results[r]) {
					log.debug({ title: 'PreData:', details: preData[itemId]})
					if (!preData[itemId]) {
						preData[itemId] = {};
					}
					log.debug({ title: 'PreData item loc:', details: preData[itemId][loc]})

					for (var loc in results[r][itemId]) {
						if (!preData[itemId][loc]) {
							preData[itemId][loc] = new Array();
						}
						for (var k = 0; k < results[r][itemId][loc].length; k++) {
							if (!preData[itemId][loc].includes(function (x) {
								return x.key == results[r][itemId][loc][k].key;
							})) {
								preData[itemId][loc].push(results[r][itemId][loc][k]);
							}
						}
					}
				}
			}

			for (var itemId in preData) {
				data[itemId] = {};
				for (var loc in preData[itemId]) {
					var total = new Number(0);
					for (var q = 0; q < preData[itemId][loc].length; q++) {
						total += preData[itemId][loc][q].qty;
					}

					if (!extended) {
						data[itemId][loc] = Math.round(total);
					} else {
						data[itemId][loc] = {
							total: Math.round(total)
							, ext: []
						};

						for (var q = 0; q < preData[itemId][loc].length; q++) {
							data[itemId][loc].ext.push({
								order: preData[itemId][loc][q].order
								, orderId: preData[itemId][loc][q].orderId
								, project: preData[itemId][loc][q].project
								, rs: preData[itemId][loc][q].rs
								, re: preData[itemId][loc][q].re
								, qty: preData[itemId][loc][q].qty
								, itemName: preData[itemId][loc][q].itemName
								, catId: preData[itemId][loc][q].catId
								, catName: preData[itemId][loc][q].catName
								, subCatId: preData[itemId][loc][q].subCatId
								, subCatName: preData[itemId][loc][q].subCatName
							});
							if (!data[itemId][loc].info) {
								data[itemId][loc].info = {
									itemName: preData[itemId][loc][q].itemName
									, catId: preData[itemId][loc][q].catId
									, catName: preData[itemId][loc][q].catName
									, subCatId: preData[itemId][loc][q].subCatId
									, subCatName: preData[itemId][loc][q].subCatName
								}
							}
						}
					}
				}
			}

			return data;
		}

		function getItemCategoryData() {
			var listOut = [];
			var filt = new Array(
				["isinactive", "is", "F"]
			);
			var cols = new Array(
				search.createColumn({name: "name"})
			);
			var results = []
			var salesOrderSearch = search.create(
				{
					type: "customlist_ng_eh_item_category",
					filters: filt,
					columns: cols
				}
			)
			var pagedData = salesOrderSearch.runPaged()
			util.each(pagedData.pageRanges, function (pageRange) {
				var myPage = pagedData.fetch({index: pageRange.index})
				util.each(myPage.data, function (result) {
					results.push(result)
				})
			})

			var sortedOptions = []
			if (results.length !== 0) {
				for (var r = 0; r < results.length; r++) {
					listOut.push({
						id: results[r].id
						, text: results[r].getValue({name: "name"})
					});
				}

				sortedOptions = listOut.sort(function (a, b)  {
					if (a.value < b.value) {
						return -1
					}
					if (a.value > b.value) {
						return 1
					}
					return 0
				})
			}

			return sortedOptions;
		}

		// TODO: Add more logging for audit purposes
		function getItemSubCategoryData() {
			var listOut = [];
			var filt = new Array(
				["isinactive", "is", "F"]
			);
			var cols = [
				search.createColumn({name: "name"}),
				search.createColumn({name: "custrecord_ng_eh_item_category"})
			];
			var results = []
			var itemSubCategorySearch = search.create({
				type: "customrecord_ng_eh_item_subcategory",
				filters: filt,
				columns: cols
			})
			var pagedData = itemSubCategorySearch.runPaged()
			util.each(pagedData.pageRanges, function (pageRange) {
				var myPage = pagedData.fetch({index: pageRange.index})
				util.each(myPage.data, function (result) {
					results.push(result)
				})
			})
			var sortedList = []
			if (results && results.length !== 0) {
				for (var r = 0; r < results.length; r++) {
					listOut.push({
						id: results[r].id
						,
						text: String(results[r].getText({name: "custrecord_ng_eh_item_category"}) + " : " + results[r].getValue({name: "name"}))
					});
				}

				sortedList = listOut.sort(function(a, b) {
					if (a.text < b.text) {
						return -1
					}
					if (a.text > b.text) {
						return 1
					}
					return 0
				})
			}

			return sortedList;
		}

		function data_GetSubcategories() {
			var options = new Array();
			var filt = new Array(
				["isinactive", "is", "F"]
			);
			var cols = [
				search.createColumn({name: "name"}),
				search.createColumn({name: "custrecord_ng_eh_item_category"})
			];
			var results = [];
			try {
				var itemSubCategorySearch = search.create({
					type: "customrecord_ng_eh_item_subcategory",
					filters: filt,
					columns: cols
				})
				var pagedData = itemSubCategorySearch.runPaged()
				util.each(pagedData.pageRanges, function (pageRange) {
					var myPage = pagedData.fetch({index: pageRange.index})
					util.each(myPage.data, function (result) {
						results.push(result)
					})
				})
			} catch (err) {
				log.error({title: 'Error running subcategory search', details: err})
			}
			var sortedOptions = []
			if (results.length !== 0) {
				for (var i = 0; i < results.length; i++) {
					options.push({
						id: results[i].id,
						value: String(results[i].getText({name: "custrecord_ng_eh_item_category"}) + " : " + search[i].getValue({name: "name"}))
					});
				}
			}
			sortedOptions = options.sort(function(a, b) {
				if (a.value < b.value) {
					return -1
				}
				if (a.value > b.value) {
					return 1
				}
				return 0
			})

			sortedOptions.unshift({
				id: "@NONE@",
				value: "N/A"
			});

			return sortedOptions;
		}

		function addTimeZoneOffsetHTML(form) {
			require(['N/ui/serverWidget'], function (widget) {
				form.addField({
					id: "custpage_offset",
					type: widget.FieldType.TEXT,
					label: "tz offset"
				}).updateDisplayType({displayType: widget.FieldDisplayType.HIDDEN});
				var html = '<script type="text/javascript">';
				html += 'setTimeout(function() { ';
				html += 'nlapiSetFieldValue("custpage_offset", (new Date()).getTimezoneOffset()); ';
				html += '}, 1000);';
				html += '</script>';
				form.addField({
					id: "custpage_offset_html",
					type: widget.FieldType.INLINEHTML,
					label: "tz offset script"
				}).defaultValue = html;
			});
		}

		function setOffsetPrintDate(request) {
			var localOffset = new Number(request.parameters['fst']);
			var printDate = new Date();
			var offset = printDate.getTimezoneOffset();
			printDate.setMinutes(printDate.getMinutes() + offset);
			printDate.setMinutes(printDate.getMinutes() - localOffset);
			return printDate;
		}

		function getAllResultsFor (searchObj, callback) {
			var myPagedData = searchObj.runPaged();
			myPagedData.pageRanges.forEach(function (pageRange) {
				var myPage = myPagedData.fetch({index: pageRange.index});
				myPage.data.forEach(function (result) {
					callback(result)
				});
			})
		}

		function objFilter(obj, func) {
			if (!obj) {
				return null;
			}
			if (!func) {
				return null;
			}
			var result = new Array();
			for (var key in obj) {
				if (func(obj[key])) {
					result.push(obj[key]);
				}
			}
			return result;
		}

		function objFind(obj, func) {
			if (!obj) {
				return null;
			}
			if (!func) {
				return null;
			}
			for (var key in obj) {
				if (func(obj[key])) {
					return this[obj];
				}
			}

			return null;
		}

		function xml_StyleSheet() {
			var xml = '';
			xml += '<style>';
			xml += 'body { font-family:Helvetica; }';
			xml += '.h_cell_a { white-space:nowrap; overflow:hidden; text-align:left; }';
			xml += '.h_cell_a p { font-size:16px; width:100%; text-align:left; color:#000000; }';
			xml += '.h_cell_b { white-space:nowrap; text-align:left; }';
			xml += '.h_cell_b p { font-size:12px; width:100%; text-align:left; color:#333333; }';
			xml += '.h_cell_c { text-align:left; }';
			xml += '.h_cell_c p { font-size:12px; width:100%; text-align:left; color:#000000; vertical-align:top; }';
			xml += '.h_cell_d { text-align:left; }';
			xml += '.h_cell_d p { font-size:12px; width:100%; text-align:left; color:#000000; vertical-align:top; }';
			xml += '.h_cell_e { white-space:nowrap; text-align:right; }';
			xml += '.h_cell_e p { font-size:12px; width:100%; text-align:right; color:#333333; }';
			xml += '.h_cell_f { white-space:nowrap; text-align:center; }';
			xml += '.h_cell_f p { font-size:12px; width:100%; text-align:center; color:#333333; }';
			xml += '.h_cell_g { white-space:nowrap; overflow:hidden; text-align:left; }';
			xml += '.h_cell_g p { font-size:14px; width:100%; text-align:left; color:#000000; }';
			xml += '.hdr_cell_a { color:#000000; background-color:#DDDDDD; padding:3px; }';
			xml += '.hdr_cell_a p { width: 100%; text-align:center; }';
			xml += '.hdr_cell_b { width:90%; color:#000000; background-color:#DDDDDD; }';
			xml += '.hdr_cell_c { width:45%; color:#000000; background-color:#DDDDDD; }';
			xml += '.hdr_cell_d { width:10%; color:#000000; background-color:#DDDDDD; }';
			xml += '.hdr_cell_d p { width: 100%; text-align:right; }';
			xml += '</style>\n';
			return xml;
		}

		function xml_PageHeaderReduced(reportTitle, headerImageURL) {
			var xml = '';
			xml += '<macro id="header">';
			xml += '<div style="position:absolute;" x="0%" y="0%"><table table-layout="fixed" border="0" style="width:720px; margin:0; padding:0; overflow:hidden;" cellmargin="0" cellpadding="0">';
			xml += new String('<tr><td style="overflow:hidden; height:0.93in;"><img src="' + (headerImageURL || "${companyInformation.logoUrl}") + '" width="2.5in" height="0.50in" style="margin:0; padding:0;" /></td>')
			xml += new String('<td style="width:55%;"><p style="width:100%; align:left; vertical-align:top;"><span style="font-size:24px;">' + reportTitle + '</span><br />')
			xml += '</p>';
			xml += '</td></tr>';
			xml += '</table>';
			xml += '{ADDITIONAL_A}';
			xml += '</div>';
			xml += '</macro>\n';
			return xml;
		}

		function xml_PageFooter(printDate) {
			function dateToString(date, fmat) {
				return format.format({value: date, type: fmat || format.Type.DATE});
			}

			var xml = '';
			xml += '<macro id="footer">';
			xml += new String('<table border="0" style="width: 100%; margin-left: 15px; margin-right: 15px;"><tr><td><p style="font-size:8px; width:100%; text-align:left">' + dateToString(printDate, "datetimetz") + '</p></td><td><p style="font-size:8px; width:100%; text-align:right">Page <pagenumber/> of <totalpages/></p></td></tr></table>')
			xml += '</macro>';
			return xml;
		}

		var _DAY_OF_WEEK = [
			"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"
		];

		var _SHADED_HEX = "#F0F0F0";
		var _BODY_XML = '<body size="Letter" header="header" header-height="235px" footer="footer" footer-height="15px" style="margin: 0.25in; padding: 0;">\n';
		var _BODY_XML_REDUCED = '<body size="Letter" header="header" header-height="125px" footer="footer" footer-height="15px" style="margin: 0.25in; padding: 0;">\n';

		return {
			getRentalAvailability: getRentalAvailability
			, getReservedQuantitiesGrouped: getReservedQuantitiesGrouped
			, getReservedQuantities: getReservedQuantities
			, getReservedQuantitiesNoItem: getReservedQuantitiesNoItem
			, condenseReservedQuantities: condenseReservedQuantities
			, getItemCategoryData: getItemCategoryData
			, getItemSubCategoryData: getItemSubCategoryData
			, data_GetSubcategories: data_GetSubcategories
			, addTimeZoneOffsetHTML: addTimeZoneOffsetHTML
			, setOffsetPrintDate: setOffsetPrintDate
			, xml_StyleSheet: xml_StyleSheet
			, xml_PageHeaderReduced: xml_PageHeaderReduced
			, xml_PageFooter: xml_PageFooter
			, _DAY_OF_WEEK: _DAY_OF_WEEK
			, _SHADED_HEX: _SHADED_HEX
			, _BODY_XML: _BODY_XML
			, _BODY_XML_REDUCED: _BODY_XML_REDUCED
		};

	});

Object.defineProperty(Array.prototype, 'has',
	{
		value: function (predicate) {
			if (!this) {
				throw new TypeError('"this" is null or not defined');
			}
			if (!predicate) {
				throw new TypeError('predicate is null or not defined');
			}
			if (typeof predicate !== 'function') {
				throw new TypeError('predicate must be a function');
			}

			for (var i = 0; i < this.length; i++) {
				if (predicate(this[i])) {
					return true;
				}
			}

			return false;
		}
		, configurable: true
		, writable: true
	}
);
