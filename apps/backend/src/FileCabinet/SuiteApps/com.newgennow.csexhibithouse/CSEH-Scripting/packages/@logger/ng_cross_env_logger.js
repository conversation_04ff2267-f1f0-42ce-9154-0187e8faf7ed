/**
 * @NApiVersion 2.1
 */
define([], () => {
  /**
   * @typedef {Object} Logger
   * @property {function} log Simulates a console.log or log.debug
   * @property {function} warn Simulates a console.warn
   * @property {function} error Simulates a console.error or log.error
   * @property {function} dir Simulates a console.dir
   * @property {function} info Simulates a console.info
   * @property {function} audit Simulates a log.audit
   * @property {function} emergency Simulates a log.emergency
   * */

  /**
   * Logs console for client env or execution logs for server env
   *
   *
   * @example ```javascript
   * const logger = useCrossLog()
   *
   * // Will run on both client and server
   * logger.log('Hi', {foo: 'bar'})
   *
   * // Specific to client
   * var clientName = 'Lorem'
   * logger.warn('Warning', clientName )
   *
   * // Server specific
   * var clientName = 'lorem'
   * logger.audit('Person', clientName)
   * ```
   * */
  function useCrossLog() {
    let runningEnv = isEnvType();
    let isServer = runningEnv === "server";
    let isClient = runningEnv === "client";
    let logger = {};

    let clientLogger = {
      log: (title, ...args) => console.log(title, ...args),
      warn: (title, ...args) => console.warn(title, ...args),
      error: (title, ...args) => console.error(title, ...args),
      dir: (title, ...args) => console.dir(title, ...args),
      info: (title, ...args) => console.info(title, ...args),
      debug: (title, ...args) => console.debug(title, ...args),
    };

    let serverLogger = {
      log: (title, ...rest) => log.debug({ title, details: [...rest] }),
      audit: (title, ...rest) => log.audit({ title, details: [...rest] }),
      error: (title, ...rest) => log.error({ title, details: [...rest] }),
      emergency: (title, ...rest) =>
        log.emergency({ title, details: [...rest] }),
    };

    if (isServer) {
      logger = serverLogger;
    } else if (isClient) {
      logger = clientLogger;
    } else {
      logger = null;
    }

    return logger;
  }

  function isEnvType() {
    let envType = "";
    try {
      console.log("Its client side");
      envType = "client";
    } catch (err) {
      envType = "server";
    }

    log.audit({ title: "Is ENV Type:", details: envType });

    return envType;
  }

  return useCrossLog
});
