/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(["N/file", "N/query", "N/record", "N/runtime", "N/url", "N/search"]
/**
 * @param{file} file
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{url} url
 * @param{search} search
 */, (file, query, record, runtime, url, search) => {
  /**
   * Defines the Suitelet script trigger point.
   * @param {Object} scriptContext
   * @param {ServerRequest} scriptContext.request - Incoming request
   * @param {ServerResponse} scriptContext.response - Suitelet response
   * @since 2015.2
   */
  const onRequest = (scriptContext) => {
    let sc = scriptContext;
    let res = sc.response;
    let req = sc.request;
    let requestParams = req.parameters;

    let currentUser = runtime.getCurrentUser();
    let userId = currentUser.id;
    let isLocal = userId === -4 || false;

    const getAllResultsFor = (searchObj, callback) => {
      let myPagedData = searchObj.runPaged();
      myPagedData.pageRanges.forEach(function (pageRange) {
        let myPage = myPagedData.fetch({ index: pageRange.index });
        myPage.data.forEach(function (result) {
          callback(result);
        });
      });
    };

    try {
      let allProjectQueried = query
        .runSuiteQL({
          query: !isLocal
            ? `SELECT * FROM JOB WHERE isinactive = 'F' 
                          AND parent = '${userId}'
                          AND custentity_ng_eh_disp_in_portal = 'T'`
            : `SELECT * FROM JOB WHERE isinactive = 'F' 
                          AND custentity_ng_eh_disp_in_portal = 'T'`,
        })
        .asMappedResults();

      log.audit({ title: "Projects Queried: ", details: allProjectQueried });

      // get project list to use in cases filter
      const allProjectsArr = allProjectQueried.map((job) => job.id);
      allProjectsArr.push(userId);

      let parsedSqlProjects = allProjectsArr.map((id) => `'${id}'`).join(",");

      let casesQueried = query
        .runSuiteQL({
          query: `SELECT * FROM supportCase WHERE 
                            custevent_ng_eh_disp_task_in_cust_prtl = 'T'
                            AND
                            company IN (${parsedSqlProjects})
                            `,
        })
        .asMappedResults();

      log.debug("casesQueried", casesQueried + " " + casesQueried.length);

      let updatedCases = casesQueried.map((cs) => {
        const currentCase = search.create({
          type: "supportcase",
          filters: [
            ["messages.internalonly", "is", "F"],
            "AND",
            ["internalid", "anyof", cs.id],
          ],
          columns: [
            search.createColumn({
              name: "casenumber",
              sort: search.Sort.ASC,
            }),
            search.createColumn({ name: "title" }),
            search.createColumn({ name: "internalid", join: "messages" }),
            search.createColumn({ name: "subject", join: "messages" }),
            search.createColumn({
              name: "messagedate",
              join: "messages",
              sort: search.Sort.DESC,
            }),
            search.createColumn({ name: "recipient", join: "messages" }),
            search.createColumn({ name: "recipientemail", join: "messages" }),
            search.createColumn({ name: "author", join: "messages" }),
            search.createColumn({ name: "authoremail", join: "messages" }),
            search.createColumn({ name: "attachments", join: "messages" }),
            search.createColumn({ name: "hasattachment", join: "messages" }),
            search.createColumn({ name: "internalonly", join: "messages" }),
            search.createColumn({ name: "isemailed", join: "messages" }),
            search.createColumn({ name: "message", join: "messages" }),
          ],
        });

        let messageCount = [];

        getAllResultsFor(currentCase, (result) => {
          messageCount.push(
            result.getValue({ name: "internalid", join: "messages" })
          );
        });
        let totalMessages = messageCount.filter((msg) => msg).length;
        return {
          ...cs,
          messageCount: totalMessages,
        };
      });

      res.write({
        output: JSON.stringify({
          cases: updatedCases,
        }),
      });
    } catch (err) {
      log.error({
        title: "Error retrieving cases:",
        details: {
          ...err,
        },
      });

      res.write({
        output: JSON.stringify({
          error: err,
        }),
      });
    }
  };

  return { onRequest };
});
