/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */

define(["N/search"], function (s) {
  function onRequest(context) {
    // Instantiate obj container
    let obj = {};

    // set item internalid url parameter
    const itemId =
      context.request.parameters["custscript_custom_item_internalid"];

    // Get items obj
    const currentItemOrders = s
      .create({
        type: s.Type.NON_INVENTORY_ITEM,
        filters: [
          ["internalid", "is", itemId],
          "AND",
          ["transaction.type", "anyof", "SalesOrd"],
        ],
        columns: [
          s.createColumn({ name: "itemid" }),
          s.createColumn({ name: "displayname" }),
          s.createColumn({ name: "salesdescription" }),
          s.createColumn({ name: "location" }),
          s.createColumn({ name: "custitem_ng_eh_customer_owned" }),
          s.createColumn({ name: "created" }),
          s.createColumn({ name: "custitem_ng_eh_item_primary_image" }),
          s.createColumn({
            name: "internalid",
            join: "transaction",
          }),
          s.createColumn({
            name: "trandate",
            join: "transaction",
            sort: s.Sort.DESC,
          }),
          s.createColumn({
            name: "quantity",
            join: "transaction",
          }),
          s.createColumn({
            name: "transactionname",
            join: "transaction",
          }),
          s.createColumn({
            name: "custcol_ng_cs_rental_loc",
            join: "transaction",
          }),
          s.createColumn({
            name: "custcol_ng_cs_rental_start_date",
            join: "transaction",
          }),
          s.createColumn({
            name: "custcol_ng_cs_rental_end_date",
            join: "transaction",
          }),
          s.createColumn({
            name: "entity",
            join: "transaction",
          }),
        ],
      })
      .run()
      .getRange({ start: 0, end: 1000 });

    // Clean up ns object
    const itemSort = currentItemOrders.map((r) => {
      return {
        id: r.id,
        customer: r.getText({ name: "custitem_ng_eh_customer_owned" }),
        created: r.getValue({ name: "created" }),
        itemid: r.getValue({ name: "itemid" }),
        displayname: r.getValue({ name: "displayname" }),
        salesdescription: r.getValue({ name: "salesdescription" }),
        location: r.getText({ name: "location" }),
        custitem_ng_eh_item_primary_image: r.getText({
          name: "custitem_ng_eh_item_primary_image",
        }),
        orders: {
          internalid: r.getValue({ name: "internalid", join: "transaction" }),
          project: r.getText({
            name: "entity",
            join: "transaction",
          }),
          trandate: r.getValue({ name: "trandate", join: "transaction" }),
          quantity: r.getValue({ name: "quantity", join: "transaction" }),
          transactionname: r.getValue({
            name: "transactionname",
            join: "transaction",
          }),
          custcol_ng_cs_rental_loc: r.getText({
            name: "custcol_ng_cs_rental_loc",
            join: "transaction",
          }),
          custcol_ng_cs_rental_start_date: r.getValue({
            name: "custcol_ng_cs_rental_start_date",
            join: "transaction",
          }),
          custcol_ng_cs_rental_end_date: r.getValue({
            name: "custcol_ng_cs_rental_end_date",
            join: "transaction",
          }),
        },
      };
    });

    // Consolidate items by item
    let itemClean = itemSort.reduce((acc, curr) => {
      let match = acc.find((x) => x.id === curr.id); //+ item, order if needed
      if (!match) {
        match = { ...curr, orders: [] };
        acc.push(match);
      }
      match.orders.push(curr.orders);
      return acc;
    }, []);

    // Compile and return obj
    obj.itemId = itemId;
    obj.itemClean = itemClean;

    context.response.write({ output: JSON.stringify(obj) });
  }
  return {
    onRequest: onRequest,
  };
});
