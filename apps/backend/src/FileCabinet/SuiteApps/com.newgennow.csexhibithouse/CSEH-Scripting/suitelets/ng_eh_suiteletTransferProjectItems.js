/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
define(['N/record', 'N/runtime', 'N/search', 'N/ui/serverWidget', '../lib/newgen.library.v2'],
/**
 * @param {record} record
 * @param {runtime} runtime
 * @param {search} search
 * @param {serverWidget} serverWidget
 */
function(record, runtime, search, widget, NG) {
	
	/**
	 * Definition of the Suitelet script trigger point.
	 *
	 * @param {Object} context
	 * @param {ServerRequest} context.request - Encapsulation of the incoming request
	 * @param {ServerResponse} context.response - Encapsulation of the Suitelet response
	 * @Since 2015.2
	 */
	function onRequest(context) {
		if (context.request.method == "GET") {
			displayForm(context);
		} else {
			doWork(context);
		}
	}
	
	function displayForm(context) {
		var prjId = context.request.parameters['pid'];
		
		var form = widget.createForm({
				title: "Transfer Items"
		});
		
		if (NG.tools.isEmpty(prjId)) {
			var errFld = form.addField({
					id: "custpage_no_project_id"
				,	type: widget.FieldType.TEXT
				,	label: " "
			}).updateDisplayType({
				displayType : widget.FieldDisplayType.INLINE
			});
			errFld.defaultValue = "No project was defined. Please go back to a project record to launch this page.";
			context.response.writePage(form);
			return;
		}
		
		var prjRec = record.load({ type : "job" , id : prjId });
		var prjName = prjRec.getValue({ fieldId : "companyname" });
		var custId = prjRec.getValue({ fieldId : "parent" });
		var evtId = prjRec.getValue({ fieldId : "custentity_ng_eh_event_name" });
		
		if (NG.tools.isEmpty(custId)) {
			var errFld = form.addField({
					id: "custpage_no_customer_id"
				,	type: widget.FieldType.TEXT
				,	label: " "
			}).updateDisplayType({
				displayType : widget.FieldDisplayType.INLINE
			});
			errFld.defaultValue = "Project is not attached to a customer.";
			context.response.writePage(form);
			return;
		}
		
		form.addFieldGroup({
				id: "project_group"
			,	label: "Project"
		});
		form.addFieldGroup({
				id: "transfer_group"
			,	label: "Transfer Options"
		});
		
		var prjField = form.addField({
				id: "custpage_project"
			,	type: widget.FieldType.SELECT
			,	label: "Project"
			,	source: "job"
			,	container: "project_group"
		}).updateDisplayType({
			displayType : widget.FieldDisplayType.INLINE
		});
		prjField.defaultValue = prjId;
		
		var prjNameField = form.addField({
				id: "custpage_project_name"
			,	type: widget.FieldType.TEXT
			,	label: "Project Name"
			,	container: "project_group"
		}).updateDisplayType({
			displayType : widget.FieldDisplayType.INLINE
		});
		prjNameField.defaultValue = prjName;
		
		var evtField = form.addField({
				id: "custpage_event"
			,	type: widget.FieldType.SELECT
			,	label: "Event"
			,	source: "customrecord_ng_eh_show_event"
			,	container: "project_group"
		}).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});
		evtField.defaultValue = evtId;
		
		var fromFld = form.addField({
				id: "custpage_from_location"
			,	type: widget.FieldType.SELECT
			,	label: "From Location"
			,	source: "location"
			,	container: "transfer_group"
		});
		fromFld.isMandatory = true;
		
		var toFld = form.addField({
				id: "custpage_to_location"
			,	type: widget.FieldType.SELECT
			,	label: "To Location"
			,	source: "location"
			,	container: "transfer_group"
		});
		toFld.isMandatory = true;
	
		var subList = form.addSublist({ id : "proj_item_list" , label : "Project Items" , type : widget.SublistType.LIST });
		subList.addMarkAllButtons();
		subList.addField({ id : "selected" , label : "Select" , type : widget.FieldType.CHECKBOX });
		subList.addField({ id : "custcol_item" , label : "Item" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});
		subList.addField({ id : "custcol_item_id" , label : "Item ID" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.HIDDEN
		});
		subList.addField({ id : "custcol_element" , label : "Element" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});;		
		
		var filt = new Array(
				["isinactive","is","F"]
			,	"and"
			,	["custrecord_ng_eh_pi_project","anyof",[prjId]]
		);
		var cols = new Array(
				search.createColumn({ name : "custrecord_ng_eh_pi_item" })
			,	search.createColumn({ name : "itemid" , join : "custrecord_ng_eh_pi_item" , sort : search.Sort.ASC })
			,	search.createColumn({ name : "custitem_ng_eh_element_name" , join : "custrecord_ng_eh_pi_item" })
		);
		var results = NG.tools.getSearchResults("customrecord_ng_eh_project_item", filt, cols, null, 1000, false, true);
		
		if (results.length > 0) {
			for (var r = 0; r < results.length; r++) {
				subList.setSublistValue({ line : r , id : "custcol_item" , value : results[r].getValue(cols[1]) });
				subList.setSublistValue({ line : r , id : "custcol_item_id" , value : results[r].getValue({ name : "custrecord_ng_eh_pi_item" }) });
				subList.setSublistValue({ line : r , id : "custcol_element" , value : results[r].getText(cols[2]) || "" });
			}
		}
		
		
		form.addButton({
				id: "custpage_apply_filters_btn"
			,	label: "Cancel"
			,	functionName: "close()"
		});
	
		form.addSubmitButton({
				label: 'Submit'
		});
		form.addResetButton({
				label: 'Reset'
		});
		form.clientScriptModulePath = './ng_eh_clientTransferProjectItems.js';
		context.response.writePage(form);
	}
	
	function doWork(context) {
		var req = context.request;
		var prjId = req.parameters['custpage_project'];
		var evtId = req.parameters['custpage_event'];
		var lineCount = req.getLineCount({ group : "proj_item_list" });
		var fromId = req.parameters['custpage_from_location'];
		var toId = req.parameters['custpage_to_location'];
		
		var tOrd = record.create({ type : "transferorder" , isDynamic : true });
		tOrd.setValue({ fieldId : "location" , value : fromId });
		tOrd.setValue({ fieldId : "transferlocation" , value : toId });
		tOrd.setValue({ fieldId : "custbody_ng_eh_event_name" , value : evtId });
		
		for (var l = 0; l < lineCount; l++) {
			if (req.getSublistValue({ group : "proj_item_list" , name : "selected" , line : l }) == "T") {
				var itemId = req.getSublistValue({ group : "proj_item_list" , name : "custcol_item_id" , line : l });
				
				tOrd.selectNewLine({ sublistId : "item" });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "item" , value : itemId });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "quantity" , value : 1 });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "custcol_ng_eh_assoc_proj" , value : prjId });
				tOrd.commitLine({ sublistId : "item" });
			}
		}
		
		tOrd.save({ enableSourcing : true , ignoreMandatoryFields : true });
		
		var html = '';
		html += '<script type = "text/javascript"> ';
		html += 'setTimeout(function() { ';
		html += 'window.opener.location.reload(); ';
		html += 'window.close(); ';
		html += '}, 1000); ';
		html += '</script>';
		
		context.response.write({ output : html });
	}
	
	return {
			onRequest: onRequest
	};
	
});
