/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define(["N/query", "N/record", "N/runtime", "N/search", "N/url", "N/file"], /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{file} file
 */ (query, record, runtime, search, url, file) => {
  /**
   * Defines the Suitelet script trigger point.
   * @param {Object} scriptContext
   * @param {ServerRequest} scriptContext.request - Incoming request
   * @param {ServerResponse} scriptContext.response - Suitelet response
   * @since 2015.2
   */
  const onRequest = (scriptContext) => {
    let { request: req, response: res } = scriptContext;
    let { parameters: requestParams, method } = req;
    let { write, addHeader } = res;
    let action = requestParams?.action;
    let currentProjectId = requestParams?.project;
    let mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    let departmentsEnabled = runtime.isFeatureInEffect({
      feature: "DEPARTMENTS",
    });

    switch (method) {
      case "GET":
        // Look for an action type to determine what to do
        log.audit({ title: "⚡ Running GET with action:", details: action });
        switch (action) {
          case "getEstimates":
            let estimates = [];
            let estimateFilters = [["mainline", "is", "T"]];

            if (currentProjectId) {
              estimateFilters.push("AND", [
                "jobMain.internalid",
                "anyof",
                currentProjectId,
              ]);
            }

            let estimateSearch = search.create({
              type: search.Type.ESTIMATE,
              filters: estimateFilters,
              columns: [
                search.createColumn({
                  name: "tranid",
                  label: "Document Number",
                  sort: search.Sort.ASC,
                }),
                search.createColumn({
                  name: "trandate",
                  label: "Date",
                }),
                search.createColumn({
                  name: "title",
                  label: "Title",
                }),
                search.createColumn({
                  name: "entity",
                  label: "Customer",
                }),
                search.createColumn({
                  name: "statusref",
                  label: "Transaction Status",
                }),
                search.createColumn({
                  name: "entitystatus",
                  label: "Sale Status",
                }),
                search.createColumn({
                  name: "total",
                  label: "Total",
                }),
                search.createColumn({ name: "memo", label: "Memo" }),
                search.createColumn({
                  name: "companyname",
                  join: "jobMain",
                  label: "Project",
                }),
                search.createColumn({
                  name: "custbody_ng_eh_master_estimate",
                  label: "Is Master Estimate",
                }),
              ],
            });

            if (departmentsEnabled) {
              estimateSearch.columns.push(
                search.createColumn({
                  name: "custbody_ng_eh_division",
                  label: "Division",
                })
              );
            }

            let columns = estimateSearch.columns.map((col) => {
              let colObj = {
                field: col.name,
                headerName: col.label,
                flex: 1,
              };

              return colObj;
            });

            getAllResultsFor(estimateSearch, (result) => {
              let estimateLink = url.resolveRecord({
                recordType: "estimate",
                recordId: result.id,
                isEditMode: false,
              });

              let estimateResult = {
                id: result.id,
                tranid: result.getValue("tranid"),
                trandate: result.getValue("trandate"),
                title: result.getValue("title"),
                entitystatus: result.getText("entitystatus"),
                statusref: result.getValue("statusref"),
                memo: result.getValue("memo"),
                entity: result.getText("entity"),
                altname: result.getValue({ name: "companyname", join: "jobMain" }),
                total: result.getValue({ name: "total" }),
                custbody_ng_eh_master_estimate: result.getValue({
                  name: "custbody_ng_eh_master_estimate",
                }),
                link: `https://${mainUrl}${estimateLink}`,
              };
              departmentsEnabled &&
                (estimateResult.custbody_ng_eh_division = result.getText(
                  "custbody_ng_eh_division"
                ));

              estimates.push(estimateResult);
            });

            addHeader({
              name: "Content-Type",
              value: "application/json",
            });

            write({
              output: JSON.stringify({
                columns,
                rows: estimates,
              }),
            });
            break;

          default:
            addHeader({
              name: "Content-Type",
              value: "application/json",
            });

            log.audit({
              title: "❗That action is not found 404:",
              details: action,
            });

            write({
              output: JSON.stringify({
                error: "That GET action is not found 404",
                status: 404,
                action,
              }),
            });
            break;
        }
        // If no action is found, return a 404
        break;
      case "POST":
        // Look for an action type to determine what to do
        switch (action) {
          case "bundleEstimates":
            try {
              let body = JSON.parse(req.body);
              log.debug({ title: "Request Body:", details: body });
              const authCompanyId = runtime.getCurrentUser().id;
              let { estimates, estimateCount, project, primaryEstimate } = body;

              log.audit({
                title: "📃 Estimates gathered:",
                details: estimates,
              });

              log.audit({
                title: "🔢 Estimates to merge:",
                details: estimateCount,
              });

              log.audit({
                title: "🔢 Primary Estimate:",
                details: primaryEstimate,
              });

              addHeader({
                name: "Content-Type",
                value: "application/json",
              });

              if (estimates.length > 0) {
                // Start bundling estimates
                let masterEstimate = null;

                if (primaryEstimate.length > 0) {
                  log.debug("Master Est Creating From Copy");
                  masterEstimate = record.copy({
                    type: record.Type.ESTIMATE,
                    id: Number(primaryEstimate[0]),
                    isDynamic: true,
                  });

                  masterEstimate.setValue({
                    fieldId: "custbody_ng_eh_master_estimate",
                    value: true,
                  });

                  let masterEstLineCount = masterEstimate.getLineCount({
                    sublistId: "item",
                  });

                  for (let i = masterEstLineCount - 1; i >= 0; i--) {
                    let itemDisplay = masterEstimate.getSublistValue({
                      sublistId: 'item',
                      fieldId: 'item',
                      line: i
                    });

                    if (itemDisplay === 'End of Group') {
                      continue;
                    }

                    masterEstimate.removeLine({
                      sublistId: "item",
                      line: i,
                    });
                  }

                  // for (let i = 0; i < masterEstLineCount; i++) {
                  //   masterEstimate.selectLine({
                  //     sublistId: "item",
                  //     line: i,
                  //   });
                  //
                  //   // Set estimate origin
                  //   masterEstimate.setCurrentSublistValue({
                  //     sublistId: "item",
                  //     fieldId: "custcol_ng_eh_estimate_origin",
                  //     value: primaryEstimate[0],
                  //   });
                  //
                  //   // Set origin department
                  //   masterEstimate.setCurrentSublistValue({
                  //     sublistId: "item",
                  //     fieldId: "custcol_ng_eh_est_origin_department",
                  //     value: masterEstimate.getValue({
                  //       fieldId: "custbody_ng_eh_division",
                  //     }),
                  //   });
                  //
                  //   // Commit line
                  //   masterEstimate.commitLine({ sublistId: "item" });
                  // }
                } else {
                  masterEstimate = record.create({
                    type: record.Type.ESTIMATE,
                    isDynamic: true,
                  });

                  let projectLookup = search.lookupFields({
                    type: search.Type.JOB,
                    id: project,
                    columns: ["customer"],
                  });

                  log.audit({
                    title: "Setting customer on estimate from project:",
                    details: projectLookup.customer[0].value,
                  });

                  masterEstimate.setValue({
                    fieldId: "entity",
                    value: projectLookup.customer[0].value,
                  });

                  masterEstimate.setValue({
                    fieldId: "custbody_ng_eh_master_estimate",
                    value: true,
                  });

                  // Set the project
                  project &&
                    masterEstimate.setValue({
                      fieldId: "job",
                      value: Number(project),
                    });
                }

                let estimatesMerged = 0;

                // Load previous estimates
                estimates.forEach((estimate) => {
                  let estimateId = estimate.id;

                  let existingEstimate = record.load({
                    type: record.Type.ESTIMATE,
                    id: estimateId,
                  });
                  let originalDepartment = existingEstimate.getValue(
                    "custbody_ng_eh_division"
                  );

                  let estimateNum = "";
                  // Get line count for items
                  let newLines = existingEstimate.getLineCount({
                    sublistId: "item",
                  });
                  estimateNum = existingEstimate.getValue({
                    fieldId: "tranid",
                  });

                  let itemSublistFields = existingEstimate.getSublistFields({
                    sublistId: 'item'
                  });

                  log.debug('itemSublistFields', itemSublistFields)

                  // Add lines to master estimate
                  log.audit({
                    title: `Merging item lines onto new master from ${estimateNum}`,
                    details: "⌛",
                  });

                  for (let itemLine = 0; itemLine < newLines; itemLine++) {
                    let itemObj = {};

                    for (let field = 0; field < itemSublistFields.length; field++) {

                      itemObj = {
                        ...itemObj,
                        [itemSublistFields[field]]: existingEstimate.getSublistValue({
                          sublistId: "item",
                          fieldId: [itemSublistFields[field]],
                          line: itemLine,
                        })
                      }
                    }

                    let item = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "item",
                      line: itemLine,
                    });
                    let itemQuantity = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "quantity",
                      line: itemLine,
                    });
                    let itemPrice = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "price",
                      line: itemLine,
                    });
                    let itemRate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "rate",
                      line: itemLine,
                    });
                    let itemAmount = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "amount",
                      line: itemLine,
                    });
                    let itemType = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "itemtype",
                      line: itemLine,
                    });
                    let itemDescription = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "description",
                      line: itemLine,
                    });
                    let itemVendor = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_vendor_line",
                      line: itemLine,
                    });
                    let itemCategory = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_item_category",
                      line: itemLine,
                    });
                    let showOnPdf = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_show_on_pdf",
                      line: itemLine,
                    });
                    let rentalLocation = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_loc",
                      line: itemLine,
                    });
                    let rentalStartDate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_start_date",
                      line: itemLine,
                    });
                    let rentalEndDate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_end_date",
                      line: itemLine,
                    });

                    let costEstimateType = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "costestimatetype",
                      line: itemLine,
                    });

                    let costEstimate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "costestimate",
                      line: itemLine,
                    });

                    let costEstimateRate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "costestimaterate",
                      line: itemLine,
                    });

                    let markupTemplate = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_markup_template",
                      line: itemLine,
                    });

                    let markup = existingEstimate.getSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_markup",
                      line: itemLine,
                    });

                    log.debug({
                      title: "🟡 Setting item:",
                      details: itemObj,
                    });

                    if (itemType === "EndGroup") {
                      return;
                    }

                    masterEstimate.selectNewLine({ sublistId: "item" });

                    // Set item
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "item",
                      value: item,
                    });

                    // Set quantity
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "quantity",
                      value: itemQuantity,
                    });

                    // Set price
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "price",
                      value: itemPrice,
                    });

                    // Set rate
                    if (itemPrice === "-1") {
                      masterEstimate.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "rate",
                        value: itemRate,
                      });

                      // Set amount
                      masterEstimate.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "amount",
                        value: itemAmount,
                      });
                    }

                    // Set Vendor
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_vendor_line",
                      value: itemVendor,
                    });

                    // Set Item Category
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_item_category",
                      value: itemCategory,
                    });

                    // Set Show On PDF
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_show_on_pdf",
                      value: showOnPdf,
                    });

                    // Set Rental Location
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_loc",
                      value: rentalLocation,
                    });

                    // Set Rental Start Date
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_start_date",
                      value: rentalStartDate,
                    });

                    // Set Rental End Date
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_cs_rental_end_date",
                      value: rentalEndDate,
                    });

                    itemDescription &&
                      masterEstimate.setCurrentSublistValue({
                        sublistId: "item",
                        fieldId: "description",
                        value: itemDescription,
                      });

                    // Set estimate origin
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_estimate_origin",
                      value: estimateId,
                    });

                    // Set origin department
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_est_origin_department",
                      value: originalDepartment,
                    });

                    // Set cost estimate type
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "costestimatetype",
                      value: costEstimateType,
                    });

                    // Set cost estimate
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "costestimate",
                      value: costEstimate,
                    });

                    // Set cost estimate rate
                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "costestimaterate",
                      value: costEstimateRate,
                    });

                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_markup_template",
                      value: markupTemplate,
                    });

                    masterEstimate.setCurrentSublistValue({
                      sublistId: "item",
                      fieldId: "custcol_ng_eh_markup",
                      value: markup,
                    });

                    let fieldsToSkip = ['item', 'quantity', 'price', 'rate', 'amount', 'custcol_ng_cs_vendor_line', 'custcol_ng_eh_item_category', 'custcol_ng_eh_show_on_pdf', 'custcol_ng_cs_rental_loc', 'custcol_ng_cs_rental_start_date', 'custcol_ng_cs_rental_end_date', 'description', 'custcol_ng_eh_estimate_origin', 'custcol_ng_eh_est_origin_department', 'costestimatetype', 'costestimate', 'costestimaterate', 'custcol_ng_eh_markup_template', 'custcol_ng_eh_markup']

                    for (let field in itemObj) {
                      try {
                        if (fieldsToSkip.includes(field)) {
                          continue;
                        }

                        masterEstimate.setCurrentSublistValue({
                          sublistId: "item",
                          fieldId: field,
                          value: itemObj[field],
                          ignoreFieldChange: true
                        });

                      } catch (e) {
                        log.error('Error Setting Field', field)
                      }
                    }

                    // Commit line
                    masterEstimate.commitLine({ sublistId: "item" });
                  }

                  estimatesMerged++;
                });

                // Save master estimate
                let masterEstimateId = masterEstimate.save({
                  enableSourcing: true,
                  ignoreMandatoryFields: true,
                });

                // Load master estimate
                let masterEstimateLoaded = record.load({
                  type: record.Type.ESTIMATE,
                  id: masterEstimateId,
                });

                // Shoot success message
                log.audit({
                  title: "🎉 Master estimate created:",
                  details: masterEstimateId,
                });

                let masterEstimateLink = url.resolveRecord({
                  recordType: "estimate",
                  recordId: masterEstimateId,
                });

                let assembledUrl = `https://${mainUrl}${masterEstimateLink}`;

                // Return master estimate
                write({
                  output: JSON.stringify({
                    status: {
                      success: true,
                      message: "Master estimate created!",
                      code: 201,
                    },
                    masterEstimateId,
                    masterEstimate: masterEstimateLoaded,
                    estimateLink: assembledUrl,
                  }),
                });
              } else {
                log.audit({
                  title: "No estimates to generate:",
                  details:
                    "No estimates were found to generate a master estimate",
                });

                write({
                  output: JSON.stringify({
                    details: {
                      message: "No estimates submitted to be bundled",
                    },
                    status: {
                      message: "No estimates found",
                      code: 405,
                      success: false,
                    },
                  }),
                });
              }
            } catch (error) {
              log.error({
                title: "Error bundling estimates",
                details: error,
              });

              write({
                output: JSON.stringify({
                  error: "Error bundling estimates",
                  status: {
                    code: 500,
                    success: false,
                  },
                  details: error,
                }),
              });
            }

            break;
          default:
            log.audit({
              title: "❗That action is not found 404:",
              details: action,
            });
            write({ output: "That POST action is not found 404" });
            break;
        }
        break;
      // If no method is found, return a 404
      default:
        log.audit({
          title: "❗That method is not found 404:",
          details: method,
        });

        write({
          output: JSON.stringify({
            error: "That method is not found 404",
            status: 404,
          }),
        });
    }
  };

  const getAllResultsFor = (searchObj, callback) => {
    let myPagedData = searchObj.runPaged();
    myPagedData.pageRanges.forEach(function (pageRange) {
      let myPage = myPagedData.fetch({ index: pageRange.index });
      myPage.data.forEach(function (result) {
        callback(result);
      });
    });
  };

  return { onRequest };
});
