/**
 *@NApiVersion 2.1
 *@NScriptType Suitelet
 */
// this creates a Suitelet form that lets you create a project
define(["N/record", "N/log", "N/runtime", "N/search", "N/query"], function (
  record,
  log,
  runtime,
  s,
  query
) {
  function onRequest(context) {
    let sc = context
    let res = sc.response
    let req = sc.request

    res.addHeader({
      name: 'Content-Type',
      value: 'application/json'
    })


    const data = JSON.parse(context.request.body);
    log.audit({
      title: "data from react post",
      details: data,
    });

    const currentUser = runtime.getCurrentUser();
    const authCompanyId = currentUser.id;
    const ehSettings = query.runSuiteQL({
      query: 'SELECT * FROM CUSTOMRECORD_NG_EH_SETTINGS'
    }).asMappedResults()[0]

  /*  const ehSettings = s.lookupFields({
      type: "customrecord_ng_eh_settings",
      id: 1,
      columns: [
        "custrecord_ng_eh_ship_date_days_before",
        "custrecord_ng_eh_return_date_days_after",
        "custrecord_ng_eh_default_inv_ship_loc",
        "custrecord_ng_eh_default_prj_template",
      ],
    });*/

    log.audit({
      title: "ehSettings",
      details: ehSettings,
    });

    try {
      // create project record
      const creatProj = record.create({
        type: record.Type.JOB,
      });

      creatProj.setValue({
        fieldId: "projecttemplate",
        value: ehSettings.custrecord_ng_eh_default_prj_template,
      });

      creatProj.setValue({
        fieldId: "parent",
        // if localdev (auth user ID is -4) then create new addresses against Anonymous Customer (customer ID = 3)
        // otherwise use actual authed company in prod
        value: authCompanyId === -4 ? 3 : authCompanyId,
      });
      creatProj.setValue({
        fieldId: "companyname",
        value: data.projname,
      });
      creatProj.setValue({
        fieldId: "custentity_ng_eh_booth_description",
        value: data.spacedesc,
      });
      creatProj.setValue({
        fieldId: "custentity_ng_eh_inv_shipment_location",
        value: ehSettings.custrecord_ng_eh_default_inv_ship_loc,
      });
      if (data.materialarrival) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_material_arrival",
          value: new Date(data.materialarrival),
        });
      }
      if (data.moveindate) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_event_movein_date",
          value: new Date(data.moveindate),
        });
      }
      if (data.eventstartend) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_event_start_date",
          value: new Date(data.eventstartend[0]),
        });
      }
      if (data.eventstartend) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_event_end_date",
          value: new Date(data.eventstartend[1]),
        });
      }
      if (data.moveoutdate) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_event_moveout_date",
          value: new Date(data.moveoutdate),
        });
      }
      if (data.materialpickup) {
        creatProj.setValue({
          fieldId: "custentity_ng_eh_material_pickup",
          value: new Date(data.materialpickup),
        });
      }
      creatProj.setValue({
        fieldId: "custentity_ng_eh_booth_size",
        value: data.boothsize,
      });
      creatProj.setValue({
        fieldId: "custentity_ng_eh_booth_description",
        value: data.spacedesc,
      });
      creatProj.setValue({
        fieldId: "custentity_ng_eh_event_address",
        value: data.shippingaddress,
      });

      // mike's calcluation to set ship date via CS setting option
      const shipDate = addDays(
          new Date(data.materialarrival),
          (ehSettings.custrecord_ng_eh_ship_date_days_before || 0) * -1
      );
      creatProj.setValue({
        fieldId: "custentity_ng_eh_ship_date",
        value: shipDate,
      });
      // mike's calcluation to set return date via CS setting option
      const rtrnDate = addDays(
          new Date(data.materialpickup),
          ehSettings.custrecord_ng_eh_return_date_days_after || 0
      );
      creatProj.setValue({
        fieldId: "custentity_ng_eh_return_date",
        value: rtrnDate,
      });
      creatProj.setValue({
        fieldId: "custentity_ng_eh_disp_in_portal",
        value: true,
      });

      let newProj = creatProj.save();

      if (newProj) {
        log.audit({ title: 'Project created Success!', details: newProj})
        res.write(JSON.stringify({
          message: 'Project create success!',
          projectId: newProj
        }))
      } else {
        log.audit({ title: 'No project was created', details: 'Issue with save perhaps?'})
        res.write(JSON.stringify({
            message: 'Project did no create!'
        }))
      }

      function addDays(date, days) {
        return new Date(date.getTime() + days * 24 * 60 * 60 * 1000);
      }
    } catch (e) {
      log.error({ title: 'Error occured with project create', details: e })
      res.write(JSON.stringify(e))
    }

  }
  return {
    onRequest: onRequest,
  };
});
