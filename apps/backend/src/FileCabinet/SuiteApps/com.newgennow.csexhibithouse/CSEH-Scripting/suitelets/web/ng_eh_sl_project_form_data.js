/**
 * @NApiVersion 2.1
 * @NScriptType Suitelet
 */
define([
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/url",
  "../../packages/@extended/ng_extended_utils",
  "../../packages/@settings/ng_eh_cm_settings_hooks",
], /**
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 * @param{ngExtendedUtils} ngExtendedUtils
 * @param{ngSettings} ngSettings
 */ (query, record, runtime, search, url, ngExtendedUtils, ngSettings) => {
  const FORM_RECORD_TYPE = "customrecord_ng_eh_cs_project_form";

  /**
   * Fetches all project forms
   * @returns {Array} Array of form objects with id and name
   */
  function fetchAllProjectForms(projectId) {
    try {
      let formSelected = null;
      const currentUser = runtime.getCurrentUser();
      const customerId = currentUser.id;
      const contactId = currentUser.contact;

      // Build the search filters
      const filters = [];

      // Apply permission filtering only when projectId is not provided
      if (!projectId) {
        // Create a complex filter for permissions
        // Form is available if:
        // 1. Both permission fields are empty (open to all)
        // 2. Customer ID matches in custrecord_customer_level_permissions
        // 3. Contact ID matches in custrecord_contact_level_permissions (only if customer doesn't match)

        const permissionFilters = [
          // Case 1: Both fields are empty (open form)
          [
            ["custrecord_contact_level_permissions", "anyof", "@NONE@"],
            "AND",
            ["custrecord_customer_level_permissions", "anyof", "@NONE@"],
          ],
        ];

        // Case 2: Customer ID matches
        if (customerId && customerId !== -4) {
          permissionFilters.push("OR");
          permissionFilters.push([
            "custrecord_customer_level_permissions",
            "anyof",
            customerId,
          ]);
        }

        // Case 3: Contact ID matches (but only if customer doesn't match)
        if (contactId) {
          permissionFilters.push("OR");
          permissionFilters.push([
            "custrecord_contact_level_permissions",
            "anyof",
            contactId,
          ]);
        }

        filters.push(permissionFilters);
      }

      const formSearch = search.create({
        type: FORM_RECORD_TYPE,
        filters: filters,
        columns: [
          search.createColumn({ name: "name", label: "Name" }),
          search.createColumn({
            name: "custrecord_contact_level_permissions",
            label: "Contact Permissions",
          }),
          search.createColumn({
            name: "custrecord_customer_level_permissions",
            label: "Customer Permissions",
          }),
        ],
      });

      // Run a lookup fields to get the current project form selected on the record
      if (projectId) {
        const projectSelectedLookup = search.lookupFields({
          type: record.Type.JOB,
          id: projectId,
          columns: ["custentity_ng_eh_cs_project_form"],
        });
        log.debug("Project Selected Lookup", projectSelectedLookup);

        if (
          Array.isArray(
            projectSelectedLookup.custentity_ng_eh_cs_project_form
          ) &&
          projectSelectedLookup.custentity_ng_eh_cs_project_form.length > 0
        ) {
          const formId =
            projectSelectedLookup.custentity_ng_eh_cs_project_form[0].value;
          formSelected = Number(formId);
        }
      }

      const forms = [];
      ngExtendedUtils.getAllResultsFor(formSearch, (result) => {
        forms.push({
          id: result.id,
          name: result.getValue("name"),
          selected: Number(result.id) === Number(formSelected),
        });
      });

      // Log permission filtering details for debugging
      if (!projectId) {
        log.debug("Permission Filtering Applied", {
          customerId: customerId,
          contactId: contactId,
          formsFound: forms.length,
        });
      }

      return forms;
    } catch (error) {
      log.error("Error fetching project forms", error);
      throw error;
    }
  }

  /**
   * Checks if the current user has permission to use a specific form
   * @param {string} formId - The internal ID of the form
   * @returns {Object} Object with hasPermission boolean and message
   */
  function checkFormPermission(formId) {
    try {
      const currentUser = runtime.getCurrentUser();
      const customerId = currentUser.id;
      const contactId = currentUser.contact;

      log.debug("🔐 Permission Check Started", {
        formId: formId,
        customerId: customerId,
        contactId: contactId,
        userRole: currentUser.role,
        userEmail: currentUser.email,
      });

      // Lookup the permission fields for the form
      const formPermissions = search.lookupFields({
        type: FORM_RECORD_TYPE,
        id: formId,
        columns: [
          "custrecord_contact_level_permissions",
          "custrecord_customer_level_permissions",
        ],
      });

      const contactPermissions =
        formPermissions.custrecord_contact_level_permissions || [];
      const customerPermissions =
        formPermissions.custrecord_customer_level_permissions || [];

      log.debug("📋 Form Permissions Retrieved", {
        formId: formId,
        contactPermissions: contactPermissions.map((p) => ({
          id: p.value,
          text: p.text,
        })),
        customerPermissions: customerPermissions.map((p) => ({
          id: p.value,
          text: p.text,
        })),
        contactPermissionsCount: contactPermissions.length,
        customerPermissionsCount: customerPermissions.length,
      });

      // If both permission fields are empty, the form is open to all
      if (contactPermissions.length === 0 && customerPermissions.length === 0) {
        log.debug("✅ Permission Granted - Open Form", {
          reason: "Both permission fields are empty",
          formId: formId,
        });
        return {
          hasPermission: true,
          message: "Form is open to all users",
        };
      }

      // Check if customer ID matches (takes priority)
      if (customerId && customerId !== -4) {
        const customerHasPermission = customerPermissions.some(
          (perm) => Number(perm.value) === Number(customerId)
        );

        log.debug("🏢 Customer Permission Check", {
          customerId: customerId,
          customerHasPermission: customerHasPermission,
          customerPermissionIds: customerPermissions.map((p) => p.value),
          matchFound: customerPermissions.find(
            (p) => Number(p.value) === Number(customerId)
          ),
        });

        if (customerHasPermission) {
          log.debug("✅ Permission Granted - Customer Match", {
            customerId: customerId,
            formId: formId,
          });
          return {
            hasPermission: true,
            message: "User has customer-level permission",
          };
        }
      }

      // Check if contact ID matches
      if (contactId) {
        const contactHasPermission = contactPermissions.some(
          (perm) => Number(perm.value) === Number(contactId)
        );

        log.debug("👤 Contact Permission Check", {
          contactId: contactId,
          contactHasPermission: contactHasPermission,
          contactPermissionIds: contactPermissions.map((p) => p.value),
          matchFound: contactPermissions.find(
            (p) => Number(p.value) === Number(contactId)
          ),
        });

        if (contactHasPermission) {
          log.debug("✅ Permission Granted - Contact Match", {
            contactId: contactId,
            formId: formId,
          });
          return {
            hasPermission: true,
            message: "User has contact-level permission",
          };
        }
      }

      // No permission found
      log.debug("❌ Permission Denied - No Match", {
        formId: formId,
        customerId: customerId,
        contactId: contactId,
        customerPermissions: customerPermissions.map((p) => p.value),
        contactPermissions: contactPermissions.map((p) => p.value),
        reason: "User ID does not match any permission entries",
      });

      return {
        hasPermission: false,
        message: "User does not have permission to use this form",
      };
    } catch (error) {
      log.error("🚨 Error checking form permissions", {
        formId: formId,
        error: error.toString(),
        stack: error.stack,
      });
      throw error;
    }
  }

  /**
   * Fetches form fields for a specific form
   * @param {string} formId - The internal ID of the form
   * @param {Record} projectRecord - The project record
   * @returns {Object} Object containing form fields
   */
  function fetchFormFields(formId, projectRecord = null) {
    const currentUser = runtime.getCurrentUser();
    const userId = currentUser.id;

    try {
      // Using the getCustomFormFields function from ng_extended_utils
      return ngExtendedUtils.getCustomFormFields(
        formId,
        record, // record - module passed in
        userId === -4 ? 3 : userId, // entity - user id of currently logged in user -4 is the onlineformuser that has not customer ref so we use 3 for the local dev testing
        projectRecord
      );
    } catch (error) {
      log.error("Error fetching form fields", error);
      throw error;
    }
  }

  /**
   * Sends a JSON response
   * @param {Object} res - The server response object
   * @param {number} status - HTTP status code
   * @param {Object} data - Data to send in the response
   */
  function sendJsonResponse(res, status, data) {
    res.addHeader({
      name: "Content-Type",
      value: "application/json",
    });
    res.write(
      JSON.stringify({
        status: status >= 200 && status < 300 ? "success" : "error",
        data: data,
      })
    );
    res.status = status;
  }

  /**
   * Defines the Suitelet script trigger point.
   * @param {Object} scriptContext
   * @param {ServerRequest} scriptContext.request - Incoming request
   * @param {ServerResponse} scriptContext.response - Suitelet response
   * @since 2015.2
   */
  const onRequest = (scriptContext) => {
    const { request: req, response: res } = scriptContext;
    const { method, parameters: requestParams } = req;
    const multiSubsidiaryCustomer = runtime.isFeatureInEffect({
      feature: "MULTISUBSIDIARYCUSTOMER",
    });
    const EH_SETTINGS = ngSettings.useSettings();

    log.debug({
      title: "Request Method",
      details: method,
    });

    log.debug({
      title: "Request Parameters",
      details: JSON.stringify(requestParams),
    });

    log.debug({
      title: "Request Body",
      details: JSON.stringify(req.body),
    });

    try {
      if (method === "GET") {
        let { formId, projectId } = requestParams;

        if (projectId === "undefined") {
          projectId = null;
        }

        if (formId) {
          const projectRecord =
            projectId &&
            record.load({
              type: record.Type.JOB,
              id: projectId,
            });

          // Get form fields for a specific form

          const formFields = fetchFormFields(formId, projectRecord);
          const projectFormSelected = search.lookupFields({
            type: FORM_RECORD_TYPE,
            id: formId,
            columns: ["name"],
          });

          formFields.formSelected = {
            id: formId,
            name: projectFormSelected.name,
          };

          sendJsonResponse(res, 200, formFields);
        } else {
          // Get all project forms
          const forms = fetchAllProjectForms(projectId);
          // If project is selected load form selected on project
          if (projectId) {
            const projectSelectedLookup = search.lookupFields({
              type: record.Type.JOB,
              id: projectId,
              columns: ["custentity_ng_eh_cs_project_form"],
            });
            log.debug("Project Selected Lookup", projectSelectedLookup);

            // if (Array.isArray(projectSelectedLookup.custentity_ng_eh_cs_project_form) && projectSelectedLookup.custentity_ng_eh_cs_project_form.length > 0) {
            //     const formId = projectSelectedLookup.custentity_ng_eh_cs_project_form[0].value;
            //     const formSelected = fetchFormFields(formId);
            //     forms.formSelected = {
            //         id: formId,
            //         name: formSelected.name
            //     };
            // }
          }
          sendJsonResponse(res, 200, forms);
        }
      } else if (method === "POST") {
        // For posting projects
        try {
          log.audit("🚀 Starting project creation process");

          let {
            formId,
            userId,
            action,
            script,
            deploy,
            ns_at,
            compid,
            companyname,
            projectId,
            ...fieldValues
          } = requestParams;

          if (projectId === "undefined") {
            projectId = null;
          }

          let authCompanyId = Number(userId);

          log.debug("📋 Request Parameters:", {
            formId,
            userId,
            action,
            companyname,
            projectId,
            fieldCount: Object.keys(fieldValues).length,
          });

          // Basic validation
          if (!formId || !userId) {
            log.error("❌ Missing required parameters", { formId, userId });
            return sendJsonResponse(res, 400, {
              success: false,
              message: "Missing required parameters (formId, userId)",
            });
          }

          // Check form permissions
          log.audit("🔐 Checking form permissions");
          const permissionCheck = checkFormPermission(formId);
          if (!permissionCheck.hasPermission) {
            log.error("❌ Permission denied", {
              formId,
              userId,
              message: permissionCheck.message,
            });
            return sendJsonResponse(res, 403, {
              success: false,
              message: "Permission denied: " + permissionCheck.message,
            });
          }
          log.debug("✅ Permission granted", permissionCheck);

          log.audit("🔍 Fetching form fields for validation");
          const formFields = fetchFormFields(formId);

          // Log form fields for debugging
          log.debug("📄 Form Fields:", {
            totalFields: formFields.formFields?.length || 0,
            formFields: formFields.formFields,
            requiredFields:
              formFields.formFields?.filter((f) =>
                f.validations?.includes("required")
              ).length || 0,
          });

          // Include companyname in the values object for validation
          const allFieldValues = {
            ...fieldValues,
            companyname: companyname, // Add companyname to the values being validated
          };

          // Validate required fields
          const requiredFields =
            formFields.formFields
              ?.filter((field) => field.validations?.includes("required"))
              ?.map((field) => field.name) || [];

          // Special handling for companyname if it's a required field
          if (requiredFields.includes("companyname") && companyname) {
            allFieldValues.companyname = companyname;
          }

          const missingFields = requiredFields.filter(
            (fieldName) =>
              !allFieldValues[fieldName] && allFieldValues[fieldName] !== 0
          );

          if (missingFields.length > 0) {
            log.error("❌ Missing required fields", {
              missingFields,
              fieldValues: allFieldValues,
              requiredFields,
            });
            return sendJsonResponse(res, 400, {
              success: false,
              message: "Missing required fields",
              missingFields,
              receivedFields: Object.keys(allFieldValues),
            });
          }

          const fileFields = formFields.formFields?.filter(
            (field) => field.type === "file"
          );
          const filesRecieved = req.files;

          log.debug(
            "filesRecieved - will add files after project is created",
            filesRecieved
          );

          // Parse files by field name
          const filesByField = {};
          if (filesRecieved) {
            Object.entries(filesRecieved).forEach(([key, file]) => {
              // Extract field name from key format: fieldName[index]
              const match = key.match(/^(.+?)\[(\d+)\]$/);
              if (match) {
                const fieldName = match[1];
                if (!filesByField[fieldName]) {
                  filesByField[fieldName] = {};
                }
                filesByField[fieldName][key] = file;
                fieldValues[fieldName] = file;
              }
            });
          }

          log.debug("Files organized by field", {
            fields: Object.keys(filesByField),
            counts: Object.entries(filesByField).reduce(
              (acc, [field, files]) => {
                acc[field] = Object.keys(files).length;
                return acc;
              },
              {}
            ),
          });

          let projectRecord = null;

          if (action === 'UPDATEPROJ' && projectId) {
            log.audit("🔄 Loading existing project record for update", { projectId });
            projectRecord = record.load({
              type: record.Type.JOB,
              id: projectId,
            });
          } else if (action === 'COPYPROJ' && projectId) {
            log.audit("🔄 Copying existing project record", { projectId });
            projectRecord = record.copy({
              type: record.Type.JOB,
              id: projectId,
            });
          } else {
            log.audit("🏗️ Creating new project record");
            projectRecord = record.create({
              type: record.Type.JOB,
              isDynamic: true,
            });
          }

          // Only set parent for new projects or copies
          if (action !== 'UPDATEPROJ') {
            projectRecord.setValue({
              fieldId: "parent",
              value: authCompanyId === -4 ? 3 : authCompanyId,
            });

            // Set project expense type default to "Regular"
            projectRecord.setValue({
              fieldId: "projectexpensetype",
              value: "-2",
            });
          }

          // Set basic project info
          log.debug("📝 Setting project metadata", { formId, userId });
          projectRecord.setValue({
            fieldId: "custentity_ng_eh_cs_project_form",
            value: formId,
          });

          // Only set creation date for new projects
          if (action !== 'UPDATEPROJ') {
            const today = new Date();
            projectRecord.setValue({
              fieldId: "custrecord_ng_eh_cs_project_date",
              value: today,
            });

            projectRecord.setValue({
              fieldId: "custrecord_ng_eh_cs_project_status",
              value: 1, // Draft status
            });
          }

          projectRecord.setValue({
            fieldId: "custentity_ng_eh_disp_in_portal",
            value: true, // Display in portal by default
          });

          // Set company information if available
          if (companyname) {
            log.debug("🏢 Setting company name", { companyname });
            projectRecord.setValue({
              fieldId: "companyname",
              value: companyname,
            });
            // Also set it in fieldValues for any potential form field mappings
            fieldValues.companyname = companyname;
          }

          try {
            if (multiSubsidiaryCustomer) {
              if (EH_SETTINGS?.custrecord_ng_eh_project_subsidiary) {
                projectRecord.setValue({
                  fieldId: "subsidiary",
                  value: EH_SETTINGS?.custrecord_ng_eh_project_subsidiary,
                });
              }
            }
          } catch (e) {
            log.error(" 🚨 Error Setting Multisubsidiary Subsidiary", e);
          }

          // Set custom fields
          log.audit("🔄 Processing custom fields");
          let fieldCount = 0;
          const fileFieldsToProcess = []; // Track file fields for post-save processing

          Object.entries(fieldValues).forEach(([fieldName, fieldValue]) => {
            log.debug(`   🟨 ${fieldName} Being Processed`, {
              fieldName,
              fieldValue,
            });
            if (fieldValue !== null && fieldValue !== undefined) {
              // Skip system fields
              if (
                [
                  "formId",
                  "userId",
                  "action",
                  "script",
                  "deploy",
                  "ns_at",
                  "ns-at",
                  "compid",
                  "companyname",
                  "projectId",
                ].includes(fieldName)
              ) {
                return;
              }

              try {
                // Extract base field name for file fields (remove [index] suffix)
                const baseFieldName = fieldName.replace(/\[\d+\]$/, "");
                const fieldRef = formFields.formFields?.find(
                  (field) => field.name === baseFieldName
                );

                log.debug("Field Reference Lookup", {
                  originalFieldName: fieldName,
                  baseFieldName: baseFieldName,
                  fieldRef: fieldRef
                    ? { name: fieldRef.name, type: fieldRef.type }
                    : null,
                });

                if (!fieldRef) {
                  log.audit(
                    `⚠️ Field not found in form definition: ${fieldName} (base: ${baseFieldName})`
                  );
                  return;
                }

                if (fieldRef.type === "file") {
                  // Track file fields for processing after save
                  log.debug("File field detected, will process after save", {
                    fieldName: fieldName,
                    baseFieldName: baseFieldName,
                  });

                  if (filesByField[baseFieldName]) {
                    // Check if we already added this field to avoid duplicates
                    const alreadyAdded = fileFieldsToProcess.some(
                      (f) => f.fieldName === baseFieldName
                    );
                    if (!alreadyAdded) {
                      fileFieldsToProcess.push({
                        fieldName: baseFieldName,
                        files: filesByField[baseFieldName],
                      });
                    }
                  }

                  // Skip setting the file field value during initial save
                  return;
                }

                if (fieldRef.type === "select" && fieldRef?.multiple) {
                  // Transform select values to that contain a comma to be an array instead
                  const updatedValue = fieldValue.includes(",")
                    ? fieldValue.split(",")
                    : fieldValue;

                  log.debug("Multi-Select Value Transformed", {
                    originalValue: fieldValue,
                    transformedValue: updatedValue,
                  });

                  if (fieldValue.includes(",")) {
                    fieldValue = updatedValue;
                  }
                }

                const transformedValue =
                  ngExtendedUtils.transformCustomFieldValue(
                    fieldRef.type,
                    fieldValue
                  );
                log.debug(`   • Setting field: ${baseFieldName}`, {
                  type: fieldRef.type,
                  originalValue: fieldValue,
                  transformedValue,
                });

                projectRecord.setValue({
                  fieldId: baseFieldName,
                  value: transformedValue,
                });

                fieldCount++;
              } catch (error) {
                log.error(`❌ Error setting field ${fieldName}`, error);
              }
            }
          });

          log.audit(`💾 Saving project with ${fieldCount} custom fields`);
          const projectSavedId = projectRecord.save({
            enableSourcing: true,
            ignoreMandatoryFields: true,
          });

          log.audit("✅ Project saved successfully", { projectSavedId });

          // Process file fields after project is saved
          if (fileFieldsToProcess.length > 0) {
            log.audit("📎 Processing file attachments", {
              projectId: projectSavedId,
              fileFieldCount: fileFieldsToProcess.length,
            });

            // Load the saved project record for file processing
            const savedProjectRecord = record.load({
              type: record.Type.JOB,
              id: projectSavedId,
            });

            const fileFieldUpdates = {};

            // Process each file field
            fileFieldsToProcess.forEach(({ fieldName, files }) => {
              try {
                log.debug("Processing files for field", {
                  fieldName,
                  fileCount: Object.keys(files).length,
                });

                // Attach files and get the file IDs
                const attachedFiles =
                  ngExtendedUtils.addAndAttachFilesToProject(
                    savedProjectRecord,
                    files,
                    true, // includeFileNameInOutput
                    fieldName
                  );

                if (attachedFiles.length > 0) {
                  // Use the first file ID for the field
                  const fileId = attachedFiles[0].id;
                  fileFieldUpdates[fieldName] = fileId;

                  log.debug("File attached successfully", {
                    fieldName,
                    fileId,
                    totalAttached: attachedFiles.length,
                  });
                }
              } catch (error) {
                log.error("Failed to process files for field", {
                  fieldName,
                  error: error.toString(),
                });
              }
            });

            // Update all file fields at once using submitFields
            if (Object.keys(fileFieldUpdates).length > 0) {
              try {
                record.submitFields({
                  type: record.Type.JOB,
                  id: projectSavedId,
                  values: fileFieldUpdates,
                  options: {
                    enableSourcing: false,
                    ignoreMandatoryFields: true,
                  },
                });

                log.audit("✅ File fields updated successfully", {
                  projectId: projectSavedId,
                  updatedFields: Object.keys(fileFieldUpdates),
                });
              } catch (error) {
                log.error("Failed to update file fields", {
                  projectId: projectSavedId,
                  error: error.toString(),
                });
              }
            }
          }

          log.audit(`✅ Project creation completed`, { projectSavedId });
          
          const successMessage = action === 'UPDATEPROJ' 
            ? 'Project updated successfully' 
            : action === 'COPYPROJ' 
            ? 'Project copied successfully' 
            : 'Project created successfully';
          
          sendJsonResponse(res, 201, {
            success: true,
            projectId: projectSavedId,
            message: successMessage,
          });
        } catch (error) {
          log.error("🔥 Critical error in project creation", {
            error: error.toString(),
            stack: error.stack,
          });
          sendJsonResponse(res, 500, {
            success: false,
            message: "Failed to create project",
            error: error.toString(),
          });
        }
      } else if (method === "PUT") {
        // NetSuite doesn't handle multipart/form-data well with PUT requests
        // Redirect to POST handler with UPDATEPROJ action
        log.audit("🔀 Redirecting PUT to POST handler for file upload support");
        
        // Ensure action is set to UPDATEPROJ for PUT requests
        requestParams.action = 'UPDATEPROJ';
        
        // Call the POST handler logic
        req.method = 'POST';
        return onRequest(scriptContext);
      } else {
        // Method not allowed
        sendJsonResponse(res, 405, { message: "Method not allowed" });
      }
    } catch (error) {
      log.error("Error in project form data suitelet", error);
      sendJsonResponse(res, 500, {
        message: "Internal server error",
        error: error.toString(),
      });
    }
  };

  const folderSearch = (folderName) =>
    search
      .create({
        type: "folder",
        filters: [["name", "startswith", folderName]],
      })
      .run()
      .getRange(0, 1)[0];

  return { onRequest };
});
