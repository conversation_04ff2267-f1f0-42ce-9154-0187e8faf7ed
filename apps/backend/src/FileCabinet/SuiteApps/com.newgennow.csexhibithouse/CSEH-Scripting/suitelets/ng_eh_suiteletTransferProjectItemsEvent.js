/**
 * @NApiVersion 2.x
 * @NScriptType Suitelet
 * @NModuleScope SameAccount
 */
define(['N/record', 'N/runtime', 'N/search', 'N/ui/serverWidget', 'N/url', '../lib/newgen.library.v2'],
/**
 * @param {record} record
 * @param {runtime} runtime
 * @param {search} search
 * @param {serverWidget} serverWidget
 */
function(record, runtime, search, widget, url, NG) {
	
	/**
	 * Definition of the Suitelet script trigger point.
	 *
	 * @param {Object} context
	 * @param {ServerRequest} context.request - Encapsulation of the incoming request
	 * @param {ServerResponse} context.response - Encapsulation of the Suitelet response
	 * @Since 2015.2
	 */
	function onRequest(context) {
		if (context.request.method == "GET") {
			displayForm(context);
		} else {
			doWork(context);
		}
	}
	
	function displayForm(context) {
		var evtId = context.request.parameters['eid'];
		var prjFiltIds = !NG.tools.isEmpty(context.request.parameters['pflt']) ? context.request.parameters['pflt'].split(",") : [];
		var frmLoc = context.request.parameters['fl'];
		var toLoc = context.request.parameters['tl'];
		
		var form = widget.createForm({
				title: "Transfer Items"
		});
		
		if (NG.tools.isEmpty(evtId)) {
			var errFld = form.addField({
					id: "custpage_no_event_id"
				,	type: widget.FieldType.TEXT
				,	label: " "
			}).updateDisplayType({
				displayType : widget.FieldDisplayType.INLINE
			});
			errFld.defaultValue = "No event was defined. Please go back to an event record to launch this page.";
			context.response.writePage(form);
			return;
		}
		
		var evtRec = record.load({ type : "customrecord_ng_eh_show_event" , id : evtId });
		var locId = evtRec.getValue({ fieldId : "custrecord_ng_eh_event_location" });
		
		form.addFieldGroup({
				id: "event_group"
			,	label: "Event"
		});
		form.addFieldGroup({
				id: "transfer_group"
			,	label: "Transfer Options"
		});
		form.addFieldGroup({
				id: "filter_group"
			,	label: "Filters"
		});
		
		var evtField = form.addField({
				id: "custpage_event"
			,	type: widget.FieldType.SELECT
			,	label: "Event"
			,	source: "customrecord_ng_eh_show_event"
			,	container: "event_group"
		}).updateDisplayType({
			displayType : widget.FieldDisplayType.INLINE
		});
		evtField.defaultValue = evtId;
		
		var prjFilt = new Array(
				["custentity_ng_eh_event_name","anyof",[evtId]]
			,	"and"
			,	["isinactive","is","F"]
		);
		var prjCols = new Array(
				search.createColumn({ name : "entityid" , sort : search.Sort.ASC })
			,	search.createColumn({ name : "companyname" })
		);
		var prjData = NG.tools.getSearchResults("job", prjFilt, prjCols, null, 500, false, false);
		var prjs = NG.tools.getResultsIdList(prjData);
		
		var prjIdField = form.addField({
				id: "custpage_projects"
			,	type: widget.FieldType.MULTISELECT
			,	label: "Projects"
			,	source: "job"
			,	container: "event_group"
		}).updateDisplayType({
			displayType : widget.FieldDisplayType.INLINE
		});
		if (prjs.length > 0) {
			prjIdField.defaultValue = prjs;
		}
		
		var fromFld = form.addField({
				id: "custpage_from_location"
			,	type: widget.FieldType.SELECT
			,	label: "From Location"
			,	source: "location"
			,	container: "transfer_group"
		});
		fromFld.isMandatory = true;
		if (!NG.tools.isEmpty(frmLoc)) {
			fromFld.defaultValue = frmLoc;
		}
		
		var toFld = form.addField({
				id: "custpage_to_location"
			,	type: widget.FieldType.SELECT
			,	label: "To Location"
			,	source: "location"
			,	container: "transfer_group"
		});
		toFld.isMandatory = true;
		if (!NG.tools.isEmpty(toLoc)) {
			toFld.defaultValue = toLoc;
		} else if (!NG.tools.isEmpty(locId)) {
			toFld.defaultValue = locId;
		}
		
		var prjFltFld = form.addField({
				id: "custpage_prj_filters"
			,	type: widget.FieldType.MULTISELECT
			,	label: "Project Filter"
			,	container: "filter_group"
		});
		for (var p = 0; p < prjData.length; p++) {
			prjFltFld.addSelectOption({ value : prjData[p].id , text : "{0} : {1}".NG_Format(prjData[p].getValue({ name : "entityid" }), prjData[p].getValue({ name : "companyname" })) });
		}
		if (!NG.tools.isEmpty(prjFiltIds) && prjFiltIds.length > 0) {
			prjFltFld.defaultValue = prjFiltIds;
		}
		
		var urlFld = form.addField({
				id: "custpage_url"
			,	type: widget.FieldType.TEXT
			,	label: "url"
		}).updateDisplayType({
				displayType : widget.FieldDisplayType.HIDDEN
		});
		urlFld.defaultValue = url.resolveScript({
				scriptId: runtime.getCurrentScript().id
			,	deploymentId: runtime.getCurrentScript().deploymentId
			,	returnExternalUrl: false
		});
		
		var subList = form.addSublist({ id : "proj_item_list" , label : "Project Items" , type : widget.SublistType.LIST });
		subList.addMarkAllButtons();
		subList.addField({ id : "selected" , label : "Select" , type : widget.FieldType.CHECKBOX });
		subList.addField({ id : "custcol_item" , label : "Item" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});
		subList.addField({ id : "custcol_item_id" , label : "Item ID" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.HIDDEN
		});
		subList.addField({ id : "custcol_element" , label : "Element" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});;
		subList.addField({ id : "custcol_proj" , label : "Project" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.DISABLED
		});
		subList.addField({ id : "custcol_proj_id" , label : "Project ID" , type : widget.FieldType.TEXT }).updateDisplayType({
			displayType : widget.FieldDisplayType.HIDDEN
		});
		
		
		var filt = new Array(
				["isinactive","is","F"]
			,	"and"
		);
		log.audit({ title : "event prj IDs" , details : JSON.stringify(prjs) });
		log.audit({ title : "filter prj IDs" , details : JSON.stringify(prjFiltIds) });
		if (!NG.tools.isEmpty(prjFiltIds) && prjFiltIds.length > 0) {
			filt.push(["custrecord_ng_eh_pi_project","anyof",prjFiltIds]);
		} else {
			filt.push(["custrecord_ng_eh_pi_project","anyof",prjs]);
		}
		var cols = new Array(
				search.createColumn({ name : "custrecord_ng_eh_pi_item" })
			,	search.createColumn({ name : "itemid" , join : "custrecord_ng_eh_pi_item" , sort : search.Sort.ASC })
			,	search.createColumn({ name : "custrecord_ng_eh_pi_project" })
			,	search.createColumn({ name : "companyname" , join : "custrecord_ng_eh_pi_project" })
			,	search.createColumn({ name : "custitem_ng_eh_element_name" , join : "custrecord_ng_eh_pi_item" })
		);
		var results = NG.tools.getSearchResults("customrecord_ng_eh_project_item", filt, cols, null, 1000, false, true);
		
		if (results.length > 0) {
			for (var r = 0; r < results.length; r++) {
				subList.setSublistValue({ line : r , id : "custcol_item" , value : results[r].getValue(cols[1]) });
				subList.setSublistValue({ line : r , id : "custcol_item_id" , value : results[r].getValue({ name : "custrecord_ng_eh_pi_item" }) });
				subList.setSublistValue({ line : r , id : "custcol_element" , value : results[r].getText(cols[4]) || "" });
				subList.setSublistValue({ line : r , id : "custcol_proj" , value : "{0} : {1}".NG_Format(results[r].getText({ name : "custrecord_ng_eh_pi_project" }), results[r].getValue(cols[3])) });
				subList.setSublistValue({ line : r , id : "custcol_proj_id" , value : results[r].getValue({ name : "custrecord_ng_eh_pi_project" }) });
			}
		}
		
		
		form.addButton({
				id: "custpage_apply_filters_btn"
			,	label: "Apply Filters"
			,	functionName: "applyEventFilters()"
		});
	
		form.addButton({
				id: "custpage_cancel_btn"
			,	label: "Cancel"
			,	functionName: "close()"
		});
	
		form.addSubmitButton({
				label: 'Submit'
		});
		form.addResetButton({
				label: 'Reset'
		});
		form.clientScriptModulePath = './ng_eh_clientTransferProjectItems.js';
		context.response.writePage(form);
	}
	
	function doWork(context) {
		var req = context.request;
		var evtId = req.parameters['custpage_event'];
		var lineCount = req.getLineCount({ group : "proj_item_list" });
		var fromId = req.parameters['custpage_from_location'];
		var toId = req.parameters['custpage_to_location'];
		
		var tOrd = record.create({ type : "transferorder" , isDynamic : true });
		tOrd.setValue({ fieldId : "location" , value : fromId });
		tOrd.setValue({ fieldId : "transferlocation" , value : toId });
		tOrd.setValue({ fieldId : "custbody_ng_eh_event_name" , value : evtId });
		
		for (var l = 0; l < lineCount; l++) {
			if (req.getSublistValue({ group : "proj_item_list" , name : "selected" , line : l }) == "T") {
				var itemId = req.getSublistValue({ group : "proj_item_list" , name : "custcol_item_id" , line : l });
				var prjId = req.getSublistValue({ group : "proj_item_list" , name : "custcol_proj_id" , line : l });
				
				tOrd.selectNewLine({ sublistId : "item" });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "item" , value : itemId });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "quantity" , value : 1 });
				tOrd.setCurrentSublistValue({ sublistId : "item" , fieldId : "custcol_ng_eh_assoc_proj" , value : prjId });
				tOrd.commitLine({ sublistId : "item" });
			}
		}
		
		tOrd.save({ enableSourcing : true , ignoreMandatoryFields : true });
		
		var html = '';
		html += '<script type = "text/javascript"> ';
		html += 'setTimeout(function() { ';
		html += 'window.opener.location.reload(); ';
		html += 'window.close(); ';
		html += '}, 1000); ';
		html += '</script>';
		
		context.response.write({ output : html });
	}
	
	return {
			onRequest: onRequest
	};
	
});
