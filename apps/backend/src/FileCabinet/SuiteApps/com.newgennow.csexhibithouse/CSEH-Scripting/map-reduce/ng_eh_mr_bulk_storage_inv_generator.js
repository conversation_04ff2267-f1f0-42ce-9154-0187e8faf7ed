/**
 * @NApiVersion 2.1
 * @NScriptType MapReduceScript
 */
define(['N/record', 'N/search', 'N/runtime', 'N/url', 'N/email'],
    /**
 * @param{record} record
 * @param{search} search
 * @param{runtime} runtime
 * @param{url} url
 * @param{email} email
 */
    (record, search, runtime, url, email) => {
        /**
         * Defines the function that is executed at the beginning of the map/reduce process and generates the input data.
         * @param {Object} inputContext
         * @param {boolean} inputContext.isRestarted - Indicates whether the current invocation of this function is the first
         *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
         * @param {Object} inputContext.ObjectRef - Object that references the input data
         * @typedef {Object} ObjectRef
         * @property {string|number} ObjectRef.id - Internal ID of the record instance that contains the input data
         * @property {string} ObjectRef.type - Type of the record instance that contains the input data
         * @returns {Array|Object|Search|ObjectRef|File|Query} The input data to use in the map/reduce process
         * @since 2015.2
         */

        const getInputData = (inputContext) => {
          let currentScript = runtime.getCurrentScript()
          let customers = currentScript.getParameter('custscript_strg_inv_customers');
          let itemData = {};
          log.debug('customers', customers)

          if (customers) {
            customers = JSON.parse(customers);
            customers = customers.map(customer => Number(customer));
          }

          log.debug('customers', customers)

          let itemSearch = search.create({
            type: "item",
            filters:
              [
                ["custitem_ng_eh_customer_owned","anyof",customers],
                "AND",
                ["custitem_ng_eh_strg_inv_item","noneof","@NONE@"]
              ],
            columns:
              [
                search.createColumn({name: "internalid", label: "Internal ID"}),
                search.createColumn({name: "itemid", label: "Name"}),
                search.createColumn({name: "type", label: "Type"}),
                search.createColumn({name: "displayname", label: "Display Name"}),
                search.createColumn({name: "salesdescription", label: "Description"}),
                search.createColumn({name: "type", label: "Type"}),
                search.createColumn({name: "custitem_ng_eh_length", label: "Length"}),
                search.createColumn({name: "custitem_ng_eh_width", label: "Width"}),
                search.createColumn({name: "custitem_ng_eh_height", label: "Height"}),
                search.createColumn({name: "custitem_ng_eh_weight", label: "Weight"}),
                search.createColumn({name: "custitem_ng_eh_storage_inv_multiplier", label: "Storage Invoice Multiplier"}),
                search.createColumn({name: "custitem_ng_eh_storage_inv_cu_vol", label: "Cubic Volume"}),
                search.createColumn({name: "custitem_ng_eh_strg_inv_item", label: "Storage Invoice Item"}),
                search.createColumn({name: "custitem_ng_eh_customer_owned", label: "Customer Owned"}),
                search.createColumn({
                  name: "custentity_ng_eh_next_strg_inv_date",
                  join: "CUSTITEM_NG_EH_CUSTOMER_OWNED",
                  label: "Next Storage Invoice Date"
                }),
                search.createColumn({
                  name: "custentity_ng_eh_strg_inv_num_months",
                  join: "CUSTITEM_NG_EH_CUSTOMER_OWNED",
                  label: "Storage Invoice # Months"
                }),
                search.createColumn({
                  name: "custentity_ng_eh_strg_inv_cubic_rate",
                  join: "CUSTITEM_NG_EH_CUSTOMER_OWNED",
                  label: "Storage Invoice Cubic Rate"
                })
              ]
          });

          itemSearch.run().each(function(result, i){
            let itemId = result.getValue({name: 'internalid'});
            let itemUrl = '';
            let itemName = result.getValue({name: 'itemid'});
            let itemLength = result.getValue({name: 'custitem_ng_eh_length'});
            let itemWidth = result.getValue({name: 'custitem_ng_eh_width'});
            let itemHeight = result.getValue({name: 'custitem_ng_eh_height'});
            let itemWeight = result.getValue({name: 'custitem_ng_eh_weight'});
            let storageInvMultiplier = result.getValue({name: 'custitem_ng_eh_storage_inv_multiplier'});
            let cubicVolume = result.getValue({name: 'custitem_ng_eh_storage_inv_cu_vol'});
            let itemTypeResult = result.getValue({name: 'type'});
            let storageInvItem = result.getValue({name: 'custitem_ng_eh_strg_inv_item'});
            let customer = result.getValue({name: 'custitem_ng_eh_customer_owned'});
            let customerText = result.getText({name: 'custitem_ng_eh_customer_owned'});
            let nextStorageInvDate = result.getValue({name: 'custentity_ng_eh_next_strg_inv_date', join: 'CUSTITEM_NG_EH_CUSTOMER_OWNED'});
            let storageInvNumOfMonths = result.getValue({name: 'custentity_ng_eh_strg_inv_num_months', join: 'CUSTITEM_NG_EH_CUSTOMER_OWNED'});
            let storageInvRate = result.getValue({name: 'custentity_ng_eh_strg_inv_cubic_rate', join: 'CUSTITEM_NG_EH_CUSTOMER_OWNED'});

            let itemObj = {
              itemId,
              itemName,
              itemLength,
              itemWidth,
              itemHeight,
              itemWeight,
              storageInvMultiplier,
              storageInvItem,
              cubicVolume,
              customer,
              customerText,
              nextStorageInvDate,
              storageInvNumOfMonths,
              storageInvRate
            };

            if (!itemData[customer]) {
              itemData[customer] = [itemObj]
            } else {
              itemData[customer].push(itemObj)
            }
            return true;
          });

          const summaryArray = [];

          Object.keys(itemData).forEach((customerId) => {
            log.debug('itemData', itemData)
            const items = itemData[customerId];
            let invoiceDataObj = {
              customerId: customerId,
              itemData: []
            };
            let customerName = '';
            let totalCubicVolume = 0;
            let totalWeight = 0;
            let totalStorageMultiplier = 0;
            let storageMultiplierCount = 0;


            let nextStorageInvDate = '';
            let storageInvNumOfMonths = '';
            let storageInvRate = 0;

            items.forEach((item) => {
              let itemObj = {}
              let totalCubicFtToInvFrom = 0;
              if (!item.cubicVolume) {
                if (item.itemLength && item.itemWidth && item.itemHeight) {
                  const lengthInches = parseFloat(item.itemLength);
                  const widthInches = parseFloat(item.itemWidth);
                  const heightInches = parseFloat(item.itemHeight);

                  if (!isNaN(lengthInches) && !isNaN(widthInches) && !isNaN(heightInches)) {
                    // Convert cubic inches to cubic feet
                    let cubicFeet = (lengthInches * widthInches * heightInches) / 1728;

                    if (item.storageInvMultiplier) {
                      totalCubicFtToInvFrom += cubicFeet * Number(item.storageInvMultiplier)
                      cubicFeet = cubicFeet * Number(item.storageInvMultiplier)
                    } else {
                      totalCubicFtToInvFrom += cubicFeet
                    }
                  }
                }
              } else {
                totalCubicFtToInvFrom += Number(item.cubicVolume);
              }
              itemObj.itemId = item?.itemId;
              itemObj.customerName = item?.customerText;
              itemObj.storageInvRate = item?.storageInvRate;
              itemObj.storageInvNumOfMonths = item?.storageInvNumOfMonths;
              itemObj.totalCubicFtToInvFrom = totalCubicFtToInvFrom;
              itemObj.storageInvItem = item?.storageInvItem;

              if (item?.nextStorageInvDate) {

                nextStorageInvDate = item.nextStorageInvDate;

                let dateIncrementor = storageInvNumOfMonths ? Number(storageInvNumOfMonths) : 3;

                log.debug('dateIncrementor', dateIncrementor)

                let nextStrgDate = new Date(item.nextStorageInvDate);
                log.debug('nextStrgDate', nextStrgDate)

                nextStrgDate.setMonth(nextStrgDate.getMonth() + dateIncrementor);

                setNextStorageInvDate = `${nextStrgDate.getMonth() + 1}/${nextStrgDate.getDate()}/${nextStrgDate.getFullYear()}`;

                log.debug('nextStorageInvDate', nextStrgDate + ' ' + typeof nextStrgDate)
                log.debug('setNextStorageInvDate', setNextStorageInvDate + ' ' + typeof setNextStorageInvDate)

                itemObj.nextStrgDate = setNextStorageInvDate;
              }

              invoiceDataObj.itemData.push(itemObj);
            });


            summaryArray.push(invoiceDataObj);
          });

          log.debug('summaryArray', summaryArray)

          let consolidatedArray = consolidateItemData(summaryArray)

          log.debug('consolidatedArray', consolidatedArray)

          return consolidatedArray;
        }

        /**
         * Defines the function that is executed when the map entry point is triggered. This entry point is triggered automatically
         * when the associated getInputData stage is complete. This function is applied to each key-value pair in the provided
         * context.
         * @param {Object} mapContext - Data collection containing the key-value pairs to process in the map stage. This parameter
         *     is provided automatically based on the results of the getInputData stage.
         * @param {Iterator} mapContext.errors - Serialized errors that were thrown during previous attempts to execute the map
         *     function on the current key-value pair
         * @param {number} mapContext.executionNo - Number of times the map function has been executed on the current key-value
         *     pair
         * @param {boolean} mapContext.isRestarted - Indicates whether the current invocation of this function is the first
         *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
         * @param {string} mapContext.key - Key to be processed during the map stage
         * @param {string} mapContext.value - Value to be processed during the map stage
         * @since 2015.2
         */

        const map = (mapContext) => {
          log.debug({ title: 'Mapping results from inputData...', details: mapContext })

          let itemProps = JSON.parse(mapContext.value)

          log.debug('ItemProps', itemProps)

          mapContext.write({
            key: itemProps.customerId,
            value: itemProps
          })

        }

        /**
         * Defines the function that is executed when the reduce entry point is triggered. This entry point is triggered
         * automatically when the associated map stage is complete. This function is applied to each group in the provided context.
         * @param {Object} reduceContext - Data collection containing the groups to process in the reduce stage. This parameter is
         *     provided automatically based on the results of the map stage.
         * @param {Iterator} reduceContext.errors - Serialized errors that were thrown during previous attempts to execute the
         *     reduce function on the current group
         * @param {number} reduceContext.executionNo - Number of times the reduce function has been executed on the current group
         * @param {boolean} reduceContext.isRestarted - Indicates whether the current invocation of this function is the first
         *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
         * @param {string} reduceContext.key - Key to be processed during the reduce stage
         * @param {List<String>} reduceContext.values - All values associated with a unique key that was passed to the reduce stage
         *     for processing
         * @since 2015.2
         */
        const reduce = (reduceContext) => {
          log.audit({ title: '⌛ Running reduce...', details: ''})

          let resultedObject = reduceContext.values[0]
          let resultsParsed = JSON.parse(resultedObject)
          let currentScript = runtime.getCurrentScript()
          let memo = currentScript.getParameter('custscript_strg_inv_memo');
          let toBeEmailed = currentScript.getParameter('custscript_strg_inv_emailed');
          let invDate = currentScript.getParameter('custscript_strg_inv_date');
          let statusMessage = '';
          let companyName = '';
          let nextStrgInvDate = '';

          log.debug('resultsParsed', resultsParsed)

          try {
            let customerData = search.lookupFields({
              type: search.Type.CUSTOMER,
              id: resultsParsed.customerId,
              columns: ['custentity_ng_eh_strg_inv_email', 'custentity_ng_eh_strg_inv_client_po', 'companyname']
            })

            companyName = customerData?.companyname;

            let invRec = record.create({
              type: record.Type.INVOICE,
              isDynamic: true
            });
            let itemData = resultsParsed.itemData

            invRec.setValue({
              fieldId: 'entity',
              value: resultsParsed.customerId
            });

            memo && invRec.setValue({
              fieldId: 'memo',
              value: memo
            });

            invDate && invRec.setValue({
              fieldId: 'trandate',
              value: new Date(invDate)
            });

            invRec.setValue({
              fieldId: 'custbody_ng_eh_storage_invoice',
              value: true
            });

            if (toBeEmailed === '1') {
              invRec.setValue({
                fieldId: 'tobeemailed',
                value: true
              });
              customerData?.custentity_ng_eh_strg_inv_email && invRec.setValue({
                fieldId: 'email',
                value: customerData?.custentity_ng_eh_strg_inv_email
              });
            }

            customerData?.custentity_ng_eh_strg_inv_client_po && invRec.setValue({
              fieldId: 'otherrefnum',
              value: customerData?.custentity_ng_eh_strg_inv_client_po
            });

            for (let i = 0; i < itemData.length; i++) {
              invRec.selectNewLine({
                sublistId: 'item',
              });

              invRec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'item',
                value: itemData[i].storageInvItem
              });

              invRec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'quantity',
                value: itemData[i].storageInvNumOfMonths
              });

              invRec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'rate',
                value: Number(itemData[i].totalCubicFtToInvFrom) * Number(itemData[i].storageInvRate)
              });

              invRec.setCurrentSublistValue({
                sublistId: 'item',
                fieldId: 'description',
                value: `${itemData[i].totalCubicFtToInvFrom} cubic feet at $${itemData[i].storageInvRate} per cubic foot = $${(Number(itemData[i].totalCubicFtToInvFrom) * Number(itemData[i].storageInvRate)).toFixed(2)}\n
              ${itemData[i].storageInvNumOfMonths} months at $${(Number(itemData[i].totalCubicFtToInvFrom) * Number(itemData[i].storageInvRate)).toFixed(2)} per month`
              });

              invRec.commitLine({
                sublistId: 'item'
              });

              nextStrgInvDate = itemData[i]?.nextStrgDate
            }

            let invSaved = invRec.save({
              ignoreMandatoryFields: true
            });

            if (invSaved) {
              log.audit('✅ Invoice Saved', invSaved)

              let invUrl = url.resolveRecord({
                recordType: record.Type.INVOICE,
                recordId: invSaved
              });

              let invData = search.lookupFields({
                type: search.Type.INVOICE,
                id: invSaved,
                columns: ['tranid', 'entity']
              });

              try {
                record.submitFields({
                  type: record.Type.CUSTOMER,
                  id: resultsParsed.customerId,
                  values: {
                    custentity_ng_eh_next_strg_inv_date: nextStrgInvDate
                  }
                });

                log.audit('✅ Next Storage Inv Date Submitted')
              } catch (e) {
                log.error('❌ Error Submitting Next Storage Invoice Date', e)
              }

              statusMessage += `Invoice <a href="${invUrl}">${invData?.tranid}</a> successfully created`
            }

          } catch (e) {
            log.error('Error Creating Invoice', e)

            statusMessage += `Error creating invoice for customer ${companyName}.  ${e.message}`
          }

          reduceContext.write({
            key: statusMessage,
            value: resultsParsed.customerId,
          });
        }


        /**
         * Defines the function that is executed when the summarize entry point is triggered. This entry point is triggered
         * automatically when the associated reduce stage is complete. This function is applied to the entire result set.
         * @param {Object} summaryContext - Statistics about the execution of a map/reduce script
         * @param {number} summaryContext.concurrency - Maximum concurrency number when executing parallel tasks for the map/reduce
         *     script
         * @param {Date} summaryContext.dateCreated - The date and time when the map/reduce script began running
         * @param {boolean} summaryContext.isRestarted - Indicates whether the current invocation of this function is the first
         *     invocation (if true, the current invocation is not the first invocation and this function has been restarted)
         * @param {Iterator} summaryContext.output - Serialized keys and values that were saved as output during the reduce stage
         * @param {number} summaryContext.seconds - Total seconds elapsed when running the map/reduce script
         * @param {number} summaryContext.usage - Total number of governance usage units consumed when running the map/reduce
         *     script
         * @param {number} summaryContext.yields - Total number of yields when running the map/reduce script
         * @param {Object} summaryContext.inputSummary - Statistics about the input stage
         * @param {Object} summaryContext.mapSummary - Statistics about the map stage
         * @param {Object} summaryContext.reduceSummary - Statistics about the reduce stage
         * @since 2015.2
         */
        const summarize = (summaryContext) => {
          let userEmail = runtime.getCurrentUser().id;
          let message = `<b>Storage Invoice Generation Summary</b><br>
<ol style=\"list-style-type: decimal;\">`;

          summaryContext.output.iterator().each(function (key, value) {
            log.debug('SUM KEY', key)
            log.debug('SUM VAL', value)
            message += `<li>${key}</li>`;
            return true;
          });

          message += `</ol>`

          try {
            email.send({
              author: userEmail,
              body: message,
              recipients: userEmail,
              subject: "Storage Invoice Generation Summary",
            });
            log.debug("Email Sent");
          } catch (e) {
            log.error("Error sending email", e);
          }
        }

      function consolidateItemData(summaryArray) {
        return summaryArray.map((summary) => {
          const groupedItems = {};

          summary.itemData.forEach((item) => {
            const key = item.storageInvItem;

            if (!groupedItems[key]) {
              // Create initial group entry
              groupedItems[key] = {
                itemId: item.itemId,
                customerName: item.customerName,
                storageInvRate: item.storageInvRate,
                storageInvNumOfMonths: item.storageInvNumOfMonths,
                totalCubicFtToInvFrom: parseFloat(item.totalCubicFtToInvFrom) || 0,
                storageInvItem: item.storageInvItem,
                nextStrgDate: item.nextStrgDate,
              };
            } else {
              // Accumulate cubic footage
              groupedItems[key].totalCubicFtToInvFrom += parseFloat(item.totalCubicFtToInvFrom) || 0;
            }
          });

          return {
            ...summary,
            itemData: Object.values(groupedItems).map((item) => ({
              ...item,
              totalCubicFtToInvFrom: parseFloat(item.totalCubicFtToInvFrom.toFixed(2)),
            })),
          };
        });
      }

        return {getInputData, map, reduce, summarize}

    });
