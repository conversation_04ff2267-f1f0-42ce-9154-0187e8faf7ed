/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * 
 * Example usage of the programmatic tab reordering function
 * This demonstrates how to use the tab reordering functionality
 * implemented in ng_eh_ue_cs_project_form.js
 */

define(["N/ui/serverWidget", "../user-event/ng_eh_ue_cs_project_form"], 
/**
 * @param {serverWidget} serverWidget
 * @param {Object} projectFormModule - The project form module containing tab reordering functions
 */ 
(serverWidget, projectFormModule) => {

  /**
   * Example beforeLoad function demonstrating tab reordering usage
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type
   * @param {Form} scriptContext.form - Current form
   */
  const beforeLoad = (scriptContext) => {
    const { form } = scriptContext;

    // Example 1: Using the standalone programmaticTabReorder function
    // This is the main function that meets the original requirements
    const reorderResult = projectFormModule.programmaticTabReorder(0, form, {
      formFieldsTabId: "custpage_custom_form_fields_tab",
      formHelpTabId: "custpage_custom_form_help_tab",
      formFieldsLabel: "Form Fields",
      formHelpLabel: "Form Help"
    });

    if (reorderResult.success) {
      log.audit({
        title: 'Tab reordering successful',
        details: reorderResult.message
      });
    } else {
      log.error({
        title: 'Tab reordering failed',
        details: reorderResult.message
      });
    }

    // Example 2: Alternative usage with custom tab configuration
    const customReorderResult = projectFormModule.programmaticTabReorder(0, form, {
      formFieldsTabId: "custpage_my_form_fields",
      formHelpTabId: "custpage_my_form_help",
      formFieldsLabel: "Custom Form Fields",
      formHelpLabel: "Custom Form Help"
    });

    // Example 3: Using the simplified configurable tab reorder function
    // (requires pre-existing tab objects - one at a time)
    /*
    const formFieldsTab = form.addTab({
      id: "custpage_custom_form_fields_tab",
      label: "Form Fields",
    });
    const formHelpTab = form.addTab({
      id: "custpage_custom_form_help_tab",
      label: "Form Help",
    });
    const permissionsTab = form.addTab({
      id: "custpage_permissions_tab",
      label: "Permissions",
    });
    const workflowTab = form.addTab({
      id: "custpage_workflow_tab",
      label: "Workflow",
    });

    // Reorder tabs one by one in the desired order: Form Fields, Form Help, Permissions, Workflow
    const formFieldsResult = projectFormModule.configurableTabReorder(formFieldsTab, 0, form);
    const formHelpResult = projectFormModule.configurableTabReorder(formHelpTab, 1, form);
    const permissionsResult = projectFormModule.configurableTabReorder(permissionsTab, 2, form);
    const workflowResult = projectFormModule.configurableTabReorder(workflowTab, 3, form);
    */
  };

  /**
   * Utility function to demonstrate programmatic tab creation and reordering
   * @param {Object} form - NetSuite form object
   * @param {number} targetPosition - Target position (0-based index)
   * @returns {boolean} - Success status
   */
  const createAndReorderTabs = (form, targetPosition = 0) => {
    try {
      // Use the programmatic tab reorder function
      const result = projectFormModule.programmaticTabReorder(targetPosition, form);

      if (result.success) {
        // Add some fields to the tabs to demonstrate functionality
        form.addField({
          id: "custpage_sample_field",
          type: serverWidget.FieldType.TEXT,
          label: "Sample Field",
          container: result.formFieldsTab.id
        });

        form.addField({
          id: "custpage_help_content",
          type: serverWidget.FieldType.INLINEHTML,
          label: "Help Content",
          container: result.formHelpTab.id
        }).defaultValue = "<p>This is help content for the form.</p>";

        return true;
      }

      return false;
    } catch (error) {
      log.error({
        title: 'Error in createAndReorderTabs',
        details: error.toString()
      });
      return false;
    }
  };

  /**
   * Utility function to demonstrate the refactored configurable tab reordering
   * with form.getTabs() functionality
   * @param {Object} form - NetSuite form object
   * @returns {boolean} - Success status
   */
  const createAndReorderTabsConfigurable = (form) => {
    try {
      // Demonstrate form.getTabs() before creating any tabs
      let existingTabsBefore = [];
      try {
        existingTabsBefore = form.getTabs() || [];
        log.audit({
          title: 'Existing tabs before creation',
          details: `Found ${existingTabsBefore.length} tabs: ${existingTabsBefore.join(', ')}`
        });
      } catch (error) {
        log.audit({
          title: '🟨 Could not retrieve existing tabs',
          details: error.toString()
        });
      }

      // Create all tabs first
      const formFieldsTab = form.addTab({
        id: "custpage_custom_form_fields_tab",
        label: "Form Fields",
      });
      const formHelpTab = form.addTab({
        id: "custpage_custom_form_help_tab",
        label: "Form Help",
      });
      const permissionsTab = form.addTab({
        id: "custpage_permissions_tab",
        label: "Permissions",
      });
      const workflowTab = form.addTab({
        id: "custpage_workflow_tab",
        label: "Workflow",
      });

      // Demonstrate form.getTabs() after creating tabs
      let existingTabsAfter = [];
      try {
        existingTabsAfter = form.getTabs() || [];
        log.audit({
          title: 'Existing tabs after creation',
          details: `Found ${existingTabsAfter.length} tabs: ${existingTabsAfter.join(', ')}`
        });
      } catch (error) {
        log.audit({
          title: '🟨 Could not retrieve tabs after creation',
          details: error.toString()
        });
      }

      // Use the simplified configurable tab reorder function (one tab at a time)
      // Order: Form Fields (0), Form Help (1), Permissions (2), Workflow (3)
      const formFieldsReorderSuccess = projectFormModule.configurableTabReorder(formFieldsTab, 0, form);
      const formHelpReorderSuccess = projectFormModule.configurableTabReorder(formHelpTab, 1, form);
      const permissionsReorderSuccess = projectFormModule.configurableTabReorder(permissionsTab, 2, form);
      const workflowReorderSuccess = projectFormModule.configurableTabReorder(workflowTab, 3, form);

      const reorderSuccess = formFieldsReorderSuccess && formHelpReorderSuccess &&
                            permissionsReorderSuccess && workflowReorderSuccess;

      if (reorderSuccess) {
        // Add sample fields to demonstrate the tabs are working
        form.addField({
          id: "custpage_form_field_sample",
          type: serverWidget.FieldType.TEXT,
          label: "Form Field Sample",
          container: formFieldsTab.id
        });

        form.addField({
          id: "custpage_permissions_sample",
          type: serverWidget.FieldType.SELECT,
          label: "Permission Level",
          container: permissionsTab.id
        });

        form.addField({
          id: "custpage_workflow_sample",
          type: serverWidget.FieldType.CHECKBOX,
          label: "Enable Workflow",
          container: workflowTab.id
        });

        log.audit({
          title: 'Configurable tab reordering successful',
          details: 'All tabs created and reordered in the correct sequence'
        });

        return true;
      }

      return false;
    } catch (error) {
      log.error({
        title: 'Error in createAndReorderTabsConfigurable',
        details: error.toString()
      });
      return false;
    }
  };

  /**
   * Utility function to demonstrate the simplified tab reordering with form.getTabs()
   * @param {Object} form - NetSuite form object
   * @returns {boolean} - Success status
   */
  const demonstrateSimpleTabReordering = (form) => {
    try {
      // Create some tabs first
      const tab1 = form.addTab({ id: "custpage_tab1", label: "Tab 1" });
      const tab2 = form.addTab({ id: "custpage_tab2", label: "Tab 2" });
      const tab3 = form.addTab({ id: "custpage_tab3", label: "Tab 3" });

      // Demonstrate form.getTabs() functionality
      const existingTabs = form.getTabs();
      log.audit({
        title: 'Existing tabs before reordering',
        details: `Found ${existingTabs.length} tabs: ${existingTabs.join(', ')}`
      });

      // Use the simplified configurable tab reorder function
      // Position Tab 1 at position 0 (first)
      const tab1Success = projectFormModule.configurableTabReorder(tab1, 0, form);

      // Position Tab 2 at position 1 (second)
      const tab2Success = projectFormModule.configurableTabReorder(tab2, 1, form);

      // Position Tab 3 at position 2 (third)
      const tab3Success = projectFormModule.configurableTabReorder(tab3, 2, form);

      log.audit({
        title: 'Tab reordering results',
        details: {
          tab1Success,
          tab2Success,
          tab3Success,
          allSuccess: tab1Success && tab2Success && tab3Success
        }
      });

      return tab1Success && tab2Success && tab3Success;
    } catch (error) {
      log.error({
        title: 'Error in demonstrateSimpleTabReordering',
        details: error.toString()
      });
      return false;
    }
  };

  return {
    beforeLoad,
    createAndReorderTabs,
    createAndReorderTabsConfigurable,
    demonstrateSimpleTabReordering
  };
});

/**
 * USAGE NOTES:
 *
 * PROGRAMMATIC TAB REORDER FUNCTION:
 * 1. The programmaticTabReorder function accepts an integer parameter starting at 0
 * 2. It moves the "Form Fields" tab to position 0 (first tab)
 * 3. It moves the "Form Help" tab to position 1 (second tab)
 * 4. It preserves the existing order of all other tabs
 * 5. It handles edge cases where specified tabs might not exist
 * 6. It works within the NetSuite SuiteScript 2.1 environment
 * 7. It maintains the integrity of the form structure while reordering tabs
 *
 * FUNCTION SIGNATURE:
 * programmaticTabReorder(targetPosition, form, tabConfig)
 *
 * PARAMETERS:
 * - targetPosition: number (0-based index for tab insertion)
 * - form: NetSuite form object
 * - tabConfig: object with optional configuration properties
 *
 * RETURNS:
 * Object with success status, message, and tab references
 *
 * CONFIGURABLE TAB REORDER FUNCTION (SIMPLIFIED):
 * 1. The configurableTabReorder function takes a single tab object and position
 * 2. It reorders tabs in the specified order: Form Fields, Form Help, Permissions, Workflow
 * 3. Uses form.getTabs() to get existing tab IDs, similar to enhanced project task pattern
 * 4. Positions tabs using form.insertTab() with nexttab parameter
 * 5. Position 0 inserts before first existing tab (like enhanced project task [0])
 * 6. Much simpler than the previous complex array-based approach
 *
 * FUNCTION SIGNATURE:
 * configurableTabReorder(tabObject, position, form)
 *
 * PARAMETERS:
 * - tabObject: NetSuite tab object (from form.addTab())
 * - position: 0-based index for desired position
 * - form: NetSuite form object
 *
 * RETURNS:
 * Boolean indicating success or failure
 *
 * USAGE PATTERN (similar to enhanced project task):
 * const tabs = form.getTabs();
 * form.insertTab({
 *   tab: myTab,
 *   nexttab: tabs[0] // Position at beginning
 * });
 *
 * IMPORTANT NOTES:
 * - form.getTabs() returns tab IDs as strings, not tab objects
 * - Tab objects must be maintained when created using form.addTab()
 * - NetSuite doesn't provide a direct method to get tab objects after creation
 * - This approach follows the same pattern as enhanced project task file
 */
