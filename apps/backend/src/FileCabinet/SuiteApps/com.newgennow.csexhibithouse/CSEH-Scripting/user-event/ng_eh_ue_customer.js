/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NModuleScope SameAccount
 */
define(["N/file", "N/format", "N/query", "N/record", "N/search"], /**
 * @param{file} file
 * @param{format} format
 * @param{query} query
 * @param{record} record
 * @param{search} search
 */ (file, format, query, record, search) => {
  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (scriptContext) => {
    let sc = scriptContext;
    let customerRecord = sc.newRecord;

    // Get Logo image info
    let logo_image = customerRecord.getValue("custentity_ng_eh_customer_logo");
    let parsed_logo_image = parseInt(logo_image);
    let logo_image_url = "";
    // Get cover image fallback info
    let banner_customer_cover_image = customerRecord.getValue(
      "custentity_ng_eh_customer_cover_image"
    );
    let parsed_banner_customer_cover_image = parseInt(
      banner_customer_cover_image
    );
    let customer_cover_image_url = "";

    // Set image logo url on image banner change.
    log.debug({ title: "⚡ Setting logo url...", details: parsed_logo_image });

    if (!isNaN(parsed_logo_image)) {
      logo_image_url = file.load({
        id: parsed_logo_image,
      }).url;

      customerRecord.setValue({
        fieldId: "custentity_ng_eh_customer_logo_url",
        value: logo_image_url,
      });

      log.debug({ title: "✅ Logo url set...", details: logo_image_url });
    }

    // Set item image url on image banner change.
    log.debug({
      title: "⚡ Setting cover url...",
      details: parsed_banner_customer_cover_image,
    });

    if (!isNaN(parsed_banner_customer_cover_image)) {
      customer_cover_image_url = file.load({
        id: parsed_banner_customer_cover_image,
      }).url;

      customerRecord.setValue({
        fieldId: "custentity_ng_eh_customer_cover_url",
        value: customer_cover_image_url,
      });

      log.debug({
        title: "✅ Cover url set...",
        details: customer_cover_image_url,
      });
    }
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    let sc = scriptContext;
    let customerRecord = sc.newRecord;

    // Get Logo image info
    let logo_image = customerRecord.getValue("custentity_ng_eh_customer_logo");
    let parsed_logo_image = parseInt(logo_image);
    let logo_image_url = "";
    // Get cover image fallback info
    let banner_customer_cover_image = customerRecord.getValue(
      "custentity_ng_eh_customer_cover_image"
    );
    let parsed_banner_customer_cover_image = parseInt(
      banner_customer_cover_image
    );
    let customer_cover_image_url = "";

    // Set image logo url on image banner change.
    if (!isNaN(parsed_logo_image)) {
      logo_image_url = file.load({
        id: parsed_logo_image,
      }).url;

      customerRecord.setValue({
        fieldId: "custentity_ng_eh_customer_logo_url",
        value: logo_image_url,
      });
    }

    // Set item image url on image banner change.
    if (!isNaN(parsed_banner_customer_cover_image)) {
      customer_cover_image_url = file.load({
        id: parsed_banner_customer_cover_image,
      }).url;

      customerRecord.setValue({
        fieldId: "custentity_ng_eh_customer_cover_url",
        value: customer_cover_image_url,
      });
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {};

  const getFileInfo = (fileName) => {
    var folderSearch = search
      .create({
        type: "file",
        filters: [["name", "startswith", fileName]],
      })
      .run()
      .getRange(0, 1)[0];
    return folderSearch;
  };

  return { beforeLoad, beforeSubmit, afterSubmit };
});
