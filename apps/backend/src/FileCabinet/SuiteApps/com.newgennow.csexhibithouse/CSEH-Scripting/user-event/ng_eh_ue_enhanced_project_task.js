/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NAmdConfig ./amdUserEventConfig.json
 */
define([
  "N/file",
  "N/task",
  "N/https",
  "N/query",
  "N/record",
  "N/runtime",
  "N/search",
  "N/ui/serverWidget",
  "N/url",
  "crypto-js",
  "lodash",
  "hooks/settings",
  "x-utils",
  "N/email",
  "N/render",
], /**
 * @param{file} file
 * @param{task} task
 * @param{https} https
 * @param{query} query
 * @param{record} record
 * @param{runtime} runtime
 * @param{search} search
 * @param{serverWidget} serverWidget
 * @param{url} url
 * @param{CryptoJS} cryptoJs
 * @param{lodash} lodash
 * @param{Object} settings
 * @param{() => Object} settings.useSettings
 * @param{Object} xUtils
 * @param{email} email
 * @param{render} render
 */ (
  file,
  task,
  https,
  query,
  record,
  runtime,
  search,
  serverWidget,
  url,
  cryptoJs,
  lodash,
  settings,
  xUtils,
  email,
  render
) => {
  /**
   * Defines the function definition that is executed before record is loaded.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @param {Form} scriptContext.form - Current form
   * @param {ServletRequest} scriptContext.request - HTTP request information sent from the browser for a client action only.
   * @since 2015.2
   */
  const beforeLoad = (scriptContext) => {
    let sc = scriptContext;
    let { type } = sc;
    let ehSettings = settings.useSettings();
    const form = sc.form;

    switch (type) {
      case "create":
        formInit(sc);
        renderSubStatus(scriptContext, ehSettings);
        break;
      case "edit":
        formInit(sc);
        renderSubStatus(scriptContext, ehSettings);
        break;
      case "view":
        formInit(sc);
        renderSubStatus(scriptContext, ehSettings);
        displayPrintButton(scriptContext, ehSettings);
        break;
      default:
        break;
    }

    renderListIcon(scriptContext, "beforeLoad");
  };

  /**
   * Defines the function definition that is executed before record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const beforeSubmit = (scriptContext) => {
    let { newRecord, type, oldRecord: prevRecord } = scriptContext;
    let ehSettings = settings.useSettings();
    let subStatusEnabled =
      ehSettings.custrecord_enable_project_task_sub_statu === "T";
    let oldAssigneeLineCount =
      prevRecord &&
      prevRecord.getLineCount({
        sublistId: "assignee",
      });
    let newAssigneeLineCount = newRecord.getLineCount({
      sublistId: "assignee",
    });
    let taskId = newRecord.id;
    log.debug({
      title: "🟡 Assignee Diff:",
      details: {
        oldAssigneeLineCount,
        newAssigneeLineCount,
      },
    });

    let oldPrivacyStatus = prevRecord && prevRecord.getValue({
      fieldId: "custevent_ng_eh_extend_cust_chat_to_web",
    });
    let newPrivacyStatus = newRecord.getValue({
      fieldId: "custevent_ng_eh_extend_cust_chat_to_web",
    });

    // If the chat has been exposed for the first time then fire the task to mark all attachments as public
    if (!oldPrivacyStatus && newPrivacyStatus) {
      let mapReduceTask = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_ng_eh_mr_mark_public_files",
        params: {
          custscript_ng_eh_project_task_id_public: taskId,
        },
      });

      let mapReduceTaskId = mapReduceTask.submit();

      if (mapReduceTaskId)
        log.audit({
          title: "🟢 Map Reduce Task to make chat public:",
          details: "Task ID: " + mapReduceTaskId,
        });
    }

    let changedBy = {
      id: runtime.getCurrentUser().id,
      name: runtime.getCurrentUser().name,
    };
    /* *************************************************************
     * Data points to track
     * - Assignees
     * - Status
     * - Comments (Already taken care of in CS Chat UE)
     ************************************************************* */
    // Check the status diff
    let oldStatus = "";
    let newStatus = "";
    let oldSubStatus = "";
    let newSubStatus = "";

    let diffObject = {
      updateCount: 0,
      prevValues: {
        assignees: [],
        status: "",
      },
      newValues: {
        assignees: [],
        status: "",
      },
    };

    log.debug({
      title: "🚧 Diff Object After Comparison:",
      details: diffObject,
    });

    let assigneeCheckOptions = {
      prevRecord,
      newRecord,
      diffObject,
      oldAssigneeLineCount,
      newAssigneeLineCount,
      changedBy,
    };

    switch (type) {
      case "create":
        // Do nothing like jira
        break;
      case "edit":
        checkAssigneeDiff(assigneeCheckOptions);

        // Listen for changes and notify the watchers on the task
        // Differentiate the status

        if (oldStatus !== newStatus) {
          diffObject.updateCount++;
          diffObject.prevValues.status = oldStatus;
          diffObject.newValues.status = newStatus;
        }
        // Record the old and new status
        newStatus =
          newRecord &&
          newRecord.getText({
            fieldId: "status",
          });

        oldStatus =
          prevRecord &&
          prevRecord.getText({
            fieldId: "status",
          });

        // Record the old and new sub-status if sub-status is enabled
        if (subStatusEnabled) {
          newSubStatus =
            newRecord &&
            newRecord.getText({
              fieldId: "custevent_ng_eh_proj_substatus",
            });

          oldSubStatus =
            prevRecord &&
            prevRecord.getText({
              fieldId: "custevent_ng_eh_proj_substatus",
            });

          if (oldSubStatus !== newSubStatus) {
            diffObject.updateCount++;
            diffObject.prevValues.subStatus = oldSubStatus;
            diffObject.newValues.subStatus = newSubStatus;
          }
        }

        if (diffObject.updateCount !== 0) {
          // Notify the watchers
          notifyWatchers(taskId, "TASK_UPDATE", diffObject);
        }
        break;
      case "xedit":
        checkAssigneeDiff(assigneeCheckOptions);

        // Differentiate the status
        if (oldStatus !== newStatus) {
          diffObject.updateCount++;
          diffObject.prevValues.status = oldStatus;
          diffObject.newValues.status = newStatus;
        }

        newStatus =
          newRecord &&
          newRecord.getText({
            fieldId: "status",
          });

        oldStatus =
          prevRecord &&
          prevRecord.getText({
            fieldId: "status",
          });

        if (subStatusEnabled) {
          newSubStatus =
            newRecord &&
            newRecord.getText({
              fieldId: "custevent_ng_eh_proj_substatus",
            });

          oldSubStatus =
            prevRecord &&
            prevRecord.getText({
              fieldId: "custevent_ng_eh_proj_substatus",
            });

          if (oldSubStatus !== newSubStatus) {
            diffObject.updateCount++;
            diffObject.prevValues.subStatus = oldSubStatus;
            diffObject.newValues.subStatus = newSubStatus;
          }
        }

        if (diffObject.updateCount !== 0) {
          // Notify the watchers
          notifyWatchers(taskId, "TASK_UPDATE", diffObject);
        }
        break;
      case "delete":
        let templateIdSearch = query
          .runSuiteQL({
            query: `SELECT id FROM customrecord_ng_eh_link_proj_task_pdf WHERE custrecord_ng_eh_projtaskpdf_projtask = ${newRecord.id}`,
          })
          .asMappedResults();

        log.debug("templateIdSearch", templateIdSearch);

        if (templateIdSearch.length > 0) {
          record.delete({
            type: "customrecord_ng_eh_link_proj_task_pdf",
            id: templateIdSearch[0].id,
          });
        }
        break;
    }
  };

  /**
   * Defines the function definition that is executed after record is submitted.
   * @param {Object} scriptContext
   * @param {Record} scriptContext.newRecord - New record
   * @param {Record} scriptContext.oldRecord - Old record
   * @param {string} scriptContext.type - Trigger type; use values from the context.UserEventType enum
   * @since 2015.2
   */
  const afterSubmit = (scriptContext) => {
    let { newRecord, oldRecord, type } = scriptContext;
    let taskId = newRecord.id;
    let ehSettings = settings.useSettings();
    renderListIcon(scriptContext, "afterSubmit");
    sendEmailToAssignees(scriptContext, ehSettings);
  };

  /***
   * Notify the watchers of the task
   * @param {number} taskId - The ID of the task
   * @param {"NEW_COMMENT"|"TASK_UPDATE"} type - The type of notification to send
   * @param {Object} diffObject - The object containing the differences between the old and new record
   * */
  const notifyWatchers = (taskId, type, diffObject) => {
    try {
      let mapReduceTask = task.create({
        taskType: task.TaskType.MAP_REDUCE,
        scriptId: "customscript_ng_eh_mr_send_chat_notifica",
        params: {
          custscript_ng_eh_send_notification_task: taskId,
          custscript_ng_notification_type: type,
          custscript_ng_diff_data_from_task: JSON.stringify(diffObject),
        },
      });

      let mapReduceTaskId = mapReduceTask.submit();

      if (mapReduceTaskId)
        log.audit({
          title: "🟢 Email notification task submitted:",
          details: "Task ID: " + mapReduceTaskId,
        });
    } catch (err) {
      log.error({
        title: "🔴 Error submitting notification task:",
        details: err,
      });
    }
  };

  /**
   * Rendering the list icon for the task
   *@param {Object} sc - The script context
   * @param {Record} sc.newRecord - The new record
   * @param {Record} sc.oldRecord - The old record
   * @param {string} sc.type - The type of trigger
   * @param {string} lifeCycle - The life cycle of the trigger
   * */
  const renderListIcon = (sc, lifeCycle) => {
    log.audit({ title: "⚡ Running list icon render...", details: "" });
    let { newRecord: currentRecord, type } = sc;
    var mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    const spaFile = file.load({
      id: "../spas/project-task-chat-thread-spa/index.html",
    });

    log.debug("MAIN URL", mainUrl);

    const fileUrl = spaFile.url;
    let assembledUrl = `https://${mainUrl}${fileUrl}&taskId=${currentRecord.id}&mode=${type}`;
    let messageSearch =
      currentRecord.id &&
      search.create({
        type: "customrecord_ng_eh_cs_chat",
        filters: [
          ["custrecord_ng_eh_cs_chat_proj_task", "anyof", currentRecord.id],
        ],
        columns: [],
      });

    let messagesCount = messageSearch && messageSearch.runPaged().count;

    // Set Icon for link to open chat
    log.audit({ title: "🚧 Setting up chat link render...", details: "" });

    let chatLinkField = currentRecord.getField({
      fieldId: "custevent_ng_eh_cs_chat_link",
    });

    log.audit({
      title: "🟡 Gathered Chat Link Field:",
      details: chatLinkField,
    });

    let chatIconNoMessages = `
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="#94a3b8" style="width: 1rem; height: 1rem;">
		  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.76c0 1.6 1.123 2.994 2.707 3.227 1.087.16 2.185.283 3.293.369V21l4.076-4.076a1.526 1.526 0 011.037-.443 48.282 48.282 0 005.68-.494c1.584-.233 2.707-1.626 2.707-3.228V6.741c0-1.602-1.123-2.995-2.707-3.228A48.394 48.394 0 0012 3c-2.392 0-4.744.175-7.043.513C3.373 3.746 2.25 5.14 2.25 6.741v6.018z" />
		</svg>
		`;
    let chatIconWithMessages = `
      <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="#94a3b8" style="width: 1rem; height: 1rem;">
  			<path fill-rule="evenodd" d="M12 2.25c-2.429 0-4.817.178-7.152.521C2.87 3.061 1.5 4.795 1.5 6.741v6.018c0 1.946 1.37 3.68 3.348 3.97.877.129 1.761.234 2.652.316V21a.75.75 0 001.28.53l4.184-4.183a.39.39 0 01.266-.112c2.006-.05 3.982-.22 5.922-.506 1.978-.29 3.348-2.023 3.348-3.97V6.741c0-1.947-1.37-3.68-3.348-3.97A49.145 49.145 0 0012 2.25zM8.25 8.625a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25zm2.625 1.125a1.125 1.125 0 112.25 0 1.125 1.125 0 01-2.25 0zm4.875-1.125a1.125 1.125 0 100 2.25 1.125 1.125 0 000-2.25z" clip-rule="evenodd" />
			</svg>
		`;

    let chatIcon =
      messagesCount !== 0 ? chatIconWithMessages : chatIconNoMessages;

    let chatIconLink = `
    		<div style="display: flex; align-items: center; justify-content: center;">
          <a href="${assembledUrl}" target="_blank">
            ${chatIcon}
          </a>
				</div>
    `;

    log.audit({
      title: "↙️ Chat Link Field Set:",
      details: chatIconLink,
    });

    switch (type) {
      case "create": // Set the value instead of submitting the field as ID is not available yet
        if (lifeCycle === "afterSubmit") {
          record.submitFields({
            id: currentRecord.id,
            type: record.Type.PROJECT_TASK,
            values: {
              custevent_ng_eh_cs_chat_text_html: chatIconLink,
            },
          });
        }
        break;
      case "xedit":
      case "edit":
        if (lifeCycle === "afterSubmit") {
          record.submitFields({
            id: currentRecord.id,
            type: record.Type.PROJECT_TASK,
            values: {
              custevent_ng_eh_cs_chat_text_html: chatIconLink,
            },
          });
        }
        break;
      case "view":
        if (lifeCycle === "beforeLoad") {
          record.submitFields({
            id: currentRecord.id,
            type: record.Type.PROJECT_TASK,
            values: {
              custevent_ng_eh_cs_chat_text_html: chatIconLink,
            },
          });
        }
        break;
      default:
        log.audit({
          title: "?? Not rendering for context type:",
          details: type,
        });
        break;
    }
  };

  const formInit = (sc) => {
    let form = sc.form;
    let recordMode = sc.type;
    let tabs = form.getTabs();
    var mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    const currentRecord = sc.newRecord;
    const spaFile = file.load({
      id: "../spas/project-task-chat-thread-spa/index.html",
    });

    const fileUrl = spaFile.url;

    log.audit({
      title: "Loading file URL:",
      details: `http://${mainUrl}${fileUrl}`,
    });

    log.audit({ title: "Tabs defined:", details: tabs });

    if (recordMode !== "create") {
      const chatMessages = form.addTab({
        id: "custpage_ng_chat_messages",
        label: "Task Activity",
      });

      form.insertTab({
        tab: chatMessages,
        nexttab: tabs[0], // Set tab position to be the first tab
      });

      let chatField = form.addField({
        id: "custpage_ng_chat_messages",
        type: serverWidget.FieldType.INLINEHTML,
        label: "Chat Messages",
        container: "custpage_ng_chat_messages",
      });

      let assembledUrl = `https://${mainUrl}${fileUrl}&taskId=${currentRecord.id}&mode=${recordMode}&internal=T`;
      chatField.defaultValue = `<iframe src="${assembledUrl}" style="width: 100%; height: 100vh; border: none;"></iframe>`;
    }

    const displayInPortal = currentRecord.getValue({
      fieldId: "custevent_ng_eh_disp_task_in_cust_prtl",
    });

    const externalChatEnabledField = form.getField({
      id: "custevent_ng_eh_extend_cust_chat_to_web",
    });

    const projectId = currentRecord.getValue({
      fieldId: "company",
    });

    if (displayInPortal) {
      externalChatEnabledField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.NORMAL,
      });
    } else {
      externalChatEnabledField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.NODISPLAY,
      });
    }

    const chatWatchers = form.addTab({
      id: "custpage_ng_chat_watchers",
      label: "Chat Watchers",
    });

    form.insertTab({
      tab: chatWatchers,
      nexttab: tabs[1], // Set tab position to be the first tab
    });

    let employees = query
      .runSuiteQL({
        query: `SELECT entityid, id FROM employee`,
      })
      .asMappedResults();

    let entityGroups = query
      .runSuiteQL({
        query: `SELECT groupname as name, id FROM ENTITYGROUP`,
      })
      .asMappedResults();

    let projectContactSearch = search.create({
      type: "contact",
      filters:
      [
         ["job.internalidnumber","equalto", projectId]
      ],
      columns:
      [
         search.createColumn({name: "entityid", label: "Name"}),
         search.createColumn({name: "email", label: "Email"}),
         search.createColumn({name: "phone", label: "Phone"}),
         search.createColumn({name: "altphone", label: "Office Phone"}),
         search.createColumn({name: "fax", label: "Fax"}),
         search.createColumn({name: "company", label: "Company"}),
         search.createColumn({name: "altemail", label: "Alt. Email"}),
         search.createColumn({
            name: "internalid",
            join: "job",
            label: "Internal ID"
         })
      ]
   });

   let projectContacts = []

   xUtils.getAllResultsFor(projectContactSearch, (result) => {
    const resultObj = {
      type: "contact",
      id: result.id,
      entityid: result.getValue({name: "entityid"}),
      email: result.getValue({name: "email"}),
      phone: result.getValue({name: "phone"}),
      altphone: result.getValue({name: "altphone"}),
      fax: result.getValue({name: "fax"}),
      company: result.getValue({name: "company"}),
      altemail: result.getValue({name: "altemail"}),
      uuid: cryptoJs.SHA256(`${result.getValue({name: "entityid"})}_${result.id}`).toString()
    }
    projectContacts.push(resultObj);
   });

    let mixIn = entityGroups
      .map((result) => ({
        ...result,
        type: "group",
        uuid: cryptoJs.SHA256(`${result.name}_${result.id}`).toString(),
      }))
      .concat(
        employees.map((result) => ({
          ...result,
          type: "employee",
          uuid: cryptoJs.SHA256(`${result.entityid}_${result.id}`).toString(),
        }))
      )

      log.debug("🟢 Mix In:", mixIn);

    if (projectContacts.length !== 0) {
      log.debug("🟢 Project Contacts:", projectContacts);
      mixIn = mixIn.concat(projectContacts);
    }

    log.debug("🟢 Mix After Contacts:", mixIn);

    let employeeQueryResults = form.addField({
      id: "custpage_ng_eh_employee_query_results",
      type: serverWidget.FieldType.LONGTEXT,
      label: "Employee Query Results",
    });

    let mergedWatcherResults = [...employees, ...projectContacts];

    currentRecord.setValue({
      fieldId: "custpage_ng_eh_employee_query_results",
      value: JSON.stringify(mergedWatcherResults),
    });

    employeeQueryResults.updateDisplayType({
      displayType: serverWidget.FieldDisplayType.HIDDEN,
    });

    log.debug("🟢 Employee Query Results:", employeeQueryResults);

    let sublist = form.addSublist({
      id: "custpage_cs_watchers_sublist",
      type: serverWidget.SublistType.INLINEEDITOR,
      label: "CS Chat Watchers",
      tab: "custpage_ng_chat_watchers",
    });

    let watcherSelect = sublist.addField({
      id: "custpage_cs_watcher",
      type: serverWidget.FieldType.SELECT,
      label: "Watcher",
    });

    let shoeLaceStyles = form.addField({
      id: "custpage_ng_eh_shoelace_styles",
      type: serverWidget.FieldType.INLINEHTML,
      label: "Shoelace Styles",
    });

    shoeLaceStyles.defaultValue = `<html>
			<head>
    		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.5.2/cdn/themes/light.css" />
      	<script type="module" src="https://cdn.jsdelivr.net/npm/@shoelace-style/shoelace@2.5.2/cdn/shoelace-autoloader.js"></script>
			</head>
		</html>
		`;

    shoeLaceStyles.updateDisplayType({
      displayType: serverWidget.FieldDisplayType.NODISPLAY,
    });

    let watchersRecorded = currentRecord.getValue({
      fieldId: "custevent_ng_eh_cs_chat_watchers",
    });

    let watchers = [];

    sublist.defaultValue = watcherSelect.addSelectOption({
      value: "",
      text: "",
    });

    mixIn.forEach((result) => {
      watcherSelect.addSelectOption({
        value: result.uuid,
        text: result.name || result.entityid,
      });
    });

    log.audit({ title: "🟡 Watchers Recorded:", details: watchersRecorded });

    if (watchersRecorded) {
      watchersRecorded = JSON.parse(watchersRecorded);
      watchers = watchersRecorded?.state.watchers.filter((watcher) => watcher);

      watchers.forEach((watcher, index) => {
        sublist.setSublistValue({
          id: "custpage_cs_watcher",
          value: watcher.uuid,
          line: index,
        });
      });
    }
  };

  /**
   * Will render sub status field if enabled and disable native status field - and vice versa
   * @param {Object} scriptContext - SuiteScript context
   * @param {Object} ehSettings - Eh Settings
   * */
  const renderSubStatus = (scriptContext, ehSettings) => {
    let form = scriptContext.form;
    let subStatusEnabled =
      ehSettings.custrecord_enable_project_task_sub_statu === "T";
    let subStatusField = form.getField({
      id: "custevent_ng_eh_proj_substatus",
    });
    let statusField = form.getField({
      id: "status",
    });

    if (subStatusEnabled) {
      // Show sub status field disable native status field
      statusField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.DISABLED,
      });
      subStatusField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.NORMAL,
      });
    } else {
      // Hide sub status field enable native status field
      statusField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.NORMAL,
      });
      subStatusField.updateDisplayType({
        displayType: serverWidget.FieldDisplayType.NODISPLAY,
      });
    }
  };

  /**
   * Runs the check for the assignee diff - only run on edit - xedit
   * @param {Object} options - Options object
   * @param {Number} options.oldAssigneeLineCount - Old assignee line count
   * @param {Number} options.newAssigneeLineCount - New assignee line count
   * @param {Object} options.newRecord - New record
   * @param {Object} options.prevRecord - Previous record
   * @param {Object} options.changedBy - Changed by
   * @param {Object} options.diffObject - Diff object
   * @returns {void} - Returns the diff object
   * */
  const checkAssigneeDiff = ({
    oldAssigneeLineCount,
    newAssigneeLineCount,
    newRecord,
    prevRecord,
    changedBy,
    diffObject,
  }) => {
    // Check the assignee diff
    if (oldAssigneeLineCount !== 0 || newAssigneeLineCount !== 0) {
      /****************************************************************************************
       * The same assignee count can be assigned but to different people; ensure neither is empty
       * Create an array of objects with the following structure:
       *
       *  {
       *     id: 123,
       *     name: "John Doe",
       *     changedBy: {
       *       id: 123,
       *       name: "John Doe",
       *     },
       *  }
       * **************************************************************************************/

      /* *************************************************************
       * Initialize a new array with the length defined by the line count
       * **************************************************************/
      let oldAssignees = new Array(oldAssigneeLineCount);
      let newAssignees = new Array(newAssigneeLineCount);

      if (oldAssigneeLineCount === newAssigneeLineCount) {
        // Iterate through the assignees and compare only once
        for (let i = 0; i < oldAssigneeLineCount; i++) {
          let oldAssigneeId = prevRecord.getSublistValue({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });
          let oldAssigneeText = prevRecord.getSublistText({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });
          let newAssigneeId = newRecord.getSublistValue({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });
          let newAssigneeText = newRecord.getSublistText({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });

          let oldAssignee = {
            id: oldAssigneeId,
            name: oldAssigneeText,
            changedBy,
          };
          let newAssignee = {
            id: newAssigneeId,
            name: newAssigneeText,
            changedBy,
          };

          oldAssignees[i] = oldAssignee;
          newAssignees[i] = newAssignee;
        }
      } else {
        // Iterate through the assignees and compare each one

        // If there are old assignees
        if (oldAssignees.length !== 0) {
          for (let i = 0; i < oldAssigneeLineCount; i++) {
            let oldAssigneeId = prevRecord.getSublistValue({
              sublistId: "assignee",
              fieldId: "resource",
              line: i,
            });
            let oldAssigneeText = prevRecord.getSublistText({
              sublistId: "assignee",
              fieldId: "resource",
              line: i,
            });

            oldAssignees[i] = {
              id: oldAssigneeId,
              name: oldAssigneeText,
              changedBy,
            };
          }
        }
        // If there are new assignees
        if (newAssignees.length !== 0) {
          for (let i = 0; i < newAssigneeLineCount; i++) {
            let newAssigneeId = newRecord.getSublistValue({
              sublistId: "assignee",
              fieldId: "resource",
              line: i,
            });
            let newAssigneeText = newRecord.getSublistText({
              sublistId: "assignee",
              fieldId: "resource",
              line: i,
            });

            newAssignees[i] = {
              id: newAssigneeId,
              name: newAssigneeText,
              changedBy,
            };
          }
        }
      }

      // Compare the two arrays of assignees
      if (lodash.isEqual(oldAssignees, newAssignees)) {
        log.audit({
          title: "🟢 Old Assignees Equal New Assignees",
          details: "No diff found",
        });
      } else {
        log.audit({
          title: "↙️ Diff found in assignees",
          details: "Adding to diff object...",
        });
        // diffObject.updateCount++;
        diffObject.prevValues.assignees = oldAssignees;
        diffObject.newValues.assignees = newAssignees;

        log.debug({ title: "🟡 Differentiation object:", details: diffObject });
      }
    }
  };

  const sendEmailToAssignees = (scriptContext, ehSettings) => {
    let currRec = scriptContext.newRecord;
    let oldRec = scriptContext.oldRecord;

    if (scriptContext.type === "delete" || scriptContext.type === "xedit") {
      return;
    }

    let emailFeatureEnabled =
      ehSettings.custrecord_ng_eh_prjt_tsk_email_assignee === "T";

    let advancedPdfTemplates = runtime.isFeatureInEffect({
      feature: "advancedprinting",
    });

    let projectsFeature = runtime.isFeatureInEffect({
      feature: "jobs",
    });

    if (advancedPdfTemplates && projectsFeature) {
      if (emailFeatureEnabled) {
        let currRecAssigneeCount = currRec.getLineCount({
          sublistId: "assignee",
        });

        let oldRecAssigneeCount;

        if (oldRec) {
          oldRecAssigneeCount = oldRec.getLineCount({ sublistId: "assignee" });
        }

        let projectTaskName = currRec.getValue({ fieldId: "title" });
        let projectLookup = search.lookupFields({
          type: search.Type.PROJECT_TASK,
          id: currRec.id,
          columns: ["company"],
        }).company;

        let projectName =
          projectLookup.length !== 0 ? projectLookup[0].text : "";
        let projectId = currRec.getValue({ fieldId: "company" });

        let projectTaskUrl = url.resolveRecord({
          recordId: currRec.id,
          recordType: "projecttask",
        });

        let projectUrl = url.resolveRecord({
          recordId: projectId,
          recordType: "job",
        });

        let mainUrl = url.resolveDomain({
          hostType: url.HostType.APPLICATION,
        });

        let assembledProjectTaskUrl = `https://${mainUrl}${projectTaskUrl}`;
        let assembledProjectUrl = `https://${mainUrl}${projectUrl}`;

        log.debug("projectTaskUrl", projectTaskUrl);

        /**
         * * This is the new assignees from the new record state.
         * @type {Array<number>}
         */
        let currRecAssignees = [];
        /**
         * * This is the old assignees from the previous record state.
         * @type {Array<number>}
         */
        let oldRecAssignees = [];

        for (let i = 0; i < currRecAssigneeCount; i++) {
          let assignee = currRec.getSublistValue({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });
          currRecAssignees.push(assignee);
        }

        for (let i = 0; i < oldRecAssigneeCount; i++) {
          let assignee = oldRec.getSublistValue({
            sublistId: "assignee",
            fieldId: "resource",
            line: i,
          });
          oldRecAssignees.push(assignee);
        }

        if (currRecAssignees.length > oldRecAssignees.length) {
          log.debug("Assignee Added");

          let assigneesAdded = currRecAssignees.filter(
            (id) => !oldRecAssignees.includes(id)
          );
          log.debug("assigneesAdded", assigneesAdded);

          let assigneeEmails = assigneesAdded.map((id) => {
            let email = search.lookupFields({
              type: search.Type.EMPLOYEE,
              id,
              columns: ["email"],
            }).email;
            return email;
          });

          log.debug("Assignee Email", assigneeEmails);

          let addedEmailTemplateId =
            ehSettings.custrecord_prj_task_email_temp_add;

          let addedEmailTemplate = record.load({
            type: record.Type.EMAIL_TEMPLATE,
            id: addedEmailTemplateId,
          });

          let emailBody = addedEmailTemplate.getValue({ fieldId: "content" });
          let emailSubject = addedEmailTemplate.getValue({
            fieldId: "subject",
          });

          let renderer = render.create();

          renderer.addRecord({
            templateName: "entity",
            record: record.load({
              type: record.Type.PROJECT_TASK,
              id: currRec.id,
            }),
          });
          renderer.addRecord({
            templateName: "project",
            record: record.load({
              type: record.Type.JOB,
              id: projectId,
            }),
          });

          renderer.templateContent = emailSubject;
          let renderedSubject = renderer.renderAsString();

          renderer.templateContent = emailBody;
          let renderedBody = renderer.renderAsString();

          log.debug("Assignee Added Email Subject", renderedSubject);
          log.debug("Assignee Added Email Body", renderedBody);

          email.send({
            author: ehSettings.custrecord_chat_notification_author,
            body: `<html>
                  <div>
                    <p>${renderedBody}</p>
                  </div>
                </html>`,
            recipients: assigneesAdded,
            subject: renderedSubject,
          });
        }

        if (oldRecAssignees.length > currRecAssignees.length) {
          let assigneesRemoved = oldRecAssignees.filter(
            (id) => !currRecAssignees.includes(id)
          );
          log.debug("assigneesRemoved", assigneesRemoved);

          let assigneeEmails = assigneesRemoved.map((id) => {
            let email = search.lookupFields({
              type: search.Type.EMPLOYEE,
              id,
              columns: ["email"],
            }).email;
            return email;
          });

          log.debug("Assignee Removed Email", assigneeEmails);

          let removedEmailTemplateId =
            ehSettings.custrecord_prj_task_email_temp_remove;

          let addedEmailTemplate = record.load({
            type: record.Type.EMAIL_TEMPLATE,
            id: removedEmailTemplateId,
          });

          let emailBody = addedEmailTemplate.getValue({ fieldId: "content" });
          let emailSubject = addedEmailTemplate.getValue({
            fieldId: "subject",
          });

          let renderer = render.create();

          renderer.addRecord({
            templateName: "entity",
            record: record.load({
              type: record.Type.PROJECT_TASK,
              id: currRec.id,
            }),
          });
          renderer.addRecord({
            templateName: "project",
            record: record.load({
              type: record.Type.JOB,
              id: projectId,
            }),
          });

          renderer.templateContent = emailSubject;
          let renderedSubject = renderer.renderAsString();

          renderer.templateContent = emailBody;
          let renderedBody = renderer.renderAsString();

          log.debug("Assignee Removed Email Subject", renderedSubject);
          log.debug("Assignee Removed Email Body", renderedBody);

          email.send({
            author: ehSettings.custrecord_chat_notification_author,
            body: `<html>
                  <div>
                    <p>${renderedBody}</p>
                  </div>
                </html>`,
            recipients: assigneesRemoved,
            subject: renderedSubject,
          });
        }

        if (
          oldRecAssignees.length !== 0 &&
          currRecAssignees.length !== 0 &&
          oldRecAssignees.length === currRecAssignees.length
        ) {
          let assigneesAdded = currRecAssignees.filter(
            (assignee) => !oldRecAssignees.includes(assignee)
          );
          let assigneesRemoved = oldRecAssignees.filter(
            (assignee) => !currRecAssignees.includes(assignee)
          );

          log.debug("Assignee REPLACED ADD", assigneesAdded);
          log.debug("Assignee REPLACED REMOVE", assigneesRemoved);

          if (assigneesAdded.length !== 0) {
            let addedEmailTemplateId =
              ehSettings.custrecord_prj_task_email_temp_add;

            let addedEmailTemplate = record.load({
              type: record.Type.EMAIL_TEMPLATE,
              id: addedEmailTemplateId,
            });

            let emailBody = addedEmailTemplate.getValue({ fieldId: "content" });
            let emailSubject = addedEmailTemplate.getValue({
              fieldId: "subject",
            });

            let renderer = render.create();

            renderer.addRecord({
              templateName: "entity",
              record: record.load({
                type: record.Type.PROJECT_TASK,
                id: currRec.id,
              }),
            });
            renderer.addRecord({
              templateName: "project",
              record: record.load({
                type: record.Type.JOB,
                id: projectId,
              }),
            });

            renderer.templateContent = emailSubject;
            let renderedSubject = renderer.renderAsString();

            renderer.templateContent = emailBody;
            let renderedBody = renderer.renderAsString();

            log.debug("Assignee Added Email Subject", renderedSubject);
            log.debug("Assignee Added Email Body", renderedBody);

            email.send({
              author: ehSettings.custrecord_chat_notification_author,
              body: `<html>
                  <div>
                    <p>${renderedBody}</p>
                  </div>
                </html>`,
              recipients: assigneesAdded,
              subject: renderedSubject,
            });
          }

          if (assigneesRemoved.length !== 0) {
            let removedEmailTemplateId =
              ehSettings.custrecord_prj_task_email_temp_remove;

            let addedEmailTemplate = record.load({
              type: record.Type.EMAIL_TEMPLATE,
              id: removedEmailTemplateId,
            });

            let emailBody = addedEmailTemplate.getValue({ fieldId: "content" });
            let emailSubject = addedEmailTemplate.getValue({
              fieldId: "subject",
            });

            let renderer = render.create();

            renderer.addRecord({
              templateName: "entity",
              record: record.load({
                type: record.Type.PROJECT_TASK,
                id: currRec.id,
              }),
            });
            renderer.addRecord({
              templateName: "project",
              record: record.load({
                type: record.Type.JOB,
                id: projectId,
              }),
            });

            renderer.templateContent = emailSubject;
            let renderedSubject = renderer.renderAsString();

            renderer.templateContent = emailBody;
            let renderedBody = renderer.renderAsString();

            log.debug("Assignee Removed Email Subject", renderedSubject);
            log.debug("Assignee Removed Email Body", renderedBody);

            email.send({
              author: ehSettings.custrecord_chat_notification_author,
              body: `<html>
                  <div>
                    <p>${renderedBody}</p>
                  </div>
                </html>`,
              recipients: assigneesRemoved,
              subject: renderedSubject,
            });
          }
        }
      }
    } else {
      log.audit(
        "Features Not Enabled: To use feature enable Projects and Advanced PDF/HTML Templates"
      );
    }
  };

  const displayPrintButton = (sc, settings) => {
    let currRec = sc.newRecord;

    let displayButton = false;

    let projectTaskPrintEnabled =
      settings.custrecord_ng_eh_enable_project_task_prt === "T";

    let projectTaskPrintTemplateId =
      settings.custrecord_ng_eh_project_task_print_temp;
    log.debug("projectTaskPrintTemplateId", projectTaskPrintTemplateId);

    let projectTaskPrintRecord;

    log.debug("projectTaskPrintEnabled", projectTaskPrintEnabled);

    let projectTaskPdfSearch = query
      .runSuiteQL({
        query: `SELECT id FROM customrecord_ng_eh_link_proj_task_pdf WHERE custrecord_ng_eh_projtaskpdf_projtask = ${currRec.id}`,
      })
      .asMappedResults();

    log.debug("projectTaskPdfSearch", projectTaskPdfSearch);

    if (projectTaskPdfSearch.length > 0) {
      projectTaskPrintRecord = projectTaskPdfSearch[0].id;
      displayButton = true;
    } else {
      try {
        let newProjectTaskPrintRecord = record.create({
          type: "customrecord_ng_eh_link_proj_task_pdf",
        });

        newProjectTaskPrintRecord.setValue({
          fieldId: "custrecord_ng_eh_projtaskpdf_proj",
          value: currRec.getValue({ fieldId: "company" }),
        });

        newProjectTaskPrintRecord.setValue({
          fieldId: "custrecord_ng_eh_projtaskpdf_projtask",
          value: currRec.id,
        });

        projectTaskPrintRecord = newProjectTaskPrintRecord.save();
        if (projectTaskPrintRecord) {
          displayButton = true;
        }
      } catch (e) {
        log.audit("Project Mismatch", e);
      }
    }

    if (projectTaskPrintEnabled) {
      let form = sc.form;

      form.clientScriptModulePath = "../modules/ng_eh_cm_project_task.js";

      if (displayButton) {
        form.addButton({
          id: "custpage_ng_eh_print_task",
          label: "Print Project Task",
          functionName: `handleProjectTaskPrintClick(${projectTaskPrintRecord}, ${projectTaskPrintTemplateId})`,
        });
      }
    }
  };

  return { beforeLoad, beforeSubmit, afterSubmit };
});
