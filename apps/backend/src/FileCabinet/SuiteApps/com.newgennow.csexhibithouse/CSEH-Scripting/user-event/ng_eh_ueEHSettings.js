/**
 * @NApiVersion 2.1
 * @NScriptType UserEventScript
 * @NModuleScope SameAccount
 */
define([
  "N/file",
  "N/record",
  "N/search",
  "N/runtime",
  "N/ui/serverWidget",
  "N/query",
  "N/url",
  "N/config",
  "../packages/@settings/ng_eh_cm_settings_hooks",
], /**
 * @param{search} search
 * @param{runtime} runtime
 * @param{encode} encode
 * @param{query} query
 * @param{record} record
 * @param{file} file
 * @param{url} url
 * @param{serverWidget} widget
 * @param{config} config
 * @param{Object} settings
 */ function (
  file,
  record,
  search,
  runtime,
  widget,
  query,
  url,
  config,
  settings
) {
  /**
   * Mode of record:
   * create, edit, delete, xedit, copy, view
   * */
  let RECORD_MODE = "";
  let ACCESS_ROLES = ["3", "31"];

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {string} context.type - Trigger type
   * @param {Form} context.form - Current form
   * @Since 2015.2
   */
  function beforeLoad(context) {
    let sc = context;
    RECORD_MODE = sc.type;
    const role = runtime.getCurrentUser().role;
    const settingsRec = context.newRecord;
    const mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    let settingsRecId = settingsRec.id;
    let ehSettingsCount = search
      .create({
        type: "customrecord_ng_eh_settings",
        filters: [],
        columns: [],
      })
      .runPaged().count;

    switch (RECORD_MODE) {
      case "create":
        {
          if (ehSettingsCount && Number(ehSettingsCount) > 0) {
            throw new Error("You cannot create additional settings records");
          }
        }
        break;
      case "edit":
        {
          addToAltRoles(sc);
        }
        break;
      case "view":
        {
          addToAltRoles(sc);

          let projectSummaryPrintEnabled = settingsRec.getValue({ fieldId : "custrecord_ng_eh_print_proj_summary" });

          if (!projectSummaryPrintEnabled) {
            sc.form.getField({ id : "custrecord_ng_eh_proj_summ_prnt_temp" }).updateDisplayType({
              displayType : widget.FieldDisplayType.HIDDEN
            });
            sc.form.getField({ id : "custrecord_ng_eh_proj_summ_advpdf_temp" }).updateDisplayType({
              displayType : widget.FieldDisplayType.HIDDEN
            });
          }
        }
        break;
      case "delete":
        {
        }
        break;
      case "copy":
        {
          throw new Error("You cannot create additional settings records");
        }
        break;
    }

    if (!ACCESS_ROLES.includes(String(role))) {
      throw new Error(
        `You do not have permissions to access this record (role: ${role})`
      );
    }

    // Theme switch html
    let themeSwitcherHtml = `
	 <html>
<style>
:root {
	--width: 60px;
	--height: 30px;
}

body {
	font-family: helvetica, arial, sans-serif;
	box-sizing: border-box;
	margin: 0;
	padding: 0;
	margin: 2em;
}

.theme-toggle {
	position: relative;
	width: var(--width);
	height: var(--height);
}

.theme-toggle input[type="checkbox"] {
	position: absolute;
	top: 0;
	left: 0;
	opacity: 0;
	z-index: 1;
	width: var(--width);
	height: var(--height);
}

.theme-toggle .toggle-body {
	position: absolute;
	top: 0;
	left: 0;

	width: var(--width);
	height: var(--height);
	border: 2px solid #080808;
	border-radius: var(--height);
	transition: all 80ms ease-in-out;
}

.theme-toggle input[type="checkbox"] ~ .toggle-body {
	background: #1d1d1d;
	background-image: url("https://cdn.glitch.com/9be3ef5e-cf68-4fac-9bd5-82714468aed6%2F1e7b6b11b5b398711c60f2b71cdf9b03.gif?v=1599237363310");
	background-size: cover;
}
/*
https://i.giphy.com/media/U3qYN8S0j3bpK/giphy.webp
*/

.theme-toggle input[type="checkbox"]:checked ~ .toggle-body {
	background: #82dfff /*#EDBCD4*/;
	background-image: url("");
	background-size: cover;
	border-color: #89cbf9 /*#CAAAB7*/;
}
/*
https://i.giphy.com/media/tQgB6lM6XCle8/giphy.webp
*/

.theme-toggle input[type="checkbox"] ~ .celestial-body {
	position: absolute;
	width: 24px;
	height: 24px;
	border: 2px solid #fff08e /*white*/;
	border-radius: 100%;
	background: #fff5c4 /*#F0F0FA*/;
	transition: all 80ms ease-in-out;
}

.theme-toggle input[type="checkbox"]:not(:checked) ~ .celestial-body {
	top: 3px;
	left: 3px;
}

.theme-toggle input[type="checkbox"]:checked ~ .celestial-body {
	top: 3px;
	left: calc(100% - 27px);

	background: #ff9900 /*#F2E9BD*/;
	border-color: #ddceb1;
}

.theme-toggle input[type="checkbox"] ~ .celestial-body::after {
	content: " ";
	opacity: 0;
	position: absolute;
	left: -20px;
	transition: left 0.13s ease-in, opacity 0.15s ease-out;
}

.theme-toggle input[type="checkbox"]:checked ~ .celestial-body::after {
	content: "";
	position: absolute;
	bottom: -2px;
	left: -10px;
	display: block;
	opacity: 1;
	width: 20px;
	height: 20px;
	background: url("https://cdn.glitch.com/9be3ef5e-cf68-4fac-9bd5-82714468aed6%2FVector%201%20(1).svg?v=1599249814204");
	background-size: contain;
	background-position: left;
	background-repeat: no-repeat;
}

</style>
<div class="theme-toggle">
  <input id="dark_mode_toggle" type="checkbox" onclick="toggleDarkMode()"/>
  <div class="toggle-body"></div>
  <div class="celestial-body"></div>
</div>
<script type="text/javascript">
    const boolValueSetter = (val) => (val ? 'T' : 'F') 
    const boolValueGetter = (val) => (val === 'T' ? true : false) 
    document.getElementById('custrecord_ng_eh_web_dark_brand_fs').style = 'display: none;'
    var el = document.getElementById('custrecord_ng_eh_web_dark_brand');
    var checkbox = document.getElementById('dark_mode_toggle');
    var lookedupValue = nlapiLookupField('customrecord_ng_eh_settings', 1, 'custrecord_ng_eh_web_dark_brand');

    console.log('Looked up:', lookedupValue)
    console.log('Looked up bool got:', boolValueGetter(lookedupValue))

   var darkModeEnabled = true; // Reverse value for checkbox
    if (el == null) {
        darkModeEnabled = !boolValueGetter(lookedupValue)
        checkbox.disabled = true
    } else {
        darkModeEnabled = !boolValueGetter(el.value)
    }
    checkbox.checked = darkModeEnabled

    function toggleDarkMode() {
        if (checkbox.checked == true) {
            darkModeEnabled = false
            document.getElementById('dark_mode_toggle').setAttribute('checked', darkModeEnabled );
        } else {
            darkModeEnabled = true
            document.getElementById('dark_mode_toggle').setAttribute('checked', darkModeEnabled );
        }
        nlapiSetFieldValue('custrecord_ng_eh_web_dark_brand',  boolValueSetter(darkModeEnabled))
    }
</script>
</html>
	 `;

    settingsRec.setValue(
      "custrecord_ng_eh_web_dark_switch_html",
      themeSwitcherHtml
    );

    // Get Logo image info
    let logo_image = settingsRec.getValue("custrecord_ng_eh_react_logo_image");
    let parsed_logo_image = parseInt(logo_image);
    let logo_image_url = "";
    // Get item fallback image info
    let item_fallback_image = settingsRec.getValue(
      "custrecord_ng_eh_item_fallback_img"
    );
    let parsed_item_fallback_image = parseInt(item_fallback_image);
    let image_fallback_image_url = "";
    // Get cover image fallback info
    let banner_project_fallback_image = settingsRec.getValue(
      "custrecord_ng_eh_react_fallback_cover"
    );
    let parsed_banner_project_fallback_image = parseInt(
      banner_project_fallback_image
    );
    let project_fallback_image_url = "";
    // Login splash image
    let login_splash_img = settingsRec.getValue(
      "custrecord_ng_eh_login_splash_img"
    );
    let parsed_login_splash_img = parseInt(login_splash_img);
    let login_splash_url = "";

    // Set image logo url on image banner change.
    if (!isNaN(parsed_logo_image)) {
      logo_image_url = file.load({
        id: parsed_logo_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_react_logo_image_url",
        value: logo_image_url,
      });
    }

    // Set item image url on image banner change.
    if (!isNaN(parsed_item_fallback_image)) {
      image_fallback_image_url = file.load({
        id: parsed_item_fallback_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_item_fallback_img_url",
        value: image_fallback_image_url,
      });
    }

    // Set cover image url on image banner change.
    if (!isNaN(parsed_banner_project_fallback_image)) {
      project_fallback_image_url = file.load({
        id: parsed_banner_project_fallback_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_fallback_cover_img_url",
        value: project_fallback_image_url,
      });
    }

    // Set login splash image.
    if (!isNaN(parsed_login_splash_img)) {
      login_splash_url = file.load({
        id: parsed_login_splash_img,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_login_splash_url",
        value: login_splash_url,
      });
    }

    function addToAltRoles(sc) {
      let currentSettingsRec = sc.newRecord;
      const adtlRoles =
        currentSettingsRec.getValue({
          fieldId: "custrecord_ng_eh_settings_access",
        }) || [];
      if (adtlRoles.length !== 0) {
        ACCESS_ROLES = ACCESS_ROLES.concat(adtlRoles);
      }
    }
  }

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type
   * @Since 2015.2
   */
  function beforeSubmit(context) {
    let sc = context;
    const settingsRec = context.newRecord;
    const mainUrl = url.resolveDomain({ hostType: url.HostType.APPLICATION });
    RECORD_MODE = sc.type;

    if (RECORD_MODE === "delete") {
      throw new Error("This record cannot be deleted.");
    }

    if (settingsRec.id > 1) {
      throw new Error("Can only have 1 record instance of settings.");
    }

    // Get Logo image info
    let logo_image = settingsRec.getValue("custrecord_ng_eh_react_logo_image");
    let parsed_logo_image = parseInt(logo_image);
    let logo_image_url = "";
    // Get item fallback image info
    let item_fallback_image = settingsRec.getValue(
      "custrecord_ng_eh_item_fallback_img"
    );
    let parsed_item_fallback_image = parseInt(item_fallback_image);
    let image_fallback_image_url = "";
    // Get cover image fallback info
    let banner_project_fallback_image = settingsRec.getValue(
      "custrecord_ng_eh_react_fallback_cover"
    );
    let parsed_banner_project_fallback_image = parseInt(
      banner_project_fallback_image
    );
    let project_fallback_image_url = "";
    // Login splash image
    let login_splash_img = settingsRec.getValue(
      "custrecord_ng_eh_login_splash_img"
    );
    let parsed_login_splash_img = parseInt(login_splash_img);
    let login_splash_url = "";

    // Set image logo url on image banner change.
    if (!isNaN(parsed_logo_image)) {
      logo_image_url = file.load({
        id: parsed_logo_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_react_logo_image_url",
        value: logo_image_url,
      });
    }

    // Set item image url on image banner change.
    if (!isNaN(parsed_item_fallback_image)) {
      image_fallback_image_url = file.load({
        id: parsed_item_fallback_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_item_fallback_img_url",
        value: image_fallback_image_url,
      });
    }

    // Set cover image url on image banner change.
    if (!isNaN(parsed_banner_project_fallback_image)) {
      project_fallback_image_url = file.load({
        id: parsed_banner_project_fallback_image,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_fallback_cover_img_url",
        value: project_fallback_image_url,
      });
    }

    // Set login splash image.
    if (!isNaN(parsed_login_splash_img)) {
      login_splash_url = file.load({
        id: parsed_login_splash_img,
      }).url;

      settingsRec.setValue({
        fieldId: "custrecord_ng_eh_login_splash_url",
        value: login_splash_url,
      });
    }
  }

  /**
   * Function definition to be triggered before record is loaded.
   *
   * @param {Object} context
   * @param {Record} context.newRecord - New record
   * @param {Record} context.oldRecord - Old record
   * @param {string} context.type - Trigger type
   * @Since 2015.2
   */
  function afterSubmit(context) {
    let sc = context;
    let settings = sc.newRecord;
    let isDark = settings.getValue("custrecord_ng_eh_web_dark_brand");
    let primaryColor = settings.getValue("custrecord_ng_eh_primary_web_color");
    let secondaryColor = settings.getValue(
      "custrecord_ng_eh_secondary_web_color"
    );

    const getFileInfo = (fileName) => {
      var folderSearch = search
        .create({
          type: "file",
          filters: [["name", "startswith", fileName]],
        })
        .run()
        .getRange(0, 1)[0];
      return folderSearch;
    };

    const getFolderInfo = (folderName) => {
      var folderSearch = search
        .create({
          type: "folder",
          filters: [["name", "startswith", folderName]],
        })
        .run()
        .getRange(0, 1)[0];
      return folderSearch;
    };

    let item_fallback_file_id = getFileInfo("no-product-image").id;
    let item_fallback_url = "";

    // Set Item Fallback Image
    if (item_fallback_file_id) {
      item_fallback_url = file.load({
        id: item_fallback_file_id,
      }).url;

      settings.setValue({
        fieldId: "custrecord_ng_eh_item_fallback_img_url",
        value: item_fallback_url,
      });
    }

    let folderId = getFolderInfo("LoginPage").id;
    let folderInfo = getFolderInfo("LoginPage");
    log.audit({ title: "Folder ID of LoginPage", details: folderId });
    log.audit({ title: "Folder Info of LoginPage", details: folderInfo });

    let accountDomain = url.resolveDomain({
      hostType: url.HostType.APPLICATION,
    });
    let accountId = runtime.getCurrentUser().accountId;

    let redirectAppSuitelet = url.resolveScript({
      scriptId: "customscript_ng_eh_sl_app_serve",
      deploymentId: "customdeploy_ng_eh_sl_app_serve",
    });

    log.audit({ title: "Account ID scrape from domain", details: accountId });

    log.audit({
      title: "Loading all file urls needed for html file...",
      details: "",
    });

    let mdbJsFile = file.load({
      id: getFileInfo("mdb.min.js").id,
    });

    let mdbCssFile = file.load({
      id: getFileInfo("mdb.min.css").id,
    });
    let mdbFontAwesomeCssFile = file.load({
      id: getFileInfo("fontawesome-all.min.css").id,
    });
    let localCssFile = file.load({
      id: getFileInfo("login.css").id,
    });

    let bodyBgColor = isDark ? "#1C1C1C" : "#f2f5f9";
    let pageContainerColor = shadeColor(primaryColor, isDark ? -40 : 20);

    let newLocalCssFile = file.create({
      name: "login.css",
      fileType: file.Type.STYLESHEET,
      folder: localCssFile.folder,
      isOnline: true,
      contents: `
            html,
            body,
            .intro {
                height: 100%;
                color-scheme: ${isDark ? "dark" : "light"}
            }
            
            @media (min-width: 550px) and (max-width: 750px) {
                html,
                body,
                .intro {
                    height: 550px;
                }
            }
            
            @media (min-width: 800px) and (max-width: 850px) {
                html,
                body,
                .intro {
                    height: 550px;
                }
            }
            
            a.link {
                font-size: .875rem;
                color: #6582B0;
            }
            a.link:hover,
            a.link:active {
                color: #426193;
            }
            
            .card-styling {
                border-radius: 1.5rem!important;
            }
            .login-splash {
                border-radius: 1rem 0 0 1rem;
            }
            .form-login-label {
                letter-spacing: 1px;
            }
            .page-container {
                background-color: ${pageContainerColor};
            }
            .form-container {
              background-color: ${bodyBgColor} !important;
            }
            .login-splash-image {
                height: 100%!important;
            }
            .submit-btn {
               display: inline-block;
               padding: 0.6em 1.7em;
               border: 0.1em solid ${primaryColor} !important;
               margin: 0 0.3em 0.3em 0;
               border-radius: 0.12em;
               box-sizing: border-box;
               text-decoration: none;
               font-family: "Roboto", sans-serif;
               font-weight: 300;
               color: ${primaryColor} !important;
               text-align: center;
               transition: all 0.2s;
            }
            .submit-btn:hover {
               color: ${isDark ? "white" : "black"} !important;
               background-color: ${primaryColor} !important;
            }
            .input-container{
                position:relative;
                margin-bottom:25px;
            }
            .input-container label{
                position:absolute;
                top:14px;
                left:0px;
                font-size:16px;
                pointer-event:none;
                transition: all 0.35s ease-in-out;
                background: transparent;
                cursor: text;
                color: ${secondaryColor};
            }
            .input-container input{
                border:0;
                border-bottom:1px solid #555;
                border-radius: 0;
                background:transparent;
                width:100%;
                padding:12px 0 5px 0;
                font-size:16px;
                color: ${isDark ? "#fff" : "#929292"};
            }
            .input-container input:focus{
                border:none;
                outline:none;
                border-bottom:2px solid ${primaryColor};
            }
            
            .input-container input:focus ~ label,
            .input-container input:valid ~ label{
                top:-12px;
                font-size:12px;
                
            }`,
    });

    let animateCssFile = file.load({
      id: getFileInfo("animate.min.css").id,
    });
    let googleFontCssFile = file.load({
      id: getFileInfo("google-roboto-font.css").id,
    });

    animateCssFile.isOnline = true;
    animateCssFile.save();
    googleFontCssFile.isOnline = true;
    googleFontCssFile.save();
    mdbJsFile.isOnline = true;
    mdbJsFile.save();
    mdbCssFile.isOnline = true;
    mdbCssFile.save();
    mdbFontAwesomeCssFile.isOnline = true;
    mdbFontAwesomeCssFile.save();
    newLocalCssFile.isOnline = true;
    newLocalCssFile.save();

    let newHtmlDoc = file.create({
      name: "generatedLoginPage.html",
      fileType: file.Type.HTMLDOC,
      description: "Auto-Generated: PortalGen login page for customer sign-in.",
      isOnline: true,
      folder: folderId,
      contents: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8" />
    
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'report-sample' 'self'; style-src 'report-sample' 'self'; object-src 'none'; base-uri 'self'; connect-src 'self'; font-src 'self'; frame-src 'self'; img-src 'self'; manifest-src 'self';">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta http-equiv="x-ua-compatible" content="ie=edge" />
    <title>${
      settings.getValue("custrecord_ng_eh_login_screen_text") || "Company Name"
    } | Exhibitor Login</title>
    <!-- Local CSS -->
    <link rel="stylesheet" nonce="0zf145Dg15s0223" href="https://${accountDomain}${
        localCssFile.url
      }"/>
    <!-- Font Awesome -->
    <link rel="stylesheet" nonce="z1Fl1365213001" href="https://${accountDomain}${
        mdbFontAwesomeCssFile.url
      }" />
    <!-- Google Fonts Roboto -->
    <link rel="stylesheet" nonce="a4dn451003yX4" href="https://${accountDomain}${
        googleFontCssFile.url
      }" />
    <!--Animate -->
    <link
            rel="stylesheet"
            nonce="84567311212ash"
            href="https://${accountDomain}${animateCssFile.url}"
    />
    <!-- MDB -->
    <link
            href="https://${accountDomain}${mdbCssFile.url}"
            nonce="da0024i61z6D47ng"
            rel="stylesheet"
    />
    <!-- Custom styles -->
</head>
<body>
<section class="vh-100 page-container">
    <div class="container py-5 h-100 animate__animated animate__fadeInUp">
        <div class="row d-flex justify-content-center align-items-center h-100">
            <div class="col col-xl-10">
                <div class="card card-styling form-container">
                    <div class="row g-0">
                        <div class="col-md-6 col-lg-5 d-none d-md-block">
                            <img
                                    src="${settings.getValue(
                                      "custrecord_ng_eh_login_splash_url"
                                    )}"
                                    alt="login form"
                                    class="img-fluid login-splash login-splash-image"
                            />
                        </div>
                        <div class="col-md-6 col-lg-7 d-flex align-items-center">
                            <div class="card-body p-4 p-lg-5 ${
                              isDark ? "text-white" : "text-black"
                            }">

                                <form method="post"  action="/app/login/secure/privatelogin.nl?compid=${accountId}">
                                
                                    <div class="d-flex align-items-center mb-1 pb-1">
<!--                                        <i class="fas fa-cubes fa-2x me-3" style="color: #ff6219;"></i>-->
                                        <img width="50%" src="${settings.getValue(
                                          "custrecord_ng_eh_react_logo_image_url"
                                        )}" alt="Company Logo"/>
                                    </div>
                                    
                                    <div class="d-flex align-items-center mb-3 pb-1">
<!--                                        <i class="fas fa-cubes fa-2x me-3" style="color: #ff6219;"></i>-->
                                        <span class="h1 fw-bold mb-0">${settings.getValue(
                                          "custrecord_ng_eh_login_screen_text"
                                        )}</span>
                                    </div>

                                    <h5 class="fw-normal mb-3 pb-3 form-login-label">Sign into your account</h5>

                                    <div class="input-container mb-4">
                                        <input name="email" type="email" id="form2Example17" class="form-control-lg" required/>
                                        <label class="form-label" for="form2Example17">Email address</label>
                                    </div>

                                    <div class="input-container mb-4">
                                        <input name="password" type="password" id="form2Example27" class="form-control-lg" required />
                                        <label class="form-label" for="form2Example27">Password</label>
                                    </div>
                                    <input
                                            type="hidden"
                                            name="redirect"
                                            value="https://${accountDomain}${redirectAppSuitelet}"
                                    />
                                    <div class="pt-1 mb-4">
                                        <button class="btn btn-lg btn-block submit-btn" type="submit">Login</button>
                                    </div>

                                    <a class="small text-muted" href="/app/login/preparepwdreset.nl?email=&private=T&c=${accountId}">Forgot password?</a>
<!--                                    <p class="mb-5 pb-lg-2" style="color: #393f81;">Don't have an account? <a href="#!" style="color: #393f81;">Register here</a></p>-->
<!--                                    <a href="#!" class="small text-muted">Terms of use.</a>-->
<!--                                    <a href="#!" class="small text-muted">Privacy policy</a>-->
                                </form>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- MDB -->
<script
        type="text/javascript"
        integrity="sha384-MrcW6ZMFYlzcLA8Nl+NtUVF0sA7MsXsP1UyJoMp4YLEuNSfAP+JcXn/tWtIaxVXM" crossorigin="anonymous"
        src="https://${accountDomain}${mdbJsFile.url}"
></script>
</body>
</html>`,
    });

    let newLoginPage = newHtmlDoc.save();
    log.audit({
      title: "Login page generated",
      details: "File ID: " + newLoginPage,
    });

    newHtmlDoc = file.load({
      id: newLoginPage,
    });

    log.audit({
      title: "Login page saved successfully! ✔",
      details: newHtmlDoc,
    });

    log.audit({ title: "Setting Login Page", details: "" });
    // Set newly generated form as default customer login page.
    let configRecObj = config.load({
      type: config.Type.COMPANY_PREFERENCES,
    });
    let pageSet = configRecObj.getValue({
      fieldId: "CUSTOM_PRIVATE_LOGIN_PAGE",
    });

    log.audit({ title: "Current page set:", details: pageSet });
    // This will protect against an overwrite
    if (!pageSet) {
      configRecObj.setValue({
        fieldId: "CUSTOM_PRIVATE_LOGIN_PAGE",
        value: newLoginPage,
      });
    }

    let newRec = configRecObj.save();
    log.audit({
      title: "Login Page Set!",
      details: newRec ? "Success! 😎" : "Failed!",
    });
  }

  /**
   * @param color Hex value format: #ffffff or ffffff or fff or ffffff00 with opacity or rgb rgba formats
   * @param decimal lighten or darken decimal value, example -0.5 to darken by 50% or 0.5 to lighten by 50%.
   */
  function shadeColor(color, decimal) {
    if (typeof color !== "string" || !color) return color;
    if (typeof decimal !== "number" || !decimal) return color;
    var r, g, b;
    var _format = "";
    color = color.trim();
    if (
      /^rgba?\((\s*[0-9]{1,3}\s*)(,\s*[0-9]{1,3}\s*){2}(,\s*[0-9](\.[0-9]+)?\s*)?\)$/i.test(
        color
      )
    ) {
      color = color
        .replace(/[a-z)(\s]+/gi, "")
        .split(/,/)
        .map(function (c, i) {
          return i === 3 ? c : parseInt(c);
        });
      _format = "rgb";
      if (color.length === 4) {
        _format += "a(VAL," + color[3] + ")";
        color.pop();
      } else _format += "(VAL)";
    } else if (/^#?([a-f0-9]{3}|[a-f0-9]{6}|[a-f0-9]{8})$/i.test(color)) {
      var group = color.length > 5 ? 2 : 1;
      color = color
        .replace("#", "")
        .split(new RegExp("([a-f0-9]{" + group.toString() + "})", "i"))
        .filter(function (c) {
          return c.length === group;
        })
        .map(function (c, i) {
          if (group === 1) c += c;
          return i === 3 ? c : parseInt(c, 16);
        });
      _format = "#VAL";
      if (color.length === 4) {
        _format += color[3];
        color.pop();
      }
    } else return color;
    if (decimal <= 1 && decimal >= -1) decimal *= 100;
    color = color.reduce(function (_c, c) {
      c = Math.round((c * (100 + decimal)) / 100);
      c = c < 255 ? c : 255;
      if (/^#/.test(_format)) {
        c = c.toString(16);
        if (c.length === 1) c = "0" + c;
        return _c + c;
      }
      if (_c === "") return c;
      return _c + "," + c;
    }, "");
    return _format.replace("VAL", color);
  }

  return {
    beforeLoad: beforeLoad,
    beforeSubmit: beforeSubmit,
    afterSubmit: afterSubmit,
  };
});
