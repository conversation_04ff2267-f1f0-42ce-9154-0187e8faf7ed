/**
 * @NAPIVersion 2.1
 * @NScriptType SDFInstallationScript
 */
define(['N/task','N/config','N/encode','N/file','N/runtime','N/search','N/url' ],
    /**
 * @param{task} task
 * @param{config} config
 * @param{encode} encode
 * @param{file} file
 * @param{runtime} runtime
 * @param{search} search
 * @param{url} url
 */
    (task, config, encode, file, runtime, search, url) => {
        /**
         * Defines what is executed when the script is specified by the SDF deployment(in the deploy.xml file of an SDF project).
         * @param {Object} scriptContext
         * @param {fromVersion} scriptContext.fromVersion - The version of the SuiteApp currently installed on the account. Specify null
         *     if this is a new installation.
         * @param {toVersion} scriptContext.toVersion - The version of the SuiteApp that will be installed on the account.
         * @since 2015.2
         */
        const run = (scriptContext) => {

            let sc = scriptContext
            
            let create_env_task = task.create({
                taskType: task.TaskType.SCHEDULED_SCRIPT,
            })

            log.audit({ title: '⚡ Submitting Scheduled Task', details: ''})
            
            create_env_task.scriptId = 'customscript_ng_eh_ss_generate_env'
            create_env_task.deploymentId = 'customdeploy_ng_eh_ss_generate_env'

            create_env_task.submit()

            return;

        }
        return {run}
    });
