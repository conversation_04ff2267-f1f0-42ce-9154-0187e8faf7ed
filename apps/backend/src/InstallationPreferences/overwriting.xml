<!-- This file is used for overwriting or preserving the objects in your SuiteApp when deploying to a NetSuite account. -->
<!-- The configuration below overwrites all objects except for the objects specified with the action tag PRESERVE. -->
<!-- This configuration is only applied when the "Apply Installation Preferences" option is provided through SuiteCloud SDK. Otherwise this file is ignored. -->
<!-- For more information, see https://system.netsuite.com/app/help/helpcenter.nl?fid=section_160751417871.html -->

<preference type="OVERWRITING">
    <scriptdeployments defaultAction="OVERWRITE">
<!--        <scriptdeployment scriptid="customscript_myscript.customdeploy_myscriptdeployment1" action="PRESERVE"/> -->
    </scriptdeployments>
    <customrecordtypes>
        <customrecordtype scriptid="customrecord_ng_eh_settings">
            <instances defaultAction="OVERWRITE">
                <instance scriptid="ng_cs_eh_settings" action="PRESERVE"/>
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_cost_markup">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_show_event">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_event_address">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_item_subcategory">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_location">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_portal_news_item">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_cs_item_stock_avail">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_shipment">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_work_request">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
        <customrecordtype scriptid="customrecord_ng_eh_work_req_list">
            <instances defaultAction="PRESERVE">
            </instances>
        </customrecordtype>
    </customrecordtypes>
</preference>
