<deploy>
    <run>
        <script>
            <path>~/Objects/scripts/customscript_ng_eh_si_clean_up.xml</path>
            <deployment>customdeploy_ng_eh_si_clean_up</deployment>
        </script>
    </run>
    <files>
        <path>~/FileCabinet/*</path>
    </files>
    <objects>
        <!--!!!!DO NOT REARRANGE ORDER OF OBJECT PATHS WILL BREAK DEPLOYMENT!!!!!-->

        <!--Lists-->
        <path>~/Objects/lists/*</path>

        <!--Roles-->
        <path>~/Objects/roles/*</path>

        <!--Links-->
        <path>~/Objects/links/*</path>

        <!-- Sublists -->
        <path>~/Objects/sublists/*</path>

        <!-- Subtabs -->
        <path>~/Objects/subtabs/*</path>

        <!-- Transaction Fields -->
        <path>~/Objects/transactionFields/*</path>

        <!-- Entity Fields -->
        <path>~/Objects/entityFields/*</path>

        <!-- Item Fields -->
        <path>~/Objects/itemFields/*</path>

        <!-- Columns -->
        <path>~/Objects/columns/*</path>

        <!--Records includes one form object EH Settings Ordered in a way to all references to resolve -->
        <path>~/Objects/records/deploying/*</path>
        <path>~/Objects/records/dependencies/transactionFields/*</path>
        <path>~/Objects/records/dependencies/entityFields/main/*</path>
        <path>~/Objects/records/dependencies/itemFields/*</path>
        <path>~/Objects/records/dependencies/columns/*</path>
        <path>~/Objects/records/dependencies/entityFields/sourced/*</path>
        <path>~/Objects/records/dependencies/forms/*</path>
        <!-- ONLY ENABLE ON DEPLOYMENT TO NON EH DEV ACCOUNTS -->
<!--        <path>~/Objects/records/dependencies/activitycode/*</path>-->
        <!-- After everything is set deploy record instances -->


        <!-- Event Fields -->
        <path>~/Objects/events/*</path>
        <path>~/Objects/forms/dependencies/events/*</path>

        <!--Saved Searches-->
        <path>~/Objects/savedSearches/*</path>

        <!--Scripts-->
        <path>~/Objects/records/instances/settings/*</path>
        <path>~/Objects/scripts/*</path>


        <!--Centers-->
        <path>~/Objects/centers/*</path>

        <!-- Implementations -->
        <path>~/Objects/implementations/*</path>

        <!--Workflows-->
        <path>~/Objects/workflows/*</path>

        <!-- Forms and settings record with default instance that references forms -->
        <path>~/Objects/forms/deploying/*</path>

        <!--Integrations-->
        <!--TODO: Remove comment when deploying to another account besides PortalGen Dev-->
        <!-- <path>~/Objects/integrations/*</path> -->

    </objects>
    <run>
        <script>
            <path>~/Objects/scripts/customscript_ng_eh_si_generate_login_pg.xml</path>
            <deployment>customdeploy_ng_eh_si_generate_login_pg</deployment>
        </script>
        <script>
            <path>~/Objects/scripts/customscript_ng_eh_si_generate_env.xml</path>
            <deployment>customdeploy_ng_eh_si_generate_env</deployment>
        </script>
    </run>
    <translationimports>
        <path>~/Translations/*</path>
    </translationimports>
</deploy>
