{"name": "axios", "main": "./dist/axios.js", "version": "1.3.4", "homepage": "https://axios-http.com", "authors": ["<PERSON>"], "description": "Promise based HTTP client for the browser and node.js", "moduleType": ["amd", "globals"], "keywords": ["xhr", "http", "ajax", "promise", "node"], "license": "MIT", "ignore": ["**/.*", "*.iml", "examples", "lib", "node_modules", "sandbox", "test", "CONTRIBUTING.md", "COOKBOOK.md", "Gruntfile.js", "index.js", "karma.conf.js", "package.json", "webpack.*.js"]}